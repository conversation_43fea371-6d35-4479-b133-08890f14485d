# XTask: AI-Powered Social Media Management Platform
## Comprehensive Product Requirements Document v4.0

### 🎯 Executive Summary

XTask is a next-generation AI-powered Twitter/X management platform that combines multiple AI providers with sophisticated agent personas, advanced scheduling, and semantic search capabilities. Built on Next.js 15 + Express.js hybrid architecture, it delivers superior performance, scalability, and user experience.

### 🏗️ Technical Architecture

**Core Stack (2024-2025 Optimized):**
- **Frontend**: Next.js 15.3.3 with React 19.0.0 and TypeScript 5.x
- **Backend**: Express.js 4.19.0 integrated with Next.js
- **Database**: PostgreSQL with pgvector extension
- **Package Manager**: Bun (user preference)
- **Styling**: Tailwind CSS 4.x + shadcn/ui components
- **State Management**: Zustand 5.0.5 + TanStack Query 5.80.7
- **Authentication**: Custom OAuth with database sessions
- **Scheduling**: Node-cron with database queue
- **Media**: UploadThing 6.2.0 integration

### 📋 Core Features & Requirements

#### 1. Authentication System
- **Multi-Provider OAuth**: Google, Twitter/X OAuth 2.0
- **Session Management**: Database-based sessions with JWT tokens
- **Security**: Encrypted API keys, CSRF protection, rate limiting
- **Profile Management**: User preferences, connected accounts

#### 2. AI Agent System (Core Innovation)
- **Persona-Driven Agents**: JSON-based personality definitions
- **Multi-Provider Support**: OpenAI GPT-4, Google Gemini Pro
- **Behavioral Engine**: Context-aware content generation
- **Memory System**: Vector embeddings for agent context
- **Auto-Scheduling**: Intelligent timing based on persona preferences

#### 3. Content Management
- **Rich Text Composer**: Advanced tweet editor with media support
- **AI-Generated Content**: Agent-powered tweet generation
- **Media Upload**: UploadThing integration for images/videos
- **Draft Management**: Save and edit drafts
- **Thread Support**: Multi-tweet thread composition

#### 4. Advanced Scheduling System
- **Node-cron Integration**: Reliable job scheduling with database queue
- **Optimal Timing**: AI-suggested posting times
- **Bulk Scheduling**: Schedule multiple tweets
- **Timezone Support**: User-specific timezone handling

#### 5. Analytics & Insights
- **Performance Tracking**: Tweet engagement metrics
- **Agent Analytics**: Per-agent performance analysis
- **Growth Metrics**: Follower growth, reach analysis
- **Content Insights**: Best performing content types

### 🎨 UI/UX Design System

#### Color Palette
```css
/* Primary Colors */
--primary-500: #8b5cf6  /* Main purple */
--primary-600: #7c3aed
--primary-700: #6d28d9

/* Dark Theme */
--dark-bg: #0a0a0a      /* Main background */
--dark-surface: #1a1a1a  /* Card background */
--dark-border: #2a2a2a   /* Border color */
--dark-text: #ffffff     /* Primary text */
```

#### Component Architecture
- **Base Components**: Button, Input, Card, Modal, Badge, Avatar
- **Layout Components**: Dashboard, Sidebar, Navbar, Breadcrumbs
- **Feature Components**: Agent Cards, Tweet Composer, Schedule Calendar
- **Form Components**: React Hook Form + Zod validation

### 🔧 API Design

#### Authentication Routes
```typescript
POST   /api/auth/login
POST   /api/auth/register  
GET    /api/auth/google
GET    /api/auth/twitter
POST   /api/auth/logout
GET    /api/auth/me
```

#### Agent Management
```typescript
GET    /api/agents              # List user's agents
POST   /api/agents              # Create new agent
GET    /api/agents/:id          # Get agent details
PUT    /api/agents/:id          # Update agent
DELETE /api/agents/:id          # Delete agent
POST   /api/agents/:id/persona  # Upload persona file
POST   /api/agents/:id/generate # Generate content
```

#### Content & Scheduling
```typescript
POST   /api/tweets/compose      # Create tweet
POST   /api/tweets/schedule     # Schedule tweet
GET    /api/tweets/scheduled    # List scheduled
POST   /api/tweets/publish      # Publish immediately
GET    /api/analytics/overview  # Dashboard metrics
```

### 📊 Database Schema

#### Key Models
- **User**: Authentication, preferences, AI configurations
- **Agent**: AI personas with behavioral settings
- **AgentMemory**: Vector embeddings for context
- **TwitterAccount**: Connected social accounts
- **ScheduledTweet**: Queued content with metadata
- **MediaFile**: Uploaded media assets

### 🚀 Implementation Phases

#### Phase 1: Foundation (Week 1-2)
- Project setup with Next.js 15 + Express
- Authentication system implementation
- Database schema with Prisma
- Basic UI components and layout

#### Phase 2: Core Features (Week 3-4)
- Agent CRUD operations
- Persona file upload system
- Tweet composer with media upload
- Basic scheduling functionality

#### Phase 3: AI Integration (Week 5-6)
- OpenAI and Google AI integration
- Agent behavior engine
- Content generation system
- Vector embeddings for memory

#### Phase 4: Advanced Features (Week 7-8)
- Advanced scheduling system
- Analytics dashboard
- Performance optimization
- Production deployment

### 🎯 Success Metrics

#### Technical KPIs
- **Performance**: < 2s page load times
- **Reliability**: 99.9% uptime for scheduling
- **Scalability**: Support 10,000+ scheduled tweets
- **Security**: Zero data breaches, encrypted storage

#### User Experience KPIs
- **Engagement**: > 90% user retention after 30 days
- **Productivity**: 5x faster content creation vs manual
- **Satisfaction**: > 4.5/5 user rating
- **Growth**: 50% month-over-month user growth

### 🔒 Security Requirements

- **Data Encryption**: All API keys encrypted at rest
- **Session Security**: Secure session management with database
- **CORS Protection**: Proper cross-origin request handling
- **Rate Limiting**: API rate limiting to prevent abuse
- **Input Validation**: Comprehensive input sanitization
- **OAuth Security**: Secure token handling and refresh

### 📱 Responsive Design Requirements

- **Mobile First**: Optimized for mobile devices
- **Tablet Support**: Enhanced experience on tablets
- **Desktop**: Full-featured desktop interface
- **PWA Ready**: Progressive Web App capabilities
- **Offline Support**: Basic offline functionality

### 🛠️ Package Dependencies (Latest Stable)

#### Core Dependencies
```json
{
  "next": "15.3.3",
  "react": "^19.0.0",
  "react-dom": "^19.0.0",
  "typescript": "^5.5.0",
  "tailwindcss": "^4.0.0",
  "@tanstack/react-query": "^5.80.7",
  "zustand": "^5.0.5",
  "react-hook-form": "^7.57.0",
  "framer-motion": "^12.18.1",
  "zod": "^3.25.64"
}
```

#### Backend Dependencies
```json
{
  "express": "^4.19.0",
  "prisma": "^5.8.0",
  "@prisma/client": "^5.8.0",
  "bcryptjs": "^2.4.3",
  "jsonwebtoken": "^9.0.2",
  "node-cron": "^3.0.3"
}
```

#### AI & External Services
```json
{
  "openai": "^4.24.0",
  "@google/generative-ai": "^1.4.0",
  "uploadthing": "^6.2.0",
  "twitter-api-v2": "^1.15.0"
}
```

This PRD consolidates all requirements, technical specifications, and implementation guidelines for the XTask platform, ensuring a comprehensive foundation for task generation and development execution.
