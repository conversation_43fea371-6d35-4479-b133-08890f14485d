# XTask Frontend Implementation Guide
## Complete UI/UX Development Roadmap (No Redis)

### 🎯 Frontend Architecture Overview

**Technology Stack:**
- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS + shadcn/ui components
- **State Management**: Zustand + React Query
- **Authentication**: Custom hooks with database sessions
- **Icons**: Heroicons + Lucide React
- **Animations**: Framer Motion
- **Forms**: React Hook Form + Zod validation

### 📋 Complete Implementation Checklist

#### ✅ Phase 1: Project Setup & Core Infrastructure

**1.1 Initial Project Setup**
- [ ] Create Next.js 15 project with TypeScript
- [ ] Install and configure Bun package manager
- [ ] Set up Tailwind CSS with custom configuration
- [ ] Install shadcn/ui and configure components
- [ ] Set up ESLint and Prettier
- [ ] Configure TypeScript with strict mode

**1.2 Essential Dependencies**
```bash
# Core dependencies
bun add next@15 react@18 react-dom@18 typescript
bun add tailwindcss @tailwindcss/forms @tailwindcss/typography
bun add @headlessui/react @heroicons/react lucide-react
bun add framer-motion clsx tailwind-merge
bun add react-hook-form @hookform/resolvers zod
bun add zustand @tanstack/react-query
bun add axios date-fns

# Development dependencies
bun add -D @types/node @types/react @types/react-dom
bun add -D eslint @typescript-eslint/parser @typescript-eslint/eslint-plugin
bun add -D prettier prettier-plugin-tailwindcss
bun add -D autoprefixer postcss
```

**1.3 Configuration Files**
- [ ] Configure `tailwind.config.js` with XTask theme
- [ ] Set up `next.config.js` for production optimization
- [ ] Create `tsconfig.json` with path aliases
- [ ] Set up `.eslintrc.json` and `.prettierrc`

#### ✅ Phase 2: Design System & Base Components

**2.1 Theme Configuration**
- [ ] Define color palette (dark theme with purple accents)
- [ ] Set up typography scale and font families
- [ ] Configure spacing, shadows, and border radius
- [ ] Create animation and transition utilities

**2.2 Base UI Components (src/components/ui/)**
- [ ] **Button Component** - All variants (primary, secondary, outline, ghost)
- [ ] **Input Component** - Text, email, password, textarea with validation
- [ ] **Card Component** - Different elevations and styles
- [ ] **Modal Component** - Overlay, dialog, and drawer variants
- [ ] **Badge Component** - Status indicators and labels
- [ ] **Avatar Component** - User profile images with fallbacks
- [ ] **Dropdown Component** - Menu and select dropdowns
- [ ] **Tabs Component** - Horizontal and vertical tab navigation
- [ ] **Toast Component** - Success, error, warning notifications
- [ ] **Loading Spinner** - Various sizes and styles
- [ ] **Progress Bar** - Linear and circular progress indicators
- [ ] **Theme Toggle** - Dark/light mode switcher

**2.3 Layout Components (src/components/layout/)**
- [ ] **Dashboard Layout** - Main application wrapper
- [ ] **Sidebar Navigation** - Collapsible sidebar with menu items
- [ ] **Top Navbar** - User menu, notifications, search
- [ ] **Breadcrumbs** - Navigation path indicator
- [ ] **Footer** - Links and copyright information

#### ✅ Phase 3: Authentication System

**3.1 Authentication Components (src/components/auth/)**
- [ ] **AuthProvider** - Context provider for authentication state
- [ ] **ProtectedRoute** - Route wrapper for authenticated pages
- [ ] **LoginForm** - Email/password login form
- [ ] **OAuth Buttons** - Google and Twitter login buttons
- [ ] **LogoutButton** - Secure logout functionality

**3.2 Authentication Pages (src/app/(auth)/)**
- [ ] **Login Page** - `/login` with OAuth options
- [ ] **Register Page** - `/register` for email signup
- [ ] **Callback Pages** - OAuth callback handlers
- [ ] **Forgot Password** - Password reset flow

**3.3 Authentication Hooks (src/hooks/)**
- [ ] **useAuth** - Authentication state and methods
- [ ] **useSession** - Session management
- [ ] **useLogout** - Logout functionality

#### ✅ Phase 4: Agent Management System

**4.1 Agent Components (src/components/agents/)**
- [ ] **AgentCard** - Display agent with status and metrics
- [ ] **AgentForm** - Create/edit agent form
- [ ] **AgentList** - Grid/list view of agents
- [ ] **PersonaUploader** - JSON file upload with validation
- [ ] **AgentChat** - Chat interface with agent
- [ ] **AgentSettings** - Configuration panel
- [ ] **AgentAnalytics** - Performance metrics display

**4.2 Agent Pages (src/app/dashboard/agents/)**
- [ ] **Agents List** - `/dashboard/agents` main page
- [ ] **Create Agent** - `/dashboard/agents/create` form
- [ ] **Agent Details** - `/dashboard/agents/[id]` overview
- [ ] **Edit Agent** - `/dashboard/agents/[id]/edit` form
- [ ] **Agent Chat** - `/dashboard/agents/[id]/chat` interface
- [ ] **Agent Analytics** - `/dashboard/agents/[id]/analytics` metrics

**4.3 Agent Features**
- [ ] Agent status management (active/inactive)
- [ ] Persona file upload and validation
- [ ] AI provider configuration per agent
- [ ] Agent performance tracking
- [ ] Bulk agent operations

#### ✅ Phase 5: Tweet Composition System

**5.1 Compose Components (src/components/compose/)**
- [ ] **TweetComposer** - Main composition interface
- [ ] **RichTextEditor** - Text formatting and mentions
- [ ] **MediaUploader** - Image/video upload with preview
- [ ] **EmojiPicker** - Emoji selection interface
- [ ] **CharacterCounter** - Real-time character count
- [ ] **ThreadComposer** - Multi-tweet thread creation
- [ ] **AISuggestions** - AI-powered content suggestions

**5.2 Compose Pages (src/app/dashboard/compose/)**
- [ ] **Compose Interface** - `/dashboard/compose` main page
- [ ] **Draft Management** - `/dashboard/compose/drafts` saved drafts
- [ ] **Templates** - `/dashboard/compose/templates` reusable content

**5.3 Compose Features**
- [ ] Real-time character counting
- [ ] Media upload with drag & drop
- [ ] Hashtag and mention autocomplete
- [ ] Thread composition
- [ ] Draft auto-save
- [ ] AI content generation integration

#### ✅ Phase 6: Scheduling System

**6.1 Schedule Components (src/components/schedule/)**
- [ ] **ScheduleCalendar** - Calendar view with events
- [ ] **ScheduleForm** - Date/time picker form
- [ ] **ScheduledTweetCard** - Individual scheduled tweet
- [ ] **TimePicker** - Time selection with timezone
- [ ] **TimezoneSelector** - Timezone dropdown
- [ ] **BulkScheduler** - Multiple tweet scheduling

**6.2 Schedule Pages (src/app/dashboard/schedule/)**
- [ ] **Schedule Overview** - `/dashboard/schedule` main page
- [ ] **Calendar View** - `/dashboard/schedule/calendar` visual calendar
- [ ] **Queue Management** - `/dashboard/schedule/queue` pending tweets
- [ ] **History** - `/dashboard/schedule/history` published tweets

**6.3 Schedule Features**
- [ ] Visual calendar interface
- [ ] Optimal time suggestions
- [ ] Bulk scheduling operations
- [ ] Schedule conflict detection
- [ ] Timezone-aware scheduling

#### ✅ Phase 7: Analytics Dashboard

**7.1 Analytics Components (src/components/analytics/)**
- [ ] **AnalyticsDashboard** - Main metrics overview
- [ ] **EngagementChart** - Line/bar charts for engagement
- [ ] **GrowthChart** - Follower growth visualization
- [ ] **PerformanceTable** - Tabular data display
- [ ] **MetricCard** - Individual metric display
- [ ] **DateRangePicker** - Time period selection

**7.2 Analytics Pages (src/app/dashboard/analytics/)**
- [ ] **Analytics Overview** - `/dashboard/analytics` main dashboard
- [ ] **Engagement Metrics** - `/dashboard/analytics/engagement` detailed view
- [ ] **Growth Analysis** - `/dashboard/analytics/growth` follower tracking
- [ ] **Custom Reports** - `/dashboard/analytics/reports` report builder

**7.3 Analytics Features**
- [ ] Real-time metrics updates
- [ ] Interactive charts and graphs
- [ ] Export functionality
- [ ] Comparative analysis
- [ ] Performance insights

#### ✅ Phase 8: Settings & Configuration

**8.1 Settings Components (src/components/settings/)**
- [ ] **SettingsForm** - General preferences
- [ ] **ProfileForm** - User profile management
- [ ] **AccountConnections** - Connected social accounts
- [ ] **AIProviderConfig** - AI service configuration
- [ ] **NotificationSettings** - Notification preferences
- [ ] **BillingInfo** - Subscription and billing

**8.2 Settings Pages (src/app/dashboard/settings/)**
- [ ] **General Settings** - `/dashboard/settings` main page
- [ ] **Profile Settings** - `/dashboard/settings/profile` user info
- [ ] **Connected Accounts** - `/dashboard/settings/accounts` social accounts
- [ ] **AI Configuration** - `/dashboard/settings/ai-config` AI providers
- [ ] **Billing** - `/dashboard/settings/billing` subscription

#### ✅ Phase 9: State Management & Data Fetching

**9.1 Zustand Stores (src/stores/)**
- [ ] **authStore** - Authentication state
- [ ] **agentStore** - Agent management state
- [ ] **composeStore** - Tweet composition state
- [ ] **scheduleStore** - Scheduling state
- [ ] **settingsStore** - User preferences

**9.2 React Query Setup (src/lib/)**
- [ ] **queryClient** - React Query configuration
- [ ] **queries** - API query definitions
- [ ] **mutations** - API mutation definitions
- [ ] **queryKeys** - Centralized query key management

**9.3 Custom Hooks (src/hooks/)**
- [ ] **useAgents** - Agent data fetching
- [ ] **useTweets** - Tweet management
- [ ] **useSchedule** - Scheduling operations
- [ ] **useAnalytics** - Analytics data
- [ ] **useSettings** - Settings management

#### ✅ Phase 10: Performance & Optimization

**10.1 Performance Optimizations**
- [ ] Implement React.memo for expensive components
- [ ] Add lazy loading for route components
- [ ] Optimize images with Next.js Image component
- [ ] Implement virtual scrolling for large lists
- [ ] Add skeleton loading states

**10.2 SEO & Accessibility**
- [ ] Add proper meta tags and Open Graph
- [ ] Implement semantic HTML structure
- [ ] Add ARIA labels and roles
- [ ] Ensure keyboard navigation
- [ ] Test with screen readers

**10.3 Error Handling**
- [ ] Global error boundary
- [ ] API error handling
- [ ] Form validation errors
- [ ] Network error recovery
- [ ] User-friendly error messages

### 🎨 Design System Specifications

#### Color Palette
```css
/* Primary Colors */
--primary-50: #faf5ff
--primary-500: #8b5cf6  /* Main purple */
--primary-600: #7c3aed
--primary-700: #6d28d9
--primary-900: #4c1d95

/* Dark Theme */
--dark-bg: #0a0a0a      /* Main background */
--dark-surface: #1a1a1a  /* Card background */
--dark-border: #2a2a2a   /* Border color */
--dark-text: #ffffff     /* Primary text */
--dark-text-muted: #a1a1aa /* Secondary text */
```

#### Typography
```css
/* Font Sizes */
--text-xs: 0.75rem    /* 12px */
--text-sm: 0.875rem   /* 14px */
--text-base: 1rem     /* 16px */
--text-lg: 1.125rem   /* 18px */
--text-xl: 1.25rem    /* 20px */
--text-2xl: 1.5rem    /* 24px */
--text-3xl: 1.875rem  /* 30px */
--text-4xl: 2.25rem   /* 36px */
```

### 🔧 Essential Component Implementations

#### Core Button Component
```typescript
// src/components/ui/button.tsx
import { cn } from '@/lib/utils';
import { cva, type VariantProps } from 'class-variance-authority';
import { forwardRef } from 'react';

const buttonVariants = cva(
  "inline-flex items-center justify-center rounded-lg text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        primary: "bg-gradient-primary text-white hover:opacity-90",
        secondary: "bg-dark-surface text-white border border-dark-border hover:bg-dark-border",
        outline: "border border-primary-500 text-primary-500 hover:bg-primary-500 hover:text-white",
        ghost: "text-gray-400 hover:text-white hover:bg-dark-surface",
        danger: "bg-red-600 text-white hover:bg-red-700",
      },
      size: {
        sm: "h-8 px-3 text-xs",
        md: "h-10 px-4",
        lg: "h-12 px-6 text-base",
        xl: "h-14 px-8 text-lg",
      },
    },
    defaultVariants: {
      variant: "primary",
      size: "md",
    },
  }
);

interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  loading?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, loading, icon, iconPosition = 'left', children, ...props }, ref) => {
    return (
      <button
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        disabled={loading || props.disabled}
        {...props}
      >
        {loading && (
          <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
            <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
          </svg>
        )}
        {icon && iconPosition === 'left' && !loading && <span className="mr-2">{icon}</span>}
        {children}
        {icon && iconPosition === 'right' && !loading && <span className="ml-2">{icon}</span>}
      </button>
    );
  }
);

Button.displayName = "Button";
export { Button, buttonVariants };
```

#### Authentication Provider
```typescript
// src/components/auth/AuthProvider.tsx
'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { User } from '@/types/auth';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (provider: 'google' | 'twitter') => void;
  logout: () => Promise<void>;
  refetch: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchUser = async () => {
    try {
      const response = await fetch('/api/auth/me', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        setUser(data.user);
      } else {
        setUser(null);
      }
    } catch (error) {
      console.error('Auth fetch error:', error);
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  const login = (provider: 'google' | 'twitter') => {
    const authUrls = {
      google: '/api/auth/google',
      twitter: '/api/auth/twitter',
    };

    window.location.href = authUrls[provider];
  };

  const logout = async () => {
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include',
      });
      setUser(null);
      window.location.href = '/';
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  useEffect(() => {
    fetchUser();
  }, []);

  return (
    <AuthContext.Provider value={{ user, loading, login, logout, refetch: fetchUser }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
```

#### Dashboard Layout Component
```typescript
// src/components/layout/DashboardLayout.tsx
'use client';

import { useState } from 'react';
import { Sidebar } from './Sidebar';
import { Navbar } from './Navbar';
import { useAuth } from '@/components/auth/AuthProvider';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { user } = useAuth();

  return (
    <div className="min-h-screen bg-dark-bg">
      <Sidebar open={sidebarOpen} onClose={() => setSidebarOpen(false)} />

      <div className="lg:pl-64">
        <Navbar onMenuClick={() => setSidebarOpen(true)} user={user} />

        <main className="py-6">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
```

#### Agent Card Component
```typescript
// src/components/agents/AgentCard.tsx
import { Agent } from '@/types/agent';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import {
  ChatBubbleLeftIcon,
  PencilIcon,
  TrashIcon,
  PlayIcon,
  PauseIcon
} from '@heroicons/react/24/outline';

interface AgentCardProps {
  agent: Agent;
  onEdit: () => void;
  onDelete: () => void;
  onChat: () => void;
  onToggleStatus: () => void;
}

export function AgentCard({ agent, onEdit, onDelete, onChat, onToggleStatus }: AgentCardProps) {
  return (
    <Card className="p-6 hover:border-primary-500 transition-colors">
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-gradient-primary rounded-lg flex items-center justify-center">
            <span className="text-white font-semibold text-lg">
              {agent.name.charAt(0).toUpperCase()}
            </span>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white">{agent.name}</h3>
            <p className="text-gray-400 text-sm">{agent.description}</p>
          </div>
        </div>

        <Badge variant={agent.isActive ? 'success' : 'secondary'}>
          {agent.isActive ? 'Active' : 'Inactive'}
        </Badge>
      </div>

      <div className="mt-4 grid grid-cols-3 gap-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-white">{agent.tweetsGenerated}</div>
          <div className="text-xs text-gray-400">Tweets</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-white">{agent.engagementRate}%</div>
          <div className="text-xs text-gray-400">Engagement</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-white">{agent.maxDailyTweets}</div>
          <div className="text-xs text-gray-400">Daily Limit</div>
        </div>
      </div>

      <div className="mt-6 flex space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={onChat}
          icon={<ChatBubbleLeftIcon className="w-4 h-4" />}
        >
          Chat
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={onEdit}
          icon={<PencilIcon className="w-4 h-4" />}
        >
          Edit
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggleStatus}
          icon={agent.isActive ? <PauseIcon className="w-4 h-4" /> : <PlayIcon className="w-4 h-4" />}
        >
          {agent.isActive ? 'Pause' : 'Activate'}
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={onDelete}
          icon={<TrashIcon className="w-4 h-4" />}
          className="text-red-400 hover:text-red-300"
        >
          Delete
        </Button>
      </div>
    </Card>
  );
}
```

### 📱 Mobile-First Responsive Design

#### Responsive Breakpoints
```css
/* Tailwind CSS Breakpoints */
sm: 640px   /* Small devices */
md: 768px   /* Medium devices */
lg: 1024px  /* Large devices */
xl: 1280px  /* Extra large devices */
2xl: 1536px /* 2X large devices */
```

#### Mobile Navigation
```typescript
// src/components/layout/MobileNav.tsx
import { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { NavigationItems } from './NavigationItems';

interface MobileNavProps {
  open: boolean;
  onClose: () => void;
}

export function MobileNav({ open, onClose }: MobileNavProps) {
  return (
    <Transition.Root show={open} as={Fragment}>
      <Dialog as="div" className="relative z-50 lg:hidden" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="transition-opacity ease-linear duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="transition-opacity ease-linear duration-300"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black/80" />
        </Transition.Child>

        <div className="fixed inset-0 flex">
          <Transition.Child
            as={Fragment}
            enter="transition ease-in-out duration-300 transform"
            enterFrom="-translate-x-full"
            enterTo="translate-x-0"
            leave="transition ease-in-out duration-300 transform"
            leaveFrom="translate-x-0"
            leaveTo="-translate-x-full"
          >
            <Dialog.Panel className="relative mr-16 flex w-full max-w-xs flex-1">
              <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-dark-surface px-6 pb-2">
                <div className="flex h-16 shrink-0 items-center justify-between">
                  <span className="text-xl font-bold text-white">XTask</span>
                  <button
                    type="button"
                    className="text-gray-400 hover:text-white"
                    onClick={onClose}
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>
                <NavigationItems />
              </div>
            </Dialog.Panel>
          </Transition.Child>
        </div>
      </Dialog>
    </Transition.Root>
  );
}
```

This comprehensive guide provides everything needed to build the complete XTask frontend without Redis dependencies.
