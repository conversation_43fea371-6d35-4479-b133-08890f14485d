# XTask Project Structure
## Complete File Organization & Architecture

### 📁 Root Directory Structure
```
xtask/
├── 📁 src/                          # Main source code
│   ├── 📁 app/                      # Next.js 15 App Router
│   ├── 📁 components/               # React components
│   ├── 📁 lib/                      # Utility libraries
│   ├── 📁 server/                   # Express.js backend
│   ├── 📁 types/                    # TypeScript definitions
│   └── 📁 hooks/                    # Custom React hooks
├── 📁 prisma/                       # Database schema & migrations
├── 📁 public/                       # Static assets
├── 📁 docs/                         # Documentation
├── 📄 server.js                     # Custom server entry point
├── 📄 next.config.js                # Next.js configuration
├── 📄 tailwind.config.js            # Tailwind CSS config
├── 📄 package.json                  # Dependencies & scripts
├── 📄 bun.lockb                     # Bun lock file
├── 📄 .env.example                  # Environment variables template
└── 📄 README.md                     # Project documentation
```

### 🎯 App Router Structure (src/app/)
```
src/app/
├── 📄 layout.tsx                    # Root layout with providers
├── 📄 page.tsx                      # Landing page
├── 📄 globals.css                   # Global styles
├── 📄 loading.tsx                   # Global loading component
├── 📄 error.tsx                     # Global error boundary
├── 📄 not-found.tsx                 # 404 page
│
├── 📁 (auth)/                       # Authentication routes
│   ├── 📄 layout.tsx                # Auth layout
│   ├── 📁 login/
│   │   └── 📄 page.tsx              # Login page
│   ├── 📁 register/
│   │   └── 📄 page.tsx              # Registration page
│   ├── 📁 callback/
│   │   ├── 📁 google/
│   │   │   └── 📄 page.tsx          # Google OAuth callback
│   │   └── 📁 twitter/
│   │       └── 📄 page.tsx          # Twitter OAuth callback
│   └── 📁 forgot-password/
│       └── 📄 page.tsx              # Password reset
│
├── 📁 dashboard/                    # Main application
│   ├── 📄 layout.tsx                # Dashboard layout
│   ├── 📄 page.tsx                  # Dashboard home
│   ├── 📄 loading.tsx               # Dashboard loading
│   │
│   ├── 📁 agents/                   # AI Agent management
│   │   ├── 📄 page.tsx              # Agents list
│   │   ├── 📁 create/
│   │   │   └── 📄 page.tsx          # Create agent
│   │   ├── 📁 [id]/
│   │   │   ├── 📄 page.tsx          # Agent details
│   │   │   ├── 📄 loading.tsx       # Agent loading
│   │   │   ├── 📁 edit/
│   │   │   │   └── 📄 page.tsx      # Edit agent
│   │   │   ├── 📁 chat/
│   │   │   │   └── 📄 page.tsx      # Chat with agent
│   │   │   └── 📁 analytics/
│   │   │       └── 📄 page.tsx      # Agent analytics
│   │   └── 📁 templates/
│   │       └── 📄 page.tsx          # Agent templates
│   │
│   ├── 📁 compose/                  # Tweet composition
│   │   ├── 📄 page.tsx              # Compose interface
│   │   ├── 📁 drafts/
│   │   │   └── 📄 page.tsx          # Draft tweets
│   │   └── 📁 templates/
│   │       └── 📄 page.tsx          # Tweet templates
│   │
│   ├── 📁 schedule/                 # Scheduling system
│   │   ├── 📄 page.tsx              # Schedule overview
│   │   ├── 📁 calendar/
│   │   │   └── 📄 page.tsx          # Calendar view
│   │   ├── 📁 queue/
│   │   │   └── 📄 page.tsx          # Queue management
│   │   └── 📁 history/
│   │       └── 📄 page.tsx          # Published tweets
│   │
│   ├── 📁 analytics/                # Performance analytics
│   │   ├── 📄 page.tsx              # Analytics dashboard
│   │   ├── 📁 engagement/
│   │   │   └── 📄 page.tsx          # Engagement metrics
│   │   ├── 📁 growth/
│   │   │   └── 📄 page.tsx          # Growth analysis
│   │   └── 📁 reports/
│   │       └── 📄 page.tsx          # Custom reports
│   │
│   └── 📁 settings/                 # User settings
│       ├── 📄 page.tsx              # General settings
│       ├── 📁 profile/
│       │   └── 📄 page.tsx          # Profile settings
│       ├── 📁 accounts/
│       │   └── 📄 page.tsx          # Connected accounts
│       ├── 📁 ai-config/
│       │   └── 📄 page.tsx          # AI provider settings
│       └── 📁 billing/
│           └── 📄 page.tsx          # Billing & subscription
│
└── 📁 api/                          # Next.js API routes (delegating to Express)
    ├── 📄 health/
    │   └── 📄 route.ts              # Health check endpoint
    └── 📄 [...slug]/
        └── 📄 route.ts              # Catch-all for Express delegation
```

### 🧩 Components Structure (src/components/)
```
src/components/
├── 📁 ui/                           # Base UI components
│   ├── 📄 button.tsx                # Button component
│   ├── 📄 input.tsx                 # Input component
│   ├── 📄 modal.tsx                 # Modal component
│   ├── 📄 card.tsx                  # Card component
│   ├── 📄 badge.tsx                 # Badge component
│   ├── 📄 avatar.tsx                # Avatar component
│   ├── 📄 dropdown.tsx              # Dropdown component
│   ├── 📄 tabs.tsx                  # Tabs component
│   ├── 📄 toast.tsx                 # Toast notifications
│   ├── 📄 loading-spinner.tsx       # Loading spinner
│   ├── 📄 progress-bar.tsx          # Progress bar
│   └── 📄 theme-toggle.tsx          # Dark/light theme toggle
│
├── 📁 layout/                       # Layout components
│   ├── 📄 dashboard-layout.tsx      # Main dashboard layout
│   ├── 📄 sidebar.tsx               # Navigation sidebar
│   ├── 📄 navbar.tsx                # Top navigation bar
│   ├── 📄 breadcrumbs.tsx           # Breadcrumb navigation
│   └── 📄 footer.tsx                # Footer component
│
├── 📁 auth/                         # Authentication components
│   ├── 📄 login-form.tsx            # Login form
│   ├── 📄 register-form.tsx         # Registration form
│   ├── 📄 oauth-buttons.tsx         # Social login buttons
│   └── 📄 protected-route.tsx       # Route protection
│
├── 📁 agents/                       # Agent-related components
│   ├── 📄 agent-card.tsx            # Agent display card
│   ├── 📄 agent-form.tsx            # Agent creation/edit form
│   ├── 📄 agent-list.tsx            # List of agents
│   ├── 📄 persona-uploader.tsx      # Persona file upload
│   ├── 📄 agent-chat.tsx            # Chat interface
│   ├── 📄 agent-settings.tsx        # Agent configuration
│   └── 📄 agent-analytics.tsx       # Agent performance metrics
│
├── 📁 compose/                      # Tweet composition components
│   ├── 📄 tweet-composer.tsx        # Main composer interface
│   ├── 📄 rich-text-editor.tsx      # Rich text editor
│   ├── 📄 media-uploader.tsx        # Media upload component
│   ├── 📄 emoji-picker.tsx          # Emoji selection
│   ├── 📄 character-counter.tsx     # Character count display
│   ├── 📄 thread-composer.tsx       # Thread creation
│   └── 📄 ai-suggestions.tsx        # AI-powered suggestions
│
├── 📁 schedule/                     # Scheduling components
│   ├── 📄 schedule-calendar.tsx     # Calendar interface
│   ├── 📄 schedule-form.tsx         # Schedule tweet form
│   ├── 📄 scheduled-tweet-card.tsx  # Scheduled tweet display
│   ├── 📄 time-picker.tsx           # Time selection
│   ├── 📄 timezone-selector.tsx     # Timezone selection
│   └── 📄 bulk-scheduler.tsx        # Bulk scheduling interface
│
├── 📁 analytics/                    # Analytics components
│   ├── 📄 analytics-dashboard.tsx   # Main analytics view
│   ├── 📄 engagement-chart.tsx      # Engagement metrics chart
│   ├── 📄 growth-chart.tsx          # Growth metrics chart
│   ├── 📄 performance-table.tsx     # Performance data table
│   ├── 📄 metric-card.tsx           # Individual metric display
│   └── 📄 date-range-picker.tsx     # Date range selection
│
└── 📁 settings/                     # Settings components
    ├── 📄 settings-form.tsx         # General settings form
    ├── 📄 profile-form.tsx          # Profile settings form
    ├── 📄 account-connections.tsx   # Connected accounts
    ├── 📄 ai-provider-config.tsx    # AI provider configuration
    ├── 📄 notification-settings.tsx # Notification preferences
    └── 📄 billing-info.tsx          # Billing information
```

### 🔧 Server Structure (src/server/)
```
src/server/
├── 📄 app.ts                        # Express app configuration
├── 📄 index.ts                      # Server entry point
│
├── 📁 routes/                       # API route handlers
│   ├── 📄 index.ts                  # Route aggregation
│   ├── 📄 auth.ts                   # Authentication routes
│   ├── 📄 agents.ts                 # Agent management routes
│   ├── 📄 tweets.ts                 # Tweet management routes
│   ├── 📄 schedule.ts               # Scheduling routes
│   ├── 📄 analytics.ts              # Analytics routes
│   ├── 📄 media.ts                  # Media upload routes
│   └── 📄 settings.ts               # Settings routes
│
├── 📁 middleware/                   # Express middleware
│   ├── 📄 auth.ts                   # Authentication middleware
│   ├── 📄 cors.ts                   # CORS configuration
│   ├── 📄 rate-limit.ts             # Rate limiting
│   ├── 📄 validation.ts             # Request validation
│   ├── 📄 error-handler.ts          # Error handling
│   └── 📄 logging.ts                # Request logging
│
├── 📁 services/                     # Business logic services
│   ├── 📄 auth-service.ts           # Authentication service
│   ├── 📄 agent-service.ts          # Agent management service
│   ├── 📄 ai-service.ts             # AI provider integration
│   ├── 📄 tweet-service.ts          # Tweet operations service
│   ├── 📄 scheduler-service.ts      # Scheduling service
│   ├── 📄 analytics-service.ts      # Analytics service
│   ├── 📄 media-service.ts          # Media handling service
│   └── 📄 notification-service.ts   # Notification service
│
├── 📁 jobs/                         # Background job processors
│   ├── 📄 tweet-publisher.ts        # Tweet publishing job
│   ├── 📄 agent-auto-tweet.ts       # Automated agent tweets
│   ├── 📄 analytics-collector.ts    # Analytics data collection
│   └── 📄 cleanup-jobs.ts           # Database cleanup jobs
│
└── 📁 config/                       # Configuration files
    ├── 📄 database.ts               # Database configuration
    ├── 📄 session.ts                # Session configuration
    ├── 📄 scheduler.ts              # Node-cron configuration
    └── 📄 ai-providers.ts           # AI provider configurations
```

### 📚 Library Structure (src/lib/)
```
src/lib/
├── 📄 db.ts                         # Prisma client configuration
├── 📄 auth.ts                       # Authentication utilities
├── 📄 session.ts                    # Database session management
├── 📄 scheduler.ts                  # Node-cron job scheduler
├── 📄 ai-providers.ts               # AI provider clients
├── 📄 encryption.ts                 # Data encryption utilities
├── 📄 validation.ts                 # Validation schemas
├── 📄 utils.ts                      # General utilities
├── 📄 constants.ts                  # Application constants
└── 📄 types.ts                      # Shared type definitions
```

### 🎨 Styling & Configuration
```
📄 tailwind.config.js                # Tailwind CSS configuration
📄 next.config.js                    # Next.js configuration
📄 tsconfig.json                     # TypeScript configuration
📄 .eslintrc.json                    # ESLint configuration
📄 .prettierrc                       # Prettier configuration
📄 bun.lockb                         # Bun lock file
```

### 📊 Database & Infrastructure
```
prisma/
├── 📄 schema.prisma                 # Database schema
├── 📁 migrations/                   # Database migrations
└── 📄 seed.ts                       # Database seeding script
```

This structure provides a comprehensive, scalable foundation for the XTask platform with clear separation of concerns, modular architecture, and room for future expansion.
