# XTask Development Setup Guide
## Complete Environment Configuration & Getting Started

### 🚀 Prerequisites

#### System Requirements
- **Node.js**: Version 24+ (latest LTS recommended)
- **Bun**: Latest version for package management
- **PostgreSQL**: Version 14+ with pgvector extension
- **Git**: For version control

#### Development Tools
- **VS Code**: Recommended IDE with extensions:
  - TypeScript and JavaScript Language Features
  - Tailwind CSS IntelliSense
  - Prisma
  - ESLint
  - Prettier
  - GitLens

### 📦 Project Initialization

#### 1. Clone and Setup
```bash
# Clone the repository
git clone <repository-url> xtask
cd xtask

# Install dependencies with Bun
bun install

# Copy environment variables
cp .env.example .env.local
```

#### 2. Environment Configuration
```env
# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/xtask?schema=public"

# Next.js Configuration
NEXTAUTH_SECRET="your-super-secret-jwt-key-here"
NEXTAUTH_URL="http://localhost:3000"
NODE_ENV="development"

# OAuth Providers - Google
GOOGLE_CLIENT_ID="your-google-oauth-client-id"
GOOGLE_CLIENT_SECRET="your-google-oauth-client-secret"
GOOGLE_CALLBACK_URL="http://localhost:3000/api/auth/google/callback"

# OAuth Providers - Twitter (OAuth 2.0)
TWITTER_CLIENT_ID="your-twitter-client-id"
TWITTER_CLIENT_SECRET="your-twitter-client-secret"
TWITTER_CALLBACK_URL="http://www.localhost:3001/api/auth/twitter/callback"

# PKCE Configuration for Twitter OAuth 2.0
TWITTER_CODE_CHALLENGE="y_SfRG4BmOES02uqWeIkIgLQAlTBggyf_G7uKT51ku8"
TWITTER_CODE_VERIFIER="8KxxO-RPl0bLSxX5AWwgdiFbMnry_VOKzFeIlVA7NoA"

# Security Keys
ENCRYPTION_KEY="your-32-byte-encryption-key-for-tokens"

# AI Providers (Optional - users can configure their own)
OPENAI_API_KEY="your-openai-api-key"
GOOGLE_AI_API_KEY="your-google-ai-api-key"

# UploadThing Configuration
UPLOADTHING_SECRET="your-uploadthing-secret"
UPLOADTHING_APP_ID="your-uploadthing-app-id"

# Session Configuration
SESSION_SECRET="your-express-session-secret"

# Application Settings
APP_NAME="XTask"
APP_URL="http://localhost:3000"
```

#### 3. Database Setup
```bash
# Start PostgreSQL and enable pgvector extension
sudo -u postgres psql
CREATE DATABASE xtask;
CREATE EXTENSION vector;

# Generate Prisma client
bun run db:generate

# Push database schema
bun run db:push

# Optional: Seed database with sample data
bun run db:seed
```

#### 4. Start Development
```bash
# Start the development server
bun run dev

# The application will be available at:
# http://localhost:3000
```

### 🛠️ Development Scripts

#### Package.json Scripts
```json
{
  "scripts": {
    "dev": "node server.js",
    "build": "next build",
    "start": "NODE_ENV=production node server.js",
    "lint": "next lint",
    "lint:fix": "next lint --fix",
    "type-check": "tsc --noEmit",
    "format": "prettier --write .",
    "format:check": "prettier --check .",
    
    "db:generate": "prisma generate",
    "db:push": "prisma db push",
    "db:migrate": "prisma migrate dev",
    "db:migrate:deploy": "prisma migrate deploy",
    "db:seed": "tsx prisma/seed.ts",
    "db:studio": "prisma studio",
    "db:reset": "prisma migrate reset",
    
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:e2e": "playwright test",
    
    "analyze": "cross-env ANALYZE=true next build",
    "clean": "rimraf .next out dist build"
  }
}
```

### ⚙️ Configuration Files

#### Next.js Configuration (next.config.js)
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    serverComponentsExternalPackages: ['@prisma/client'],
  },
  images: {
    domains: ['uploadthing.com', 'utfs.io'],
    formats: ['image/webp', 'image/avif'],
  },
  webpack: (config) => {
    config.externals.push('@node-rs/argon2', '@node-rs/bcrypt');
    return config;
  },
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          { key: 'Access-Control-Allow-Origin', value: '*' },
          { key: 'Access-Control-Allow-Methods', value: 'GET,POST,PUT,DELETE,OPTIONS' },
          { key: 'Access-Control-Allow-Headers', value: 'Content-Type, Authorization' },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
```

#### Tailwind Configuration (tailwind.config.js)
```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#faf5ff',
          100: '#f3e8ff',
          200: '#e9d5ff',
          300: '#d8b4fe',
          400: '#c084fc',
          500: '#8b5cf6',
          600: '#7c3aed',
          700: '#6d28d9',
          800: '#5b21b6',
          900: '#4c1d95',
        },
        dark: {
          bg: '#0a0a0a',
          surface: '#1a1a1a',
          border: '#2a2a2a',
        },
      },
      backgroundImage: {
        'gradient-primary': 'linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%)',
        'gradient-dark': 'linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%)',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'pulse-slow': 'pulse 3s infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
    require('tailwindcss-animate'),
  ],
};
```

#### TypeScript Configuration (tsconfig.json)
```json
{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/components/*"],
      "@/lib/*": ["./src/lib/*"],
      "@/types/*": ["./src/types/*"],
      "@/hooks/*": ["./src/hooks/*"],
      "@/server/*": ["./src/server/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
```

### 🧪 Testing Configuration

#### Jest Configuration (jest.config.js)
```javascript
const nextJest = require('next/jest');

const createJestConfig = nextJest({
  dir: './',
});

const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testEnvironment: 'jest-environment-jsdom',
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/types/**/*',
  ],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70,
    },
  },
};

module.exports = createJestConfig(customJestConfig);
```

### 🚀 Development Workflow

#### 1. Start Development Server
```bash
# Start all services
bun run dev

# The application will be available at:
# http://localhost:3000
```

#### 2. Database Management
```bash
# View database in Prisma Studio
bun run db:studio

# Reset database (development only)
bun run db:reset

# Create new migration
bun run db:migrate
```

#### 3. Code Quality
```bash
# Run linting
bun run lint

# Fix linting issues
bun run lint:fix

# Format code
bun run format

# Type checking
bun run type-check
```

#### 4. Testing
```bash
# Run unit tests
bun run test

# Run tests in watch mode
bun run test:watch

# Run E2E tests
bun run test:e2e

# Generate coverage report
bun run test:coverage
```

### 🔧 IDE Configuration

#### VS Code Settings (.vscode/settings.json)
```json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "files.associations": {
    "*.css": "tailwindcss"
  },
  "tailwindCSS.includeLanguages": {
    "typescript": "javascript",
    "typescriptreact": "javascript"
  }
}
```

### 📚 Additional Resources

#### Documentation Links
- [Next.js 15 Documentation](https://nextjs.org/docs)
- [Prisma Documentation](https://www.prisma.io/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [TypeScript Documentation](https://www.typescriptlang.org/docs)

#### Useful Commands
```bash
# Check Node.js version
node --version

# Check Bun version
bun --version

# Check PostgreSQL version
psql --version

# Check Redis version
redis-server --version
```

This setup guide provides everything needed to get the XTask development environment running smoothly.
