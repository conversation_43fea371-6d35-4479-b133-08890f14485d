# XTask Frontend Development Startup Prompt
## Project Initialization Guide

### 🚀 PROJECT INITIALIZATION PROMPT

```
You are building XTask, an AI-powered social media management platform frontend. Initialize the project with the following specifications:

PROJECT: XTask Frontend
DESCRIPTION: AI-powered social media management platform with agent personas, tweet scheduling, and analytics

TECHNOLOGY STACK:
- Next.js 15 (App Router)
- TypeScript (strict mode)
- Tailwind CSS + shadcn/ui
- Bun package manager

THEME:
- Dark background: #0a0a0a
- Purple accents: #8b5cf6, #a855f7
- Mobile-first responsive design

PROJECT STRUCTURE TO CREATE:
```
xtask-frontend/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── (auth)/            # Auth pages (login, register)
│   │   ├── dashboard/         # Main app pages
│   │   │   ├── agents/        # Agent management
│   │   │   ├── compose/       # Tweet composer
│   │   │   ├── schedule/      # Scheduling
│   │   │   ├── analytics/     # Analytics
│   │   │   └── settings/      # Settings
│   │   ├── api/               # API routes
│   │   ├── globals.css        # Global styles
│   │   ├── layout.tsx         # Root layout
│   │   └── page.tsx           # Landing page
│   ├── components/            # React components
│   │   ├── ui/                # Base components (Button, Input, Card)
│   │   ├── auth/              # Auth components
│   │   ├── agents/            # Agent components
│   │   ├── compose/           # Composer components
│   │   ├── schedule/          # Schedule components
│   │   ├── analytics/         # Analytics components
│   │   └── layout/            # Layout components
│   ├── hooks/                 # Custom React hooks
│   ├── lib/                   # Utilities and configs
│   ├── stores/                # Zustand stores
│   └── types/                 # TypeScript definitions
├── public/                    # Static assets
├── package.json
├── tailwind.config.js
├── next.config.js
├── tsconfig.json
└── components.json            # shadcn/ui config
```

INITIAL DEPENDENCIES TO INSTALL:
```bash
# Core
bun add next@15 react@18 react-dom@18 typescript
bun add tailwindcss @tailwindcss/forms @tailwindcss/typography
bun add @headlessui/react @heroicons/react lucide-react
bun add clsx tailwind-merge class-variance-authority

# Development
bun add -D @types/node @types/react @types/react-dom
bun add -D eslint @typescript-eslint/parser @typescript-eslint/eslint-plugin
bun add -D prettier prettier-plugin-tailwindcss
bun add -D autoprefixer postcss
```

FIRST TASKS:
1. Create the project structure above
2. Set up Tailwind with XTask dark theme colors
3. Configure shadcn/ui components
4. Create basic layout with sidebar navigation
5. Build core UI components (Button, Input, Card)

Start by creating the project structure and initial configuration files.
```

### 🎯 SPECIFIC FEATURE DEVELOPMENT PROMPTS

#### When Starting Project Setup:
```
Set up the XTask frontend project with the following specifications:

INITIAL SETUP:
1. Create Next.js 15 project with TypeScript and App Router
2. Install dependencies: Tailwind CSS, shadcn/ui, Framer Motion, React Hook Form, Zod, Zustand, React Query
3. Configure Tailwind with XTask dark theme colors
4. Set up shadcn/ui components
5. Create basic project structure following the knowledge base

CONFIGURATION FILES NEEDED:
- tailwind.config.js (with XTask theme colors)
- next.config.js (with optimization settings)
- tsconfig.json (with path aliases)
- components.json (shadcn/ui config)

FOLDER STRUCTURE:
Create the complete src/ folder structure as specified in the knowledge base, including app/, components/, hooks/, lib/, stores/, and types/ directories.

FIRST COMPONENTS:
After setup, create these base UI components:
1. Button (with all variants)
2. Input (with validation states)
3. Card (with hover effects)
4. Modal (with animations)

Please start with the project initialization and let me know when you're ready for the next phase.
```

#### When Building Authentication:
```
Implement the complete authentication system for XTask:

AUTHENTICATION REQUIREMENTS:
- Support Google and Twitter OAuth 2.0
- Database-based sessions (no Redis)
- Secure token handling with encryption
- Proper error handling and user feedback
- Responsive login interface

COMPONENTS TO CREATE:
1. AuthProvider - Context provider for auth state
2. useAuth - Custom hook for authentication
3. ProtectedRoute - Route wrapper for auth-required pages
4. LoginPage - OAuth login interface with Google/Twitter buttons
5. OAuth callback handlers

IMPLEMENTATION DETAILS:
- Use secure httpOnly cookies for session management
- Implement proper loading states during auth flow
- Handle authentication errors gracefully
- Redirect users appropriately after login/logout
- Include CSRF protection and rate limiting

STYLING:
- Dark theme with purple accent buttons
- Smooth animations for login flow
- Mobile-responsive design
- Clear error messaging

Start with the AuthProvider and useAuth hook, then build the login interface.
```

#### When Building Agent Management:
```
Create the AI agent management system for XTask:

AGENT SYSTEM REQUIREMENTS:
- Create, edit, and delete AI agents
- Upload and validate JSON persona files
- Configure AI providers (OpenAI, Google)
- Manage agent status (active/inactive)
- Display agent performance metrics

COMPONENTS TO BUILD:
1. AgentCard - Display agent with metrics and actions
2. AgentForm - Create/edit agent form with validation
3. AgentList - Grid/list view with search and filtering
4. PersonaUploader - Drag & drop JSON file upload
5. AgentSettings - AI provider configuration

AGENT DATA STRUCTURE:
```typescript
interface Agent {
  id: string;
  name: string;
  description: string;
  personaData: PersonaSchema;
  aiProvider: 'openai' | 'google';
  aiModel: string;
  isActive: boolean;
  tweetsGenerated: number;
  engagementRate: number;
  maxDailyTweets: number;
}
```

FEATURES:
- Form validation with Zod schemas
- Real-time status updates
- Bulk operations (activate/deactivate multiple agents)
- Performance metrics visualization
- Persona file validation and preview

Start with the AgentCard component and then build the creation form.
```

#### When Building Tweet Composer:
```
Implement the advanced tweet composition system:

COMPOSER REQUIREMENTS:
- Rich text editing with formatting
- 280 character limit with visual feedback
- Media upload (images, videos, GIFs)
- Hashtag and mention autocomplete
- Thread composition for multi-tweet posts
- AI content suggestions from agents
- Draft auto-save functionality

COMPONENTS TO CREATE:
1. TweetComposer - Main composition interface
2. RichTextEditor - Text formatting with mentions/hashtags
3. MediaUploader - Drag & drop media upload with preview
4. CharacterCounter - Real-time character counting with warnings
5. ThreadComposer - Multi-tweet thread creation
6. EmojiPicker - Emoji selection interface

FEATURES:
- Real-time character counting with color-coded warnings
- Media preview with remove/reorder functionality
- Hashtag suggestions based on content
- @mention autocomplete with user search
- Thread numbering and management
- AI-powered content suggestions
- Draft persistence across sessions

STYLING:
- Dark theme with purple accents
- Smooth animations for character counter
- Mobile-optimized interface
- Clear visual hierarchy

Begin with the main TweetComposer component and character counter.
```

#### When Building Scheduling System:
```
Create the advanced scheduling system for XTask:

SCHEDULING REQUIREMENTS:
- Visual calendar interface for scheduled tweets
- Timezone-aware scheduling
- Optimal posting time suggestions
- Bulk scheduling operations
- Schedule conflict detection
- Drag & drop rescheduling

COMPONENTS TO BUILD:
1. ScheduleCalendar - Visual calendar with scheduled tweets
2. ScheduleForm - Date/time picker with timezone support
3. ScheduledTweetCard - Individual scheduled tweet display
4. TimePicker - Time selection with optimal suggestions
5. BulkScheduler - Multiple tweet scheduling interface

FEATURES:
- Monthly/weekly/daily calendar views
- Color-coded tweets by agent
- Optimal time recommendations based on analytics
- Timezone conversion and display
- Schedule conflict warnings
- Bulk operations (reschedule, cancel multiple)

CALENDAR FUNCTIONALITY:
- Click to schedule new tweet
- Drag & drop to reschedule
- Hover to preview tweet content
- Filter by agent or status
- Export schedule to calendar apps

Start with the ScheduleCalendar component and basic scheduling form.
```

#### When Building Analytics Dashboard:
```
Implement the comprehensive analytics dashboard:

ANALYTICS REQUIREMENTS:
- Tweet performance metrics (likes, retweets, replies)
- Engagement rate calculations
- Follower growth tracking
- Agent performance comparison
- Content type analysis
- Time-based performance insights

COMPONENTS TO CREATE:
1. AnalyticsDashboard - Main metrics overview
2. EngagementChart - Interactive line/bar charts
3. GrowthChart - Follower growth visualization
4. MetricCard - Individual metric display with trends
5. PerformanceTable - Detailed tweet performance data
6. DateRangePicker - Time period selection

CHART TYPES:
- Line charts for engagement trends
- Bar charts for performance comparison
- Pie charts for content distribution
- Area charts for growth metrics
- Heatmaps for optimal posting times

FEATURES:
- Interactive charts with hover details
- Export functionality (PDF, CSV)
- Real-time data updates
- Comparative analysis between agents
- Custom date range selection
- Performance insights and recommendations

Use Recharts for chart implementation with dark theme colors.
```

### 🔄 DEVELOPMENT WORKFLOW PROMPT

```
Follow this development workflow for each feature:

1. PLANNING PHASE:
   - Review the feature requirements
   - Design the component hierarchy
   - Define TypeScript interfaces
   - Plan the state management approach

2. IMPLEMENTATION PHASE:
   - Create base components first
   - Implement core functionality
   - Add styling with Tailwind CSS
   - Include proper error handling

3. INTEGRATION PHASE:
   - Connect to state management
   - Implement API integration
   - Add loading and error states
   - Test user interactions

4. OPTIMIZATION PHASE:
   - Add animations and transitions
   - Optimize for performance
   - Ensure accessibility compliance
   - Test responsive design

5. QUALITY ASSURANCE:
   - Review code for best practices
   - Test edge cases and error scenarios
   - Validate TypeScript types
   - Ensure consistent styling

Always ask for clarification if requirements are unclear, and provide progress updates as you complete each component.
```

This startup prompt gives you everything needed to initialize development and guide the agent through building the complete XTask frontend systematically.
