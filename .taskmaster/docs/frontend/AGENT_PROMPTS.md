# XTask Frontend Agent Prompts
## Comprehensive Prompts for AI Agent Development

### 🎯 Primary System Prompt

```
You are an expert frontend developer specializing in building modern React applications with Next.js 15, TypeScript, and Tailwind CSS. You are working on XTask, an AI-powered social media management platform.

CORE REQUIREMENTS:
- Use Next.js 15 with App Router
- Write all code in TypeScript with strict typing
- Use Tailwind CSS for styling with dark theme (#0a0a0a background, #8b5cf6 purple accents)
- Implement shadcn/ui components as base UI library
- Follow the coding standards in CODING_METHODS_AND_RULES.md
- Reference the technical specifications in KNOWLEDGE_BASE.md

ARCHITECTURE PRINCIPLES:
- Component-based architecture with clear separation of concerns
- Mobile-first responsive design
- Accessibility compliance (WCAG 2.1 AA)
- Performance optimization with React best practices
- Proper error handling and loading states

STYLING GUIDELINES:
- Dark theme with purple accents (#8b5cf6, #a855f7)
- Use Tailwind utility classes exclusively
- Implement proper hover states and transitions
- Ensure consistent spacing and typography
- Use class-variance-authority for component variants

Always provide complete, production-ready code with proper TypeScript interfaces, error handling, and responsive design.
```

### 🧩 Component Development Prompts

#### Base UI Component Creation
```
Create a [COMPONENT_NAME] component for the XTask platform following these requirements:

SPECIFICATIONS:
- Component: [COMPONENT_NAME]
- Variants: [LIST_VARIANTS]
- Props: [DESCRIBE_PROPS]
- Functionality: [DESCRIBE_FUNCTIONALITY]

REQUIREMENTS:
- Use TypeScript with proper interfaces
- Implement forwardRef for DOM element access
- Use class-variance-authority for variants
- Include proper ARIA attributes for accessibility
- Add hover states and transitions
- Support dark theme colors
- Include loading and disabled states where applicable

STYLING:
- Dark background (#0a0a0a) with purple accents (#8b5cf6)
- Use Tailwind CSS utility classes
- Implement proper focus states
- Add smooth transitions (transition-colors, transition-transform)
- Ensure mobile-responsive design

Please provide the complete component with TypeScript interfaces, proper styling, and usage examples.
```

#### Feature Component Development
```
Create a [FEATURE_NAME] component for the XTask dashboard with the following specifications:

COMPONENT: [FEATURE_NAME]
PURPOSE: [DESCRIBE_PURPOSE]
FEATURES: [LIST_FEATURES]
DATA: [DESCRIBE_DATA_STRUCTURE]

REQUIREMENTS:
- Use React Hook Form with Zod validation for forms
- Implement proper loading and error states
- Use React Query for data fetching
- Include responsive design for mobile/desktop
- Add proper TypeScript interfaces
- Implement accessibility features

STYLING:
- Follow XTask design system (dark theme, purple accents)
- Use consistent spacing and typography
- Add hover effects and micro-interactions
- Ensure proper contrast ratios

FUNCTIONALITY:
- Handle user interactions properly
- Implement optimistic updates where appropriate
- Add proper error handling and user feedback
- Include loading skeletons or spinners

Please provide the complete component with all necessary hooks, types, and styling.
```

### 🔐 Authentication Component Prompts

#### Auth Provider Setup
```
Create the authentication system for XTask with the following requirements:

COMPONENTS NEEDED:
1. AuthProvider - Context provider for authentication state
2. useAuth hook - Custom hook for accessing auth state
3. ProtectedRoute - Route wrapper for authenticated pages
4. LoginForm - OAuth login interface

AUTHENTICATION FLOW:
- Support Google and Twitter OAuth
- Use database sessions (no Redis)
- Implement proper loading states
- Handle authentication errors gracefully
- Redirect users appropriately

REQUIREMENTS:
- TypeScript interfaces for User and AuthContext
- Proper error handling and user feedback
- Loading states during authentication
- Responsive design for login forms
- Accessibility compliance

STYLING:
- Dark theme with purple accents
- Smooth transitions and animations
- Mobile-friendly OAuth buttons
- Clear error messaging

Please implement the complete authentication system with all components and proper TypeScript typing.
```

### 🤖 Agent Management Prompts

#### Agent Components
```
Create the agent management system for XTask with these components:

COMPONENTS:
1. AgentCard - Display agent with metrics and actions
2. AgentForm - Create/edit agent form
3. AgentList - Grid/list view of agents
4. PersonaUploader - JSON file upload for agent personas

AGENT DATA STRUCTURE:
```typescript
interface Agent {
  id: string;
  name: string;
  description: string;
  personaData: PersonaSchema;
  aiProvider: 'openai' | 'google';
  aiModel: string;
  isActive: boolean;
  tweetsGenerated: number;
  engagementRate: number;
  maxDailyTweets: number;
}
```

REQUIREMENTS:
- Form validation with Zod schemas
- File upload with drag & drop
- Real-time status updates
- Responsive grid layouts
- Proper error handling

FEATURES:
- Agent activation/deactivation
- Performance metrics display
- Bulk operations
- Search and filtering

Please create all components with proper TypeScript interfaces and XTask styling.
```

### ✍️ Tweet Composition Prompts

#### Composer System
```
Create the tweet composition system for XTask with these specifications:

COMPONENTS:
1. TweetComposer - Main composition interface
2. RichTextEditor - Text formatting with mentions/hashtags
3. MediaUploader - Image/video upload with preview
4. CharacterCounter - Real-time character counting
5. ThreadComposer - Multi-tweet thread creation

FEATURES:
- 280 character limit with visual feedback
- Media upload with drag & drop
- Hashtag and mention autocomplete
- Thread composition
- Draft auto-save
- AI content suggestions
- Emoji picker integration

REQUIREMENTS:
- Real-time character counting
- Media preview with remove option
- Form validation and error handling
- Responsive design for mobile
- Accessibility compliance

STYLING:
- Dark theme with purple accents
- Smooth animations and transitions
- Clear visual hierarchy
- Mobile-optimized interface

Please implement the complete tweet composition system with all features and proper TypeScript typing.
```

### 📅 Scheduling System Prompts

#### Schedule Components
```
Create the scheduling system for XTask with these components:

COMPONENTS:
1. ScheduleCalendar - Visual calendar with scheduled tweets
2. ScheduleForm - Date/time picker with timezone support
3. ScheduledTweetCard - Individual scheduled tweet display
4. TimePicker - Time selection interface
5. BulkScheduler - Multiple tweet scheduling

FEATURES:
- Visual calendar interface
- Timezone-aware scheduling
- Optimal time suggestions
- Bulk scheduling operations
- Schedule conflict detection
- Drag & drop rescheduling

REQUIREMENTS:
- Date/time validation
- Timezone handling
- Responsive calendar view
- Mobile-friendly time picker
- Proper error handling

STYLING:
- Dark theme calendar
- Purple accent for selected dates
- Clear time slot visualization
- Mobile-optimized interface

Please create the complete scheduling system with proper TypeScript interfaces and XTask design system.
```

### 📊 Analytics Dashboard Prompts

#### Analytics Components
```
Create the analytics dashboard for XTask with these components:

COMPONENTS:
1. AnalyticsDashboard - Main metrics overview
2. EngagementChart - Line/bar charts for engagement
3. GrowthChart - Follower growth visualization
4. MetricCard - Individual metric display
5. DateRangePicker - Time period selection

METRICS TO DISPLAY:
- Tweet performance (likes, retweets, replies)
- Engagement rates
- Follower growth
- Reach and impressions
- Agent performance comparison

REQUIREMENTS:
- Interactive charts with hover effects
- Responsive chart layouts
- Export functionality
- Real-time data updates
- Proper loading states

CHART LIBRARY:
- Use Recharts or Chart.js
- Implement dark theme colors
- Add smooth animations
- Ensure mobile responsiveness

Please create the complete analytics dashboard with interactive charts and proper TypeScript typing.
```

### 🎨 Styling and Animation Prompts

#### Design System Implementation
```
Implement the XTask design system with these specifications:

THEME:
- Primary: #8b5cf6 (purple)
- Background: #0a0a0a (dark)
- Surface: #1a1a1a (card background)
- Border: #2a2a2a
- Text: #ffffff (primary), #a1a1aa (muted)

COMPONENTS TO STYLE:
- Buttons (primary, secondary, outline, ghost)
- Cards with hover effects
- Form inputs with focus states
- Navigation elements
- Status badges

ANIMATIONS:
- Hover effects (scale, glow)
- Page transitions (fade, slide)
- Loading states (skeleton, spinner)
- Micro-interactions

REQUIREMENTS:
- Use Tailwind CSS utility classes
- Implement class-variance-authority for variants
- Add smooth transitions
- Ensure accessibility compliance
- Mobile-responsive design

Please create the complete design system with all component variants and animations.
```

### 🔧 State Management Prompts

#### Store Implementation
```
Create the state management system for XTask using Zustand:

STORES NEEDED:
1. authStore - Authentication state
2. agentStore - Agent management
3. composeStore - Tweet composition
4. scheduleStore - Scheduling state
5. settingsStore - User preferences

REQUIREMENTS:
- TypeScript interfaces for all stores
- Proper state updates with immer
- Persistence for user preferences
- Optimistic updates where appropriate

REACT QUERY SETUP:
- Query keys organization
- Mutation handling
- Cache invalidation
- Error handling

Please implement the complete state management system with proper TypeScript typing and React Query integration.
```

### 🧪 Testing Prompts

#### Component Testing
```
Create comprehensive tests for the [COMPONENT_NAME] component:

TESTING REQUIREMENTS:
- Use React Testing Library
- Test user interactions
- Test error states
- Test loading states
- Test accessibility

TEST CASES:
- Component renders correctly
- User interactions work properly
- Form validation functions
- Error handling works
- Responsive behavior

Please provide complete test suites with proper mocking and assertions.
```

### 🚀 Performance Optimization Prompts

#### Optimization Implementation
```
Optimize the XTask frontend for performance:

OPTIMIZATION AREAS:
- Component memoization
- Bundle size reduction
- Image optimization
- Lazy loading
- Virtual scrolling

REQUIREMENTS:
- Use React.memo appropriately
- Implement code splitting
- Optimize images with Next.js Image
- Add loading skeletons
- Minimize re-renders

Please implement performance optimizations while maintaining code readability and functionality.
```

These prompts provide comprehensive guidance for building every aspect of the XTask frontend with consistent quality and adherence to best practices.
