# Task ID: 13
# Title: UploadThing Integration for Media Uploads
# Status: pending
# Dependencies: 2
# Priority: high
# Description: Integrate UploadThing 6.2.0 for secure and efficient media (images/videos) uploads, and define the `MediaFile` schema.
# Details:
Sign up for UploadThing and obtain API keys. Install `uploadthing` (v6.2.0) and `@uploadthing/react`. Configure the UploadThing endpoint in your Next.js project (e.g., `app/api/uploadthing/core.ts`). Define the `MediaFile` model in `prisma/schema.prisma` with fields like `id`, `userId`, `url`, `key`, `type`, `size`, `createdAt`. Implement the `UploadDropzone` or `UploadButton` component in the frontend. On successful upload, store the returned URL and key in the `MediaFile` database table.

# Test Strategy:
Upload various image and video file types. Verify successful upload to UploadThing and that the file URL and key are stored correctly in the `MediaFile` table. Test error handling for failed uploads.

# Subtasks:
## 1. Setup UploadThing API Keys [pending]
### Dependencies: None
### Description: Obtain and configure the necessary API keys (APP_ID, SECRET) from the UploadThing dashboard and add them to the project's environment variables (.env file).
### Details:
This involves signing up for UploadThing, creating an application, and securely storing the generated API keys for backend access.

## 2. Define MediaFile Schema and Storage [pending]
### Dependencies: None
### Description: Create a new database schema (e.g., `MediaFile` model) to store metadata for uploaded files, including fields like `id`, `url`, `fileName`, `fileSize`, `mimeType`, and `uploadDate`. Implement the necessary database migrations.
### Details:
This schema will be used to persist information about files uploaded via UploadThing, allowing for later retrieval and management.

## 3. Configure Backend UploadThing Endpoints [pending]
### Dependencies: 13.1, 13.2
### Description: Implement the backend API routes for UploadThing, defining the file types, sizes, and handling the `onUploadComplete` callback to save file metadata (URL, name, size) to the `MediaFile` database schema.
### Details:
This involves setting up the `/api/uploadthing` route, configuring the `createUploadthing` instance, and integrating with the database to store file details upon successful upload.

## 4. Integrate Frontend Upload Component [pending]
### Dependencies: 13.3
### Description: Integrate the UploadThing React component (e.g., `UploadButton` or `Uploader`) into the frontend application, connecting it to the configured backend endpoints and handling successful upload responses.
### Details:
This step involves adding the UI element that users will interact with to upload files, ensuring it correctly communicates with the backend and provides feedback on upload status.

