# Task ID: 10
# Title: Database Schema for AI Agents and Memory
# Status: pending
# Dependencies: 2
# Priority: high
# Description: Define the database schema for `Agent` and `AgentMemory` models, including fields for persona definitions and vector embeddings.
# Details:
Update `prisma/schema.prisma` to define `Agent` model with fields like `id`, `userId`, `name`, `personaDefinition` (JSONB), `preferences` (JSONB), `createdAt`, `updatedAt`. Define `AgentMemory` model with fields like `id`, `agentId`, `content`, `embedding` (Bytes/ByteA for pgvector), `timestamp`. Establish a one-to-many relationship between `Agent` and `AgentMemory`. Run `bun prisma migrate dev --name agent_schema`.

# Test Strategy:
Verify new tables and columns are created. Use Prisma Studio to manually add agent data and confirm schema integrity. Ensure the `embedding` field is correctly configured for binary data suitable for `pgvector`.

# Subtasks:
## 1. Define Agent Model Schema [pending]
### Dependencies: None
### Description: Outline the Prisma schema for the `Agent` model, including fields such as `id`, `name`, `description`, `createdAt`, and `updatedAt`.
### Details:
Focus on basic scalar fields for the Agent entity.

## 2. Define AgentMemory Model Schema with Embedding [pending]
### Dependencies: 10.1
### Description: Outline the Prisma schema for the `AgentMemory` model, including fields like `id`, `content`, `createdAt`, `updatedAt`, `agentId` (foreign key to Agent), and the `embedding` field typed for `pgvector` (e.g., `Float[]`).
### Details:
Ensure the `embedding` field is correctly typed for `pgvector` and the relationship to `Agent` is established.

## 3. Generate Prisma Migration for New Models [pending]
### Dependencies: 10.1, 10.2
### Description: Create a new Prisma migration script that incorporates the schema definitions for both `Agent` and `AgentMemory` models.
### Details:
Use `npx prisma migrate dev --name add_agent_and_memory_models` or a similar command to generate the migration file.

## 4. Apply Database Migration [pending]
### Dependencies: 10.3
### Description: Execute the generated Prisma migration to apply the new `Agent` and `AgentMemory` table schemas to the database.
### Details:
Run `npx prisma migrate deploy` in production or `npx prisma db push` for development environments.

