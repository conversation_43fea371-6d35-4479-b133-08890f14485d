c# XTask: AI-Powered Social Media Management Platform
## Technical Implementation Plan v2.0

### 🏗️ Architecture Overview

**Hybrid Next.js 15 + Express.js Architecture**
- **Frontend**: Next.js 15 App Router (React 18+)
- **Backend**: Custom Express.js server integrated with Next.js
- **Database**: PostgreSQL with pgvector extension
- **Scheduling**: Bull Queue + Redis (production-grade job scheduling)
- **Theme**: Black background with purple accents (#8B5CF6, #A855F7)

### 📋 Tech Stack Specifications

#### Core Framework
```json
{
  "framework": "Next.js 15.x",
  "runtime": "Node.js 20+",
  "language": "TypeScript 5.x",
  "architecture": "Hybrid (Next.js + Express)"
}
```

#### Backend Dependencies
```json
{
  "express": "^4.18.2",
  "passport": "^0.7.0",
  "passport-google-oauth20": "^2.0.0",
  "passport-twitter": "^1.0.4",
  "bull": "^4.12.0",
  "redis": "^4.6.0",
  "prisma": "^5.7.0",
  "@prisma/client": "^5.7.0",
  "bcryptjs": "^2.4.3",
  "jsonwebtoken": "^9.0.2",
  "multer": "^1.4.5-lts.1",
  "express-session": "^1.17.3",
  "connect-redis": "^7.1.0"
}
```

#### AI & External Services
```json
{
  "openai": "^4.20.0",
  "@google/generative-ai": "^0.2.0",
  "uploadthing": "^6.0.0",
  "twitter-api-v2": "^1.15.0"
}
```

#### Frontend Dependencies
```json
{
  "tailwindcss": "^3.4.0",
  "@headlessui/react": "^1.7.17",
  "@heroicons/react": "^2.0.18",
  "framer-motion": "^10.16.0",
  "react-hook-form": "^7.48.0",
  "zustand": "^4.4.7",
  "react-query": "^3.39.0"
}
```

### 🏢 Project Structure

```
xtask/
├── src/
│   ├── app/                    # Next.js 15 App Router
│   │   ├── (auth)/
│   │   │   ├── login/
│   │   │   ├── signup/
│   │   │   └── callback/
│   │   ├── dashboard/
│   │   │   ├── agents/
│   │   │   ├── compose/
│   │   │   ├── schedule/
│   │   │   ├── analytics/
│   │   │   └── settings/
│   │   ├── api/               # Next.js API Routes (delegating to Express)
│   │   ├── globals.css
│   │   └── layout.tsx
│   ├── server/                # Express.js Backend
│   │   ├── routes/
│   │   │   ├── auth.ts
│   │   │   ├── agents.ts
│   │   │   ├── tweets.ts
│   │   │   ├── scheduling.ts
│   │   │   └── media.ts
│   │   ├── middleware/
│   │   ├── services/
│   │   │   ├── ai-service.ts
│   │   │   ├── agent-service.ts
│   │   │   ├── scheduler-service.ts
│   │   │   └── persona-service.ts
│   │   ├── jobs/              # Bull Queue Jobs
│   │   ├── config/
│   │   └── app.ts
│   ├── components/
│   │   ├── ui/                # Reusable UI components
│   │   ├── agents/
│   │   ├── compose/
│   │   └── layout/
│   ├── lib/
│   │   ├── db.ts              # Prisma client
│   │   ├── auth.ts
│   │   ├── redis.ts
│   │   └── utils.ts
│   ├── types/
│   └── hooks/
├── prisma/
│   ├── schema.prisma
│   └── migrations/
├── public/
├── server.js                  # Custom server entry point
├── next.config.js
├── tailwind.config.js
├── package.json
└── README.md
```

### 🤖 Enhanced Agent System Architecture

#### Agent Persona Schema (JSON)
```json
{
  "name": "TechGuru_AI",
  "metadata": {
    "version": "1.0",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z",
    "author": "user_id_123"
  },
  "persona": {
    "role": "Tech thought leader and developer advocate",
    "description": "Experienced software engineer who shares insights on cutting-edge technologies, best practices, and industry trends. Focuses on practical advice with a touch of humor.",
    "personality_traits": [
      "analytical", "helpful", "slightly_sarcastic", "passionate_about_tech"
    ],
    "expertise_areas": [
      "JavaScript/TypeScript", "React/Next.js", "AI/ML", "DevOps", "Web3"
    ]
  },
  "content_strategy": {
    "posting_frequency": {
      "min_hours_between_posts": 2,
      "max_posts_per_day": 8,
      "preferred_times": ["09:00", "13:00", "17:00", "20:00"]
    },
    "content_types": {
      "educational_tips": 40,
      "industry_news_commentary": 25,
      "personal_experiences": 20,
      "community_engagement": 15
    }
  },
  "voice_and_tone": {
    "formality_level": "casual_professional",
    "emoji_usage": "moderate",
    "hashtag_style": "relevant_only",
    "mention_strategy": "strategic",
    "thread_tendency": "when_complex_topics"
  },
  "writing_style": {
    "sentence_structure": "varied_length",
    "vocabulary_level": "intermediate_technical",
    "common_phrases": [
      "Pro tip:", "Here's the thing:", "Hot take:", "Thread 🧵"
    ],
    "avoid_phrases": [
      "in my humble opinion", "just my 2 cents", "thoughts?"
    ],
    "preferred_ctas": [
      "What's your experience with X?",
      "Drop your thoughts below 👇",
      "Retweet if you found this helpful!"
    ]
  },
  "content_guidelines": {
    "topics_to_embrace": [
      "emerging_tech_trends", "coding_best_practices", "dev_tools_reviews",
      "tech_career_advice", "open_source_projects"
    ],
    "topics_to_avoid": [
      "political_controversy", "personal_drama", "off_topic_rants"
    ],
    "brand_safety_rules": [
      "no_profanity", "fact_check_technical_claims", "cite_sources_when_needed"
    ]
  },
  "engagement_patterns": {
    "reply_style": "thoughtful_and_constructive",
    "retweet_frequency": "selective_quality_content",
    "quote_tweet_approach": "add_valuable_commentary",
    "dm_responsiveness": "professional_and_helpful"
  },
  "media_preferences": {
    "image_style": "clean_screenshots_and_diagrams",
    "video_usage": "rare_but_high_quality",
    "gif_usage": "occasional_for_reactions",
    "link_sharing": "always_with_context"
  },
  "example_outputs": [
    {
      "type": "educational_tip",
      "content": "Pro tip: Use TypeScript's `satisfies` operator instead of type assertions when you want both type checking AND type inference. It's a game-changer for complex object types! 🚀\n\n#TypeScript #WebDev",
      "engagement_expectation": "medium",
      "media": null
    },
    {
      "type": "industry_commentary",
      "content": "The new React Compiler is fascinating, but let's be real - most of us still have legacy class components to deal with 😅\n\nWhat's your migration strategy? Gradual or big-bang approach?",
      "engagement_expectation": "high",
      "media": null
    }
  ],
  "behavioral_context": {
    "decision_making_factors": [
      "relevance_to_audience", "educational_value", "engagement_potential",
      "brand_alignment", "timing_appropriateness"
    ],
    "response_triggers": {
      "mentions": "respond_within_2_hours_if_relevant",
      "questions": "provide_helpful_answers",
      "criticism": "acknowledge_and_learn",
      "praise": "thank_and_engage"
    }
  },
  "analytics_goals": {
    "primary_metrics": ["engagement_rate", "follower_growth", "link_clicks"],
    "success_benchmarks": {
      "engagement_rate": "> 3%",
      "weekly_follower_growth": "> 50",
      "monthly_impressions": "> 100000"
    }
  }
}
```

#### Agent Behavior Engine
```typescript
interface AgentBehavior {
  evaluateContent(content: string, context: PersonaContext): ContentScore;
  generateTweet(prompt: string, persona: AgentPersona): Promise<GeneratedTweet>;
  scheduleOptimalTime(persona: AgentPersona): Date;
  handleEngagement(interaction: Interaction, persona: AgentPersona): Promise<Response>;
}
```

### 🗄️ Database Schema (Prisma)

```prisma
generator client {
  provider = "prisma-client-js"
  previewFeatures = ["postgresqlExtensions"]
}

datasource db {
  provider = "postgresql"
  url = env("DATABASE_URL")
  extensions = [pgvector(map: "vector")]
}

model User {
  id                    String    @id @default(cuid())
  email                 String    @unique
  username              String?   @unique
  displayName           String?
  hashedPassword        String?
  profilePicture        String?
  
  // OAuth
  googleId              String?
  twitterId             String?
  
  // AI Configuration
  openaiApiKey          String?   // Encrypted
  openaiBaseUrl         String?   @default("https://api.openai.com/v1")
  openaiModel           String?   @default("gpt-4")
  googleAiApiKey        String?   // Encrypted
  googleAiModel         String?   @default("gemini-pro")
  
  // Preferences
  defaultProvider       AiProvider @default(OPENAI)
  timezone              String    @default("UTC")
  theme                 Theme     @default(DARK)
  
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt
  
  // Relations
  agents                Agent[]
  twitterAccounts       TwitterAccount[]
  scheduledTweets       ScheduledTweet[]
  mediaFiles            MediaFile[]
  
  @@map("users")
}

model Agent {
  id                    String    @id @default(cuid())
  name                  String
  description           String?
  
  // Persona Configuration
  personaData           Json      // Full persona schema as JSON
  personaVersion        String    @default("1.0")
  
  // AI Configuration
  aiProvider            AiProvider @default(OPENAI)
  aiModel               String?
  temperature           Float     @default(0.7)
  maxTokens             Int       @default(280)
  
  // Behavior Settings
  isActive              Boolean   @default(true)
  autoSchedule          Boolean   @default(false)
  maxDailyTweets        Int       @default(5)
  minHoursBetweenTweets Int       @default(2)
  
  // Context & Memory
  contextWindow         Int       @default(10)
  memoryEnabled         Boolean   @default(true)
  
  userId                String
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt
  
  // Relations
  scheduledTweets       ScheduledTweet[]
  agentMemories         AgentMemory[]
  
  @@map("agents")
}

model AgentMemory {
  id                    String    @id @default(cuid())
  content               String
  embedding             Unsupported("vector(1536)")?
  contextType           ContextType
  importance            Float     @default(0.5)
  
  agentId               String
  agent                 Agent     @relation(fields: [agentId], references: [id], onDelete: Cascade)
  
  createdAt             DateTime  @default(now())
  
  @@map("agent_memories")
}

model TwitterAccount {
  id                    String    @id @default(cuid())
  twitterUserId         String    @unique
  username              String
  displayName           String
  profilePicture        String?
  
  accessToken           String    // Encrypted
  refreshToken          String?   // Encrypted
  tokenSecret           String?   // Encrypted (OAuth 1.0a)
  
  isActive              Boolean   @default(true)
  
  userId                String
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt
  
  // Relations
  scheduledTweets       ScheduledTweet[]
  
  @@map("twitter_accounts")
}

model ScheduledTweet {
  id                    String    @id @default(cuid())
  content               String
  
  // Scheduling
  scheduledAt           DateTime
  publishedAt           DateTime?
  status                TweetStatus @default(PENDING)
  
  // Twitter Integration
  tweetId               String?   // Twitter's tweet ID after publishing
  inReplyToId           String?
  
  // Agent Context
  generationPrompt      String?
  personaSnapshot       Json?     // Snapshot of persona at generation time
  
  // Media
  mediaUrls             String[]  @default([])
  
  // Relations
  userId                String
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  agentId               String?
  agent                 Agent?    @relation(fields: [agentId], references: [id], onDelete: SetNull)
  
  twitterAccountId      String
  twitterAccount        TwitterAccount @relation(fields: [twitterAccountId], references: [id], onDelete: Cascade)
  
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt
  
  @@map("scheduled_tweets")
}

model MediaFile {
  id                    String    @id @default(cuid())
  fileName              String
  fileType              String
  fileSize              Int
  url                   String
  uploadthingId         String?
  
  userId                String
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  createdAt             DateTime  @default(now())
  
  @@map("media_files")
}

// Enums
enum AiProvider {
  OPENAI
  GOOGLE
}

enum Theme {
  LIGHT
  DARK
}

enum TweetStatus {
  PENDING
  PUBLISHING
  PUBLISHED
  FAILED
  CANCELLED
}

enum ContextType {
  CONVERSATION
  TWEET_PERFORMANCE
  USER_INTERACTION
  TRENDING_TOPIC
  SCHEDULED_CONTENT
}
```

### 🔧 API Design

#### Authentication Routes
```typescript
// Express routes
POST   /api/auth/login
POST   /api/auth/register
GET    /api/auth/google
GET    /api/auth/google/callback
GET    /api/auth/twitter
GET    /api/auth/twitter/callback
POST   /api/auth/logout
GET    /api/auth/me
```

#### Agent Management
```typescript
GET    /api/agents                 // List user's agents
POST   /api/agents                 // Create new agent
GET    /api/agents/:id             // Get agent details
PUT    /api/agents/:id             // Update agent
DELETE /api/agents/:id             // Delete agent
POST   /api/agents/:id/persona     // Upload persona file
POST   /api/agents/:id/generate    // Generate tweet with agent
GET    /api/agents/:id/memory      // Get agent's memory/context
POST   /api/agents/:id/memory      // Add to agent's memory
```

#### Tweet Management
```typescript
POST   /api/tweets/compose         // Human-composed tweet
POST   /api/tweets/generate        // AI-generated tweet
POST   /api/tweets/schedule        // Schedule tweet
GET    /api/tweets/scheduled       // List scheduled tweets
PUT    /api/tweets/scheduled/:id   // Update scheduled tweet
DELETE /api/tweets/scheduled/:id   // Cancel scheduled tweet
POST   /api/tweets/publish         // Publish immediately
```

#### Media & Upload
```typescript
POST   /api/media/upload           // Upload media via UploadThing
GET    /api/media                  // List user's media
DELETE /api/media/:id             // Delete media file
```

### 🎯 Custom Server Integration

#### server.js
```javascript
const { createServer } = require('http');
const next = require('next');
const express = require('express');
const passport = require('passport');
const session = require('express-session');
const RedisStore = require('connect-redis')(session);
const redis = require('./src/lib/redis');

const dev = process.env.NODE_ENV !== 'production';
const app = next({ dev });
const handle = app.getRequestHandler();

async function main() {
  await app.prepare();
  
  const server = express();
  
  // Express middleware
  server.use(express.json());
  server.use(session({
    store: new RedisStore({ client: redis }),
    secret: process.env.SESSION_SECRET,
    resave: false,
    saveUninitialized: false,
  }));
  
  server.use(passport.initialize());
  server.use(passport.session());
  
  // API routes (Express)
  server.use('/api', require('./src/server/routes'));
  
  // All other routes (Next.js)
  server.all('*', (req, res) => {
    return handle(req, res);
  });
  
  const httpServer = createServer(server);
  const port = process.env.PORT || 3000;
  
  httpServer.listen(port, () => {
    console.log(`> Ready on http://localhost:${port}`);
  });
}

main().catch(console.error);
```

### 📊 Production Scheduling System

#### Bull Queue Configuration
```typescript
// src/lib/scheduler.ts
import Queue from 'bull';
import Redis from 'ioredis';

const redis = new Redis(process.env.REDIS_URL);

export const tweetQueue = new Queue('tweet publishing', {
  redis: {
    host: process.env.REDIS_HOST,
    port: process.env.REDIS_PORT,
    password: process.env.REDIS_PASSWORD,
  },
});

// Job types
tweetQueue.process('publish-tweet', async (job) => {
  const { tweetId } = job.data;
  await publishScheduledTweet(tweetId);
});

tweetQueue.process('agent-auto-tweet', async (job) => {
  const { agentId } = job.data;
  await generateAndScheduleAgentTweet(agentId);
});
```

### 🎨 UI Theme Configuration

#### Tailwind Config (Black & Purple Theme)
```javascript
// tailwind.config.js
module.exports = {
  content: ['./src/**/*.{js,ts,jsx,tsx}'],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#faf5ff',
          500: '#8b5cf6',
          600: '#7c3aed',
          700: '#6d28d9',
          900: '#4c1d95',
        },
        dark: {
          bg: '#0a0a0a',
          surface: '#1a1a1a',
          border: '#2a2a2a',
        }
      },
      backgroundImage: {
        'gradient-primary': 'linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%)',
      }
    }
  }
}
```

### 🚀 Deployment & Production

#### Environment Variables
```env
# Database
DATABASE_URL="postgresql://user:pass@localhost:5432/xtask?schema=public"

# Redis
REDIS_URL="redis://localhost:6379"

# Authentication
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"

# OAuth
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
TWITTER_CONSUMER_KEY="your-twitter-consumer-key"
TWITTER_CONSUMER_SECRET="your-twitter-consumer-secret"

# AI Providers (Optional defaults)
OPENAI_API_KEY="your-default-openai-key"
GOOGLE_AI_API_KEY="your-default-google-key"

# UploadThing
UPLOADTHING_SECRET="your-uploadthing-secret"
UPLOADTHING_APP_ID="your-uploadthing-app-id"

# Session
SESSION_SECRET="your-session-secret"
```

#### Package.json Scripts
```json
{
  "scripts": {
    "dev": "node server.js",
    "build": "next build",
    "start": "NODE_ENV=production node server.js",
    "db:generate": "prisma generate",
    "db:push": "prisma db push",
    "db:migrate": "prisma migrate deploy",
    "db:seed": "tsx prisma/seed.ts"
  }
}
```

### 🔄 Key Implementation Priorities

1. **Phase 1**: Basic authentication, agent CRUD, persona file upload
2. **Phase 2**: Tweet composition, scheduling system with Bull Queue
3. **Phase 3**: AI integration (OpenAI + Google), agent behavior engine
4. **Phase 4**: Advanced scheduling, analytics, memory system
5. **Phase 5**: Production optimizations, monitoring, scaling

### 📈 Advantages of This Architecture

- **Express Integration**: Full Passport.js support with session management
- **Production Scheduling**: Bull Queue + Redis for reliable job processing
- **Scalable AI**: Support for multiple providers with custom endpoints
- **Rich Agent System**: Comprehensive persona-driven behavior
- **Modern Stack**: Next.js 15 with latest React features
- **Type Safety**: Full TypeScript implementation
- **Vector Search**: PostgreSQL + pgvector for semantic capabilities

