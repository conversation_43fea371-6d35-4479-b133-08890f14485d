#!/usr/bin/env bun

/**
 * Test Script for Vector Embeddings and Agent Memory
 * 
 * This script tests the complete embedding pipeline:
 * 1. Embedding generation with OpenAI text-embedding-3-large
 * 2. Storage in AgentMemory with pgvector
 * 3. Similarity search functionality
 * 4. Performance benchmarking
 * 
 * Run with: bun run scripts/test-embeddings.ts
 */

import { embeddingService } from '../src/services/embeddings'
import { agentMemoryService } from '../src/services/memory/agent-memory'
import { prisma } from '../src/lib/db'

// Test data for social media agent memories
const testMemories = [
  {
    content: "User prefers posting about technology and AI developments",
    type: "preference",
    importance: 0.9,
    tags: ["technology", "AI", "preferences"]
  },
  {
    content: "Successfully posted a viral tweet about machine learning trends",
    type: "achievement",
    importance: 0.8,
    tags: ["success", "viral", "machine-learning"]
  },
  {
    content: "User mentioned they work in the fintech industry",
    type: "personal",
    importance: 0.7,
    tags: ["personal", "fintech", "career"]
  },
  {
    content: "Engagement is highest on tweets posted between 9-11 AM EST",
    type: "analytics",
    importance: 0.9,
    tags: ["analytics", "timing", "engagement"]
  },
  {
    content: "User dislikes overly promotional content",
    type: "preference",
    importance: 0.8,
    tags: ["preferences", "content-style"]
  },
  {
    content: "Recent conversation about cryptocurrency regulations",
    type: "interaction",
    importance: 0.6,
    tags: ["cryptocurrency", "regulations", "conversation"]
  },
  {
    content: "User follows thought leaders in blockchain space",
    type: "interest",
    importance: 0.7,
    tags: ["blockchain", "thought-leaders", "following"]
  },
  {
    content: "Prefers concise, informative tweets over long threads",
    type: "preference",
    importance: 0.8,
    tags: ["preferences", "tweet-style", "concise"]
  }
]

async function testEmbeddingGeneration() {
  console.log('🧪 Testing Embedding Generation...')
  
  try {
    // Test single embedding
    const testText = "This is a test message about artificial intelligence and machine learning"
    const result = await embeddingService.generateEmbedding(testText)
    
    console.log(`✅ Single embedding generated:`)
    console.log(`   Model: ${result.model}`)
    console.log(`   Dimensions: ${result.dimensions}`)
    console.log(`   Tokens: ${result.tokens}`)
    console.log(`   Vector length: ${result.embedding.length}`)
    console.log(`   First 5 values: [${result.embedding.slice(0, 5).map(v => v.toFixed(4)).join(', ')}...]`)
    
    // Test batch embeddings
    const testTexts = [
      "Machine learning is transforming technology",
      "Social media marketing strategies",
      "Financial technology innovations"
    ]
    
    const batchResult = await embeddingService.generateBatchEmbeddings(testTexts)
    console.log(`✅ Batch embeddings generated:`)
    console.log(`   Count: ${batchResult.embeddings.length}`)
    console.log(`   Total tokens: ${batchResult.totalTokens}`)
    console.log(`   Model: ${batchResult.model}`)
    
    // Test similarity calculation
    const similarity = embeddingService.cosineSimilarity(
      batchResult.embeddings[0].embedding,
      batchResult.embeddings[1].embedding
    )
    console.log(`✅ Similarity between first two embeddings: ${similarity.toFixed(4)}`)
    
    return true
  } catch (error) {
    console.error('❌ Embedding generation failed:', error)
    return false
  }
}

async function testMemoryStorage() {
  console.log('\n💾 Testing Memory Storage...')
  
  try {
    // Create a test agent first
    const testAgent = await prisma.agent.create({
      data: {
        name: 'Test Agent',
        description: 'Agent for testing embeddings',
        userId: 'test-user-id', // This would be a real user ID in production
        personality: {},
        writingStyle: {},
        topics: ['technology', 'AI'],
        preferredTimes: []
      }
    })
    
    console.log(`✅ Created test agent: ${testAgent.id}`)
    
    // Test single memory creation
    const singleMemory = await agentMemoryService.createMemory({
      agentId: testAgent.id,
      content: testMemories[0].content,
      type: testMemories[0].type,
      importance: testMemories[0].importance,
      tags: testMemories[0].tags,
      generateEmbedding: true
    })
    
    console.log(`✅ Created single memory: ${singleMemory.id}`)
    console.log(`   Has embedding: ${!!singleMemory.embedding}`)
    console.log(`   Embedding model: ${singleMemory.embeddingModel}`)
    console.log(`   Embedding dimensions: ${singleMemory.embeddingDim}`)
    
    // Test batch memory creation
    const batchMemories = await agentMemoryService.createBatchMemories(
      testMemories.slice(1).map(memory => ({
        agentId: testAgent.id,
        ...memory,
        generateEmbedding: true
      }))
    )
    
    console.log(`✅ Created batch memories: ${batchMemories.length}`)
    
    const memoriesWithEmbeddings = batchMemories.filter(m => m.embedding)
    console.log(`   Memories with embeddings: ${memoriesWithEmbeddings.length}`)
    
    return testAgent.id
  } catch (error) {
    console.error('❌ Memory storage failed:', error)
    return null
  }
}

async function testSimilaritySearch(agentId: string) {
  console.log('\n🔍 Testing Similarity Search...')
  
  try {
    // Test queries related to stored memories
    const testQueries = [
      "What are the user's content preferences?",
      "Tell me about posting times and engagement",
      "What does the user think about cryptocurrency?",
      "User's professional background"
    ]
    
    for (const query of testQueries) {
      console.log(`\n🔎 Query: "${query}"`)
      
      // Test similarity search
      const similarityResults = await agentMemoryService.searchMemoriesBySimilarity(
        agentId,
        query,
        { limit: 3, threshold: 0.5 }
      )
      
      console.log(`   Similarity results: ${similarityResults.length}`)
      similarityResults.forEach((result, index) => {
        console.log(`   ${index + 1}. [${result.similarity?.toFixed(3)}] ${result.content.substring(0, 60)}...`)
        console.log(`      Type: ${result.type}, Importance: ${result.importance}, Tags: [${result.tags.join(', ')}]`)
      })
      
      // Test text search for comparison
      const textResults = await agentMemoryService.searchMemoriesByText(
        agentId,
        query,
        { limit: 3 }
      )
      
      console.log(`   Text search results: ${textResults.length}`)
      textResults.forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.content.substring(0, 60)}...`)
      })
    }
    
    return true
  } catch (error) {
    console.error('❌ Similarity search failed:', error)
    return false
  }
}

async function testMemoryStats(agentId: string) {
  console.log('\n📊 Testing Memory Statistics...')
  
  try {
    const stats = await agentMemoryService.getMemoryStats(agentId)
    
    console.log(`✅ Memory Statistics:`)
    console.log(`   Total memories: ${stats.totalMemories}`)
    console.log(`   Memories with embeddings: ${stats.memoriesWithEmbeddings}`)
    console.log(`   Average importance: ${stats.averageImportance.toFixed(3)}`)
    console.log(`   Memory types:`)
    stats.memoryTypes.forEach(type => {
      console.log(`     - ${type.type}: ${type.count}`)
    })
    console.log(`   Top tags:`)
    stats.topTags.slice(0, 5).forEach(tag => {
      console.log(`     - ${tag.tag}: ${tag.count}`)
    })
    
    return true
  } catch (error) {
    console.error('❌ Memory statistics failed:', error)
    return false
  }
}

async function cleanup(agentId: string) {
  console.log('\n🧹 Cleaning up test data...')
  
  try {
    // Delete all memories for the test agent
    await prisma.agentMemory.deleteMany({
      where: { agentId }
    })
    
    // Delete the test agent
    await prisma.agent.delete({
      where: { id: agentId }
    })
    
    console.log('✅ Cleanup completed')
  } catch (error) {
    console.error('❌ Cleanup failed:', error)
  }
}

async function main() {
  console.log('🚀 Starting Vector Embeddings Test Suite\n')
  
  const startTime = Date.now()
  
  // Test 1: Embedding Generation
  const embeddingTest = await testEmbeddingGeneration()
  if (!embeddingTest) {
    console.log('❌ Embedding test failed, stopping...')
    process.exit(1)
  }
  
  // Test 2: Memory Storage
  const agentId = await testMemoryStorage()
  if (!agentId) {
    console.log('❌ Memory storage test failed, stopping...')
    process.exit(1)
  }
  
  // Test 3: Similarity Search
  const searchTest = await testSimilaritySearch(agentId)
  if (!searchTest) {
    console.log('❌ Similarity search test failed')
  }
  
  // Test 4: Memory Statistics
  const statsTest = await testMemoryStats(agentId)
  if (!statsTest) {
    console.log('❌ Memory statistics test failed')
  }
  
  // Cleanup
  await cleanup(agentId)
  
  const endTime = Date.now()
  const duration = (endTime - startTime) / 1000
  
  console.log(`\n🎉 Test Suite Completed in ${duration.toFixed(2)}s`)
  console.log('✅ All tests passed! Vector embeddings system is working correctly.')
}

// Run the test suite
main().catch(error => {
  console.error('💥 Test suite failed:', error)
  process.exit(1)
})
