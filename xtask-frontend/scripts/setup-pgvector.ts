#!/usr/bin/env bun

/**
 * pgvector Setup Script
 * 
 * This script helps set up pgvector extension and indexes
 * Run with: bun run scripts/setup-pgvector.ts
 */

import { prisma } from '../src/lib/db'

async function checkPgVectorExtension() {
  console.log('🔍 Checking pgvector extension...')
  
  try {
    const result = await prisma.$queryRaw`
      SELECT EXISTS(
        SELECT 1 FROM pg_extension WHERE extname = 'vector'
      ) as extension_exists;
    `
    
    const exists = (result as any)[0]?.extension_exists
    
    if (exists) {
      console.log('✅ pgvector extension is already enabled')
      return true
    } else {
      console.log('❌ pgvector extension is not enabled')
      return false
    }
  } catch (error) {
    console.error('❌ Error checking pgvector extension:', error)
    return false
  }
}

async function enablePgVectorExtension() {
  console.log('🔧 Enabling pgvector extension...')
  
  try {
    await prisma.$executeRaw`CREATE EXTENSION IF NOT EXISTS vector;`
    console.log('✅ pgvector extension enabled successfully')
    return true
  } catch (error) {
    console.error('❌ Error enabling pgvector extension:', error)
    console.log('Note: You may need superuser privileges to enable extensions.')
    console.log('Try running this SQL command directly in your database:')
    console.log('CREATE EXTENSION IF NOT EXISTS vector;')
    return false
  }
}

async function checkIndexes() {
  console.log('🔍 Checking vector indexes...')
  
  try {
    const indexes = await prisma.$queryRaw`
      SELECT indexname, indexdef 
      FROM pg_indexes 
      WHERE tablename = 'agent_memories' 
      AND indexname LIKE '%embedding%';
    `
    
    console.log(`Found ${(indexes as any[]).length} embedding-related indexes:`)
    ;(indexes as any[]).forEach(index => {
      console.log(`  - ${index.indexname}`)
    })
    
    return (indexes as any[]).length > 0
  } catch (error) {
    console.error('❌ Error checking indexes:', error)
    return false
  }
}

async function createVectorIndexes() {
  console.log('🔧 Creating vector indexes...')
  
  try {
    // Create the main vector similarity index
    await prisma.$executeRaw`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS agent_memories_embedding_cosine_idx 
      ON agent_memories USING ivfflat (embedding::vector(1024) vector_cosine_ops) 
      WITH (lists = 100);
    `
    
    // Create supporting indexes
    await prisma.$executeRaw`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS agent_memories_agent_type_importance_idx 
      ON agent_memories ("agentId", type, importance DESC);
    `
    
    await prisma.$executeRaw`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS agent_memories_embedding_not_null_idx 
      ON agent_memories ("agentId") WHERE embedding IS NOT NULL;
    `
    
    await prisma.$executeRaw`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS agent_memories_tags_gin_idx 
      ON agent_memories USING gin (tags);
    `
    
    console.log('✅ Vector indexes created successfully')
    return true
  } catch (error) {
    console.error('❌ Error creating vector indexes:', error)
    return false
  }
}

async function testVectorOperations() {
  console.log('🧪 Testing vector operations...')
  
  try {
    // Test vector casting
    const testVector = '[0.1,0.2,0.3,0.4,0.5]'
    await prisma.$queryRaw`SELECT ${testVector}::vector as test_vector;`
    console.log('✅ Vector casting works')
    
    // Test cosine distance operation
    const vector1 = '[1,0,0]'
    const vector2 = '[0,1,0]'
    const result = await prisma.$queryRaw`
      SELECT ${vector1}::vector <-> ${vector2}::vector as cosine_distance;
    `
    console.log(`✅ Cosine distance calculation works: ${(result as any)[0]?.cosine_distance}`)
    
    return true
  } catch (error) {
    console.error('❌ Vector operations test failed:', error)
    return false
  }
}

async function getTableInfo() {
  console.log('📊 Getting agent_memories table info...')
  
  try {
    const tableInfo = await prisma.$queryRaw`
      SELECT 
        column_name,
        data_type,
        is_nullable,
        column_default
      FROM information_schema.columns 
      WHERE table_name = 'agent_memories' 
      ORDER BY ordinal_position;
    `
    
    console.log('Table structure:')
    ;(tableInfo as any[]).forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'YES' ? '(nullable)' : '(not null)'}`)
    })
    
    // Check if table has any data
    const count = await prisma.agentMemory.count()
    console.log(`\nCurrent memory count: ${count}`)
    
    return true
  } catch (error) {
    console.error('❌ Error getting table info:', error)
    return false
  }
}

async function main() {
  console.log('🚀 pgvector Setup and Verification\n')
  
  // Check database connection
  try {
    await prisma.$connect()
    console.log('✅ Database connection successful')
  } catch (error) {
    console.error('❌ Database connection failed:', error)
    process.exit(1)
  }
  
  // Check and enable pgvector extension
  const extensionExists = await checkPgVectorExtension()
  if (!extensionExists) {
    const enabled = await enablePgVectorExtension()
    if (!enabled) {
      console.log('❌ Could not enable pgvector extension')
      process.exit(1)
    }
  }
  
  // Check and create indexes
  const indexesExist = await checkIndexes()
  if (!indexesExist) {
    console.log('Creating vector indexes...')
    const created = await createVectorIndexes()
    if (!created) {
      console.log('❌ Could not create vector indexes')
      process.exit(1)
    }
  }
  
  // Test vector operations
  const vectorOpsWork = await testVectorOperations()
  if (!vectorOpsWork) {
    console.log('❌ Vector operations test failed')
    process.exit(1)
  }
  
  // Get table information
  await getTableInfo()
  
  console.log('\n🎉 pgvector setup completed successfully!')
  console.log('✅ Your database is ready for vector embeddings.')
  
  await prisma.$disconnect()
}

// Run the setup
main().catch(error => {
  console.error('💥 Setup failed:', error)
  process.exit(1)
})
