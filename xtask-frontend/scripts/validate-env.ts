#!/usr/bin/env bun

/**
 * Environment Validation Script
 * 
 * Validates all environment variables and checks feature availability
 * Run with: bun run validate-env
 */

import { checkEnvironmentHealth, getFeatureAvailability, getAIProviders } from '../src/lib/env';

function main() {
  console.log('🔍 Validating XTask Environment Configuration...\n');

  try {
    const health = checkEnvironmentHealth();
    
    // Display validation results
    if (health.valid) {
      console.log('✅ Environment validation passed!\n');
    } else {
      console.log('❌ Environment validation failed!\n');
      console.log('Errors found:');
      health.errors.forEach(error => {
        console.log(`  - ${error.field}: ${error.message}`);
      });
      console.log('');
    }

    // Display feature availability
    console.log('📋 Feature Availability:');
    console.log('========================');
    
    const features = health.features;
    console.log(`Database Connection: ${features.database ? '✅' : '❌'}`);
    console.log(`Google OAuth: ${features.googleOAuth ? '✅' : '❌'}`);
    console.log(`Twitter OAuth: ${features.twitterOAuth ? '✅' : '❌'}`);
    console.log(`Media Upload (UploadThing): ${features.uploadthing ? '✅' : '❌'}`);
    
    console.log('\n🤖 AI Providers:');
    console.log('================');
    
    const providers = health.aiProviders;
    if (providers.length === 0) {
      console.log('❌ No AI providers configured');
    } else {
      providers.forEach(provider => {
        console.log(`✅ ${provider.name} (${provider.models.length} models available)`);
        provider.models.forEach(model => {
          console.log(`   - ${model}`);
        });
      });
    }

    // Display summary
    console.log('\n📊 Configuration Summary:');
    console.log('=========================');
    console.log(`Total AI Providers: ${health.summary.totalProviders}`);
    console.log(`OAuth Ready: ${health.summary.oauthReady ? '✅' : '❌'}`);
    console.log(`Database Ready: ${health.summary.databaseReady ? '✅' : '❌'}`);
    console.log(`Media Ready: ${health.summary.mediaReady ? '✅' : '❌'}`);

    // Recommendations
    console.log('\n💡 Recommendations:');
    console.log('===================');
    
    if (!features.database) {
      console.log('⚠️  Set up your Neon PostgreSQL database and update DATABASE_URL');
    }
    
    if (!features.googleOAuth || !features.twitterOAuth) {
      console.log('⚠️  Configure OAuth credentials for Google and Twitter authentication');
    }
    
    if (providers.length === 0) {
      console.log('⚠️  Add at least one AI provider API key (OpenAI, Gemini, etc.)');
    }
    
    if (!features.uploadthing) {
      console.log('⚠️  Configure UploadThing for media upload functionality');
    }

    if (health.valid && health.summary.oauthReady && health.summary.databaseReady && providers.length > 0) {
      console.log('\n🎉 Your XTask environment is fully configured and ready!');
      console.log('\nNext steps:');
      console.log('1. Run: bun run dev');
      console.log('2. Test: curl http://localhost:3030/api/health');
      console.log('3. Start building amazing AI-powered features!');
    } else {
      console.log('\n🔧 Complete the missing configurations above to get started.');
    }

  } catch (error) {
    console.error('❌ Environment validation failed with error:', error);
    process.exit(1);
  }
}

main();
