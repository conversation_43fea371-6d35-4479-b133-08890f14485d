#!/usr/bin/env bun

/**
 * Embedding Performance Benchmark
 * 
 * Comprehensive performance testing for the vector embedding system
 * Run with: bun run scripts/benchmark-embeddings.ts
 */

import { embeddingService } from '../src/services/embeddings'
import { agentMemoryService } from '../src/services/memory/agent-memory'
import { prisma } from '../src/lib/db'

interface BenchmarkResult {
  operation: string
  duration: number
  itemsProcessed: number
  itemsPerSecond: number
  success: boolean
  error?: string
}

// Sample data for benchmarking
const sampleMemories = [
  "User prefers posting about technology and AI developments in the morning",
  "Successfully posted a viral tweet about machine learning trends that got 10k likes",
  "User mentioned they work in the fintech industry and follow crypto news",
  "Engagement is highest on tweets posted between 9-11 AM EST on weekdays",
  "User dislikes overly promotional content and prefers educational posts",
  "Recent conversation about cryptocurrency regulations and their impact",
  "User follows thought leaders in blockchain space like <PERSON>lik Buterin",
  "Prefers concise, informative tweets over long threads or stories",
  "User's audience responds well to data-driven content and statistics",
  "Posted about remote work trends and got good engagement from tech community",
  "User avoids political topics but engages with tech policy discussions",
  "Best performing content includes tutorials and how-to guides",
  "User collaborates with other tech influencers for cross-promotion",
  "Audience demographics: 70% male, 25-40 years old, tech professionals",
  "User schedules content in advance using automation tools",
  "Responds to comments within 2-4 hours for better engagement",
  "User's writing style is casual but professional, uses minimal jargon",
  "Posted about AI ethics and got thoughtful discussion in replies",
  "User tracks metrics weekly and adjusts strategy based on performance",
  "Prefers original content over retweets, ratio is about 80/20"
]

async function benchmarkEmbeddingGeneration(): Promise<BenchmarkResult> {
  console.log('📊 Benchmarking embedding generation...')
  
  const startTime = Date.now()
  let success = true
  let error: string | undefined
  
  try {
    // Test single embeddings
    for (let i = 0; i < 10; i++) {
      await embeddingService.generateEmbedding(sampleMemories[i % sampleMemories.length])
    }
    
    // Test batch embeddings
    await embeddingService.generateBatchEmbeddings(sampleMemories.slice(0, 10))
    
  } catch (err: any) {
    success = false
    error = err.message
  }
  
  const duration = Date.now() - startTime
  const itemsProcessed = 20 // 10 single + 10 batch
  
  return {
    operation: 'Embedding Generation',
    duration,
    itemsProcessed,
    itemsPerSecond: itemsProcessed / (duration / 1000),
    success,
    error
  }
}

async function benchmarkMemoryStorage(agentId: string): Promise<BenchmarkResult> {
  console.log('📊 Benchmarking memory storage...')
  
  const startTime = Date.now()
  let success = true
  let error: string | undefined
  let itemsProcessed = 0
  
  try {
    // Test single memory creation
    for (let i = 0; i < 5; i++) {
      await agentMemoryService.createMemory({
        agentId,
        content: sampleMemories[i],
        type: 'benchmark',
        importance: Math.random(),
        tags: ['benchmark', 'test'],
        generateEmbedding: true
      })
      itemsProcessed++
    }
    
    // Test batch memory creation
    const batchInputs = sampleMemories.slice(5, 15).map(content => ({
      agentId,
      content,
      type: 'benchmark',
      importance: Math.random(),
      tags: ['benchmark', 'batch'],
      generateEmbedding: true
    }))
    
    await agentMemoryService.createBatchMemories(batchInputs)
    itemsProcessed += batchInputs.length
    
  } catch (err: any) {
    success = false
    error = err.message
  }
  
  const duration = Date.now() - startTime
  
  return {
    operation: 'Memory Storage',
    duration,
    itemsProcessed,
    itemsPerSecond: itemsProcessed / (duration / 1000),
    success,
    error
  }
}

async function benchmarkSimilaritySearch(agentId: string): Promise<BenchmarkResult> {
  console.log('📊 Benchmarking similarity search...')
  
  const queries = [
    "What are the user's content preferences?",
    "Tell me about posting times and engagement",
    "User's professional background and industry",
    "Best performing content types",
    "Audience demographics and characteristics"
  ]
  
  const startTime = Date.now()
  let success = true
  let error: string | undefined
  let itemsProcessed = 0
  
  try {
    for (const query of queries) {
      // Test similarity search
      await agentMemoryService.searchMemoriesBySimilarity(agentId, query, {
        limit: 5,
        threshold: 0.5
      })
      
      // Test text search for comparison
      await agentMemoryService.searchMemoriesByText(agentId, query, {
        limit: 5
      })
      
      itemsProcessed += 2 // Both similarity and text search
    }
  } catch (err: any) {
    success = false
    error = err.message
  }
  
  const duration = Date.now() - startTime
  
  return {
    operation: 'Similarity Search',
    duration,
    itemsProcessed,
    itemsPerSecond: itemsProcessed / (duration / 1000),
    success,
    error
  }
}

async function benchmarkDatabaseOperations(agentId: string): Promise<BenchmarkResult> {
  console.log('📊 Benchmarking database operations...')
  
  const startTime = Date.now()
  let success = true
  let error: string | undefined
  let itemsProcessed = 0
  
  try {
    // Test memory retrieval
    const memories = await agentMemoryService.getAgentMemories(agentId, { limit: 50 })
    itemsProcessed += memories.length
    
    // Test memory statistics
    await agentMemoryService.getMemoryStats(agentId)
    itemsProcessed += 1
    
    // Test individual memory retrieval
    if (memories.length > 0) {
      for (let i = 0; i < Math.min(5, memories.length); i++) {
        await agentMemoryService.getMemory(memories[i].id)
        itemsProcessed += 1
      }
    }
    
  } catch (err: any) {
    success = false
    error = err.message
  }
  
  const duration = Date.now() - startTime
  
  return {
    operation: 'Database Operations',
    duration,
    itemsProcessed,
    itemsPerSecond: itemsProcessed / (duration / 1000),
    success,
    error
  }
}

async function analyzeSearchAccuracy(agentId: string): Promise<void> {
  console.log('🎯 Analyzing search accuracy...')
  
  const testCases = [
    {
      query: "content preferences",
      expectedKeywords: ["prefers", "content", "style", "educational"]
    },
    {
      query: "posting schedule",
      expectedKeywords: ["time", "morning", "schedule", "9-11"]
    },
    {
      query: "audience demographics",
      expectedKeywords: ["audience", "demographics", "male", "tech"]
    }
  ]
  
  for (const testCase of testCases) {
    console.log(`\n🔍 Testing query: "${testCase.query}"`)
    
    const results = await agentMemoryService.searchMemoriesBySimilarity(
      agentId,
      testCase.query,
      { limit: 3, threshold: 0.3 }
    )
    
    console.log(`   Found ${results.length} results`)
    
    results.forEach((result, index) => {
      const similarity = result.similarity?.toFixed(3) || 'N/A'
      const relevantKeywords = testCase.expectedKeywords.filter(keyword =>
        result.content.toLowerCase().includes(keyword.toLowerCase())
      )
      
      console.log(`   ${index + 1}. [${similarity}] ${result.content.substring(0, 60)}...`)
      console.log(`      Relevant keywords: [${relevantKeywords.join(', ')}]`)
      console.log(`      Type: ${result.type}, Importance: ${result.importance}`)
    })
  }
}

async function generatePerformanceReport(results: BenchmarkResult[]): Promise<void> {
  console.log('\n📈 Performance Report')
  console.log('=' .repeat(50))
  
  results.forEach(result => {
    const status = result.success ? '✅' : '❌'
    console.log(`${status} ${result.operation}:`)
    console.log(`   Duration: ${result.duration}ms`)
    console.log(`   Items processed: ${result.itemsProcessed}`)
    console.log(`   Items/second: ${result.itemsPerSecond.toFixed(2)}`)
    
    if (!result.success && result.error) {
      console.log(`   Error: ${result.error}`)
    }
    console.log()
  })
  
  // Calculate overall performance
  const totalDuration = results.reduce((sum, r) => sum + r.duration, 0)
  const totalItems = results.reduce((sum, r) => sum + r.itemsProcessed, 0)
  const successRate = results.filter(r => r.success).length / results.length * 100
  
  console.log('📊 Overall Performance:')
  console.log(`   Total duration: ${totalDuration}ms`)
  console.log(`   Total items processed: ${totalItems}`)
  console.log(`   Overall throughput: ${(totalItems / (totalDuration / 1000)).toFixed(2)} items/second`)
  console.log(`   Success rate: ${successRate.toFixed(1)}%`)
}

async function cleanup(agentId: string): Promise<void> {
  console.log('\n🧹 Cleaning up benchmark data...')
  
  try {
    // Delete benchmark memories
    const deleteResult = await prisma.agentMemory.deleteMany({
      where: {
        agentId,
        type: 'benchmark'
      }
    })
    
    // Delete test agent
    await prisma.agent.delete({
      where: { id: agentId }
    })
    
    console.log(`✅ Cleaned up ${deleteResult.count} benchmark memories and test agent`)
  } catch (error) {
    console.error('❌ Cleanup failed:', error)
  }
}

async function main() {
  console.log('🚀 Starting Embedding Performance Benchmark\n')
  
  // Create test agent
  const testAgent = await prisma.agent.create({
    data: {
      name: 'Benchmark Agent',
      description: 'Agent for performance testing',
      userId: 'benchmark-user-id',
      personality: {},
      writingStyle: {},
      topics: ['technology', 'AI'],
      preferredTimes: []
    }
  })
  
  console.log(`✅ Created benchmark agent: ${testAgent.id}\n`)
  
  const results: BenchmarkResult[] = []
  
  // Run benchmarks
  results.push(await benchmarkEmbeddingGeneration())
  results.push(await benchmarkMemoryStorage(testAgent.id))
  results.push(await benchmarkSimilaritySearch(testAgent.id))
  results.push(await benchmarkDatabaseOperations(testAgent.id))
  
  // Analyze search accuracy
  await analyzeSearchAccuracy(testAgent.id)
  
  // Generate performance report
  await generatePerformanceReport(results)
  
  // Cleanup
  await cleanup(testAgent.id)
  
  console.log('\n🎉 Benchmark completed successfully!')
  
  // Check if all benchmarks passed
  const allPassed = results.every(r => r.success)
  if (allPassed) {
    console.log('✅ All performance benchmarks passed!')
  } else {
    console.log('❌ Some benchmarks failed. Check the report above.')
    process.exit(1)
  }
}

// Run the benchmark
main().catch(error => {
  console.error('💥 Benchmark failed:', error)
  process.exit(1)
})
