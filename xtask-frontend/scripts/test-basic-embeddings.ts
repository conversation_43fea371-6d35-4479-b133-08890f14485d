#!/usr/bin/env bun

/**
 * Basic Embedding Test
 * 
 * Simple test to verify embedding generation works
 * Run with: bun run scripts/test-basic-embeddings.ts
 */

import { embeddingService } from '../src/services/embeddings'

async function testBasicEmbedding() {
  console.log('🧪 Testing Basic Embedding Generation...')
  
  try {
    // Test single embedding
    const testText = "This is a test message about artificial intelligence"
    console.log(`Input text: "${testText}"`)
    
    const result = await embeddingService.generateEmbedding(testText)
    
    console.log(`✅ Embedding generated successfully:`)
    console.log(`   Model: ${result.model}`)
    console.log(`   Dimensions: ${result.dimensions}`)
    console.log(`   Tokens: ${result.tokens}`)
    console.log(`   Vector length: ${result.embedding.length}`)
    console.log(`   First 5 values: [${result.embedding.slice(0, 5).map(v => v.toFixed(4)).join(', ')}...]`)
    
    // Test vector conversion
    const vectorString = embeddingService.embeddingToVector(result.embedding)
    console.log(`   Vector string format: ${vectorString.substring(0, 50)}...`)
    
    const backToArray = embeddingService.vectorToEmbedding(vectorString)
    console.log(`   Conversion back successful: ${backToArray.length === result.embedding.length}`)
    
    // Test similarity with itself (should be 1.0)
    const selfSimilarity = embeddingService.cosineSimilarity(result.embedding, result.embedding)
    console.log(`   Self-similarity: ${selfSimilarity.toFixed(6)} (should be 1.0)`)
    
    return true
  } catch (error) {
    console.error('❌ Embedding generation failed:', error)
    return false
  }
}

async function main() {
  console.log('🚀 Starting Basic Embedding Test\n')
  
  const success = await testBasicEmbedding()
  
  if (success) {
    console.log('\n🎉 Basic embedding test passed!')
    console.log('✅ OpenAI text-embedding-3-large integration is working correctly.')
  } else {
    console.log('\n❌ Basic embedding test failed!')
    process.exit(1)
  }
}

// Run the test
main().catch(error => {
  console.error('💥 Test failed:', error)
  process.exit(1)
})
