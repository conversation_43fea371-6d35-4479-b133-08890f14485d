#!/usr/bin/env bun

/**
 * Database Connection Test Script
 * 
 * This script tests the database connection and basic CRUD operations
 * Run with: bun run scripts/test-db.ts
 */

import { prisma, testDatabaseConnection, getDatabaseHealth } from '../src/lib/db';

async function main() {
  console.log('🔍 Testing XTask Database Connection...\n');

  // Test 1: Basic connection
  console.log('1. Testing database connection...');
  const isConnected = await testDatabaseConnection();
  if (!isConnected) {
    console.error('❌ Database connection failed. Please check your DATABASE_URL in .env');
    process.exit(1);
  }

  // Test 2: Health check
  console.log('\n2. Checking database health...');
  const health = await getDatabaseHealth();
  console.log('Health status:', health);

  // Test 3: Check if tables exist (this will fail if migrations haven't been run)
  console.log('\n3. Checking if tables exist...');
  try {
    const userCount = await prisma.user.count();
    console.log(`✅ Users table exists (${userCount} records)`);
    
    const agentCount = await prisma.agent.count();
    console.log(`✅ Agents table exists (${agentCount} records)`);
    
    const tweetCount = await prisma.scheduledTweet.count();
    console.log(`✅ ScheduledTweets table exists (${tweetCount} records)`);
    
    const memoryCount = await prisma.agentMemory.count();
    console.log(`✅ AgentMemory table exists (${memoryCount} records)`);
    
    const mediaCount = await prisma.mediaFile.count();
    console.log(`✅ MediaFile table exists (${mediaCount} records)`);
    
    const twitterCount = await prisma.twitterAccount.count();
    console.log(`✅ TwitterAccount table exists (${twitterCount} records)`);
    
  } catch (error) {
    console.log('⚠️  Tables not found. You need to run migrations first:');
    console.log('   bun prisma migrate dev --name init');
    console.log('\nError details:', error instanceof Error ? error.message : error);
  }

  // Test 4: Test basic CRUD operations (only if tables exist)
  try {
    console.log('\n4. Testing basic CRUD operations...');
    
    // Create a test user
    const testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Test User',
        timezone: 'UTC',
      },
    });
    console.log('✅ Created test user:', testUser.id);

    // Create a test agent
    const testAgent = await prisma.agent.create({
      data: {
        name: 'Test Agent',
        description: 'A test AI agent',
        userId: testUser.id,
        personality: {
          traits: ['creative', 'professional'],
          style: 'engaging'
        },
        writingStyle: {
          tone: 'friendly',
          length: 'medium'
        },
        topics: ['technology', 'AI'],
      },
    });
    console.log('✅ Created test agent:', testAgent.id);

    // Create a test memory with mock embedding
    const mockEmbedding = new Array(1536).fill(0).map(() => Math.random());
    const embeddingBytes = Buffer.from(new Float32Array(mockEmbedding).buffer);
    
    const testMemory = await prisma.agentMemory.create({
      data: {
        agentId: testAgent.id,
        content: 'This is a test memory for the AI agent',
        type: 'test',
        importance: 0.8,
        tags: ['test', 'memory'],
        embedding: embeddingBytes,
        embeddingModel: 'text-embedding-ada-002',
        embeddingDim: mockEmbedding.length,
      },
    });
    console.log('✅ Created test memory with embedding:', testMemory.id);

    // Clean up test data
    await prisma.agentMemory.delete({ where: { id: testMemory.id } });
    await prisma.agent.delete({ where: { id: testAgent.id } });
    await prisma.user.delete({ where: { id: testUser.id } });
    console.log('✅ Cleaned up test data');

  } catch (error) {
    console.log('⚠️  CRUD test skipped (migrations needed)');
  }

  console.log('\n🎉 Database test completed successfully!');
  console.log('\nNext steps:');
  console.log('1. Set up your Neon database and update DATABASE_URL in .env');
  console.log('2. Run: bun prisma migrate dev --name init');
  console.log('3. Start building your XTask features!');

  await prisma.$disconnect();
}

main().catch((error) => {
  console.error('❌ Database test failed:', error);
  process.exit(1);
});
