#!/bin/bash

echo "Installing Twitter API dependencies..."

# Core Twitter API library
echo "Installing twitter-api-v2..."
bun add twitter-api-v2

# Rate limiting plugin
echo "Installing rate limiting plugin..."
bun add @twitter-api-v2/plugin-rate-limit

# Additional utilities
echo "Installing additional dependencies..."
bun add mime-types
bun add @types/mime-types

echo "All Twitter API dependencies installed successfully!"
echo ""
echo "Next steps:"
echo "1. Ensure your .env file has the required Twitter API credentials:"
echo "   - TWITTER_CLIENT_ID=your_twitter_client_id"
echo "   - TWITTER_CLIENT_SECRET=your_twitter_client_secret"
echo "   - TWITTER_BEARER_TOKEN=your_bearer_token"
echo ""
echo "2. Test the Twitter integration:"
echo "   - Start server: bun run dev"
echo "   - Connect Twitter account via OAuth"
echo "   - Test posting: POST to http://localhost:3030/api/twitter/tweet"
echo ""
echo "3. Available API endpoints:"
echo "   - POST /api/twitter/tweet - Post single tweet"
echo "   - POST /api/twitter/thread - Post thread"
echo "   - POST /api/twitter/reply - Post reply"
echo "   - GET /api/twitter/tweet?id=<tweetId>&agentId=<agentId> - Get tweet"
echo "   - DELETE /api/twitter/tweet?id=<tweetId>&agentId=<agentId> - Delete tweet"
