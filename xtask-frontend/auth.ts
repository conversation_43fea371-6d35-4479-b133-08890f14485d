import NextAuth from "next-auth"
import Google from "next-auth/providers/google"
import Twitter from "next-auth/providers/twitter"
import { PrismaAdapter } from "@auth/prisma-adapter"
import { prisma } from "@/lib/db"
import { getEnv } from "@/lib/env"

// Get validated environment variables
const env = getEnv();

export const { handlers, auth, signIn, signOut } = NextAuth({
  adapter: PrismaAdapter(prisma),
  providers: [
    Google({
      clientId: env.AUTH_GOOGLE_ID,
      clientSecret: env.AUTH_GOOGLE_SECRET,
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code"
        }
      }
    }),
    Twitter({
      clientId: env.AUTH_TWITTER_ID,
      clientSecret: env.AUTH_TWITTER_SECRET,
    })
  ],
  callbacks: {
    async signIn({ account, profile, user }) {
      // Allow sign in for Google users with verified emails
      if (account?.provider === "google") {
        return profile?.email_verified === true;
      }

      // Allow sign in for Twitter users
      if (account?.provider === "twitter") {
        return true;
      }

      return true;
    },
    async session({ session, user }) {
      // Send properties to the client
      if (session.user) {
        session.user.id = user.id;
        // Add any additional user properties you want to expose
        session.user.timezone = user.timezone || 'UTC';
        session.user.theme = user.theme || 'dark';
        session.user.defaultModel = user.defaultModel || 'gpt-4';
      }
      return session;
    },
    async jwt({ token, user, account, profile }) {
      // Persist the OAuth access_token and refresh_token to the token right after signin
      if (account) {
        token.accessToken = account.access_token;
        token.refreshToken = account.refresh_token;
        token.provider = account.provider;
      }

      if (user) {
        token.id = user.id;
      }

      return token;
    }
  },
  pages: {
    signIn: '/auth/signin',
    signOut: '/auth/signout',
    error: '/auth/error',
  },
  session: {
    strategy: "database",
    maxAge: 30 * 24 * 60 * 60, // 30 days
    updateAge: 24 * 60 * 60, // 24 hours
  },
  events: {
    async signIn({ user, account, profile, isNewUser }) {
      console.log(`User ${user.email} signed in with ${account?.provider}`);

      // Update user preferences on sign in
      if (user.id) {
        await prisma.user.update({
          where: { id: user.id },
          data: {
            lastTokenReset: new Date(),
          }
        });
      }
    },
    async createUser({ user }) {
      console.log(`New user created: ${user.email}`);

      // Set default user preferences for new users
      if (user.id) {
        await prisma.user.update({
          where: { id: user.id },
          data: {
            timezone: 'UTC',
            theme: 'dark',
            notifications: true,
            defaultModel: 'gpt-4',
            fallbackModel: 'gemini-pro',
            maxTokensPerDay: 10000,
            tokensUsedToday: 0,
            lastTokenReset: new Date(),
          }
        });
      }
    }
  },
  debug: process.env.NODE_ENV === 'development',
});
