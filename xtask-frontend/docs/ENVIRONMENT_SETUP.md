# Environment Configuration Guide

This guide covers setting up all environment variables for the XTask platform, including OAuth credentials, AI API keys, and service configurations.

## 🔑 Required Environment Variables

### 1. Database Configuration
```env
# Neon PostgreSQL (Required)
DATABASE_URL=postgresql://username:<EMAIL>/dbname?sslmode=require&pgbouncer=true
```

### 2. Authentication & Security
```env
# JWT & Session Secrets (Required - Generate strong secrets)
JWT_SECRET=your-super-secure-jwt-secret-at-least-32-characters
SESSION_SECRET=your-super-secure-session-secret-at-least-32-characters

# Server Configuration
NODE_ENV=development
PORT=3030
```

### 3. OAuth Providers (Required for Authentication)

#### Google OAuth
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `http://localhost:3030/api/auth/google/callback` (development)
   - `https://yourdomain.com/api/auth/google/callback` (production)

```env
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=GOCSPX-your-google-client-secret
```

#### Twitter/X OAuth
1. Go to [Twitter Developer Portal](https://developer.twitter.com/)
2. Create a new app
3. Generate OAuth 2.0 credentials
4. Add callback URLs:
   - `http://localhost:3030/api/auth/twitter/callback` (development)
   - `https://yourdomain.com/api/auth/twitter/callback` (production)

```env
TWITTER_CLIENT_ID=your-twitter-client-id
TWITTER_CLIENT_SECRET=your-twitter-client-secret
```

## 🤖 AI Provider API Keys (Choose Your Providers)

### OpenAI (Recommended)
```env
OPENAI_API_KEY=sk-your-openai-api-key
```

### Google Gemini
```env
GEMINI_API_KEY=your-gemini-api-key
# OR
GOOGLE_API_KEY=your-google-api-key
```

### Additional AI Providers (Optional)
```env
# Mistral AI
MISTRAL_API_KEY=your-mistral-api-key

# Hugging Face
HUGGINGFACE_API_KEY=hf_your-huggingface-token

# Groq
GROQ_API_KEY=gsk_your-groq-api-key

# OpenRouter (Access to multiple models)
OPENROUTER_API_KEY=sk-or-v1-your-openrouter-key
```

## 📁 Media Upload Configuration

### UploadThing (Required for Media)
1. Go to [UploadThing Dashboard](https://uploadthing.com/)
2. Create a new app
3. Get your API keys

```env
UPLOADTHING_SECRET=sk_live_your-uploadthing-secret
UPLOADTHING_APP_ID=your-app-id
UPLOADTHING_TOKEN=your-uploadthing-token
```

## 🔧 Development Configuration

```env
# Logging & Debugging
DEBUG=false
LOG_LEVEL=info

# Development overrides
NODE_ENV=development
```

## 🚀 Quick Setup Steps

1. **Copy the example file:**
   ```bash
   cp .env.example .env
   ```

2. **Fill in your credentials:**
   - Update database URL from Neon
   - Add OAuth credentials from Google & Twitter
   - Add at least one AI provider API key
   - Configure UploadThing for media uploads

3. **Validate your configuration:**
   ```bash
   bun run validate-env
   ```

4. **Test the setup:**
   ```bash
   bun run dev
   curl http://localhost:3030/api/health
   ```

## 🔒 Security Best Practices

### Environment Files
- ✅ **DO**: Use `.env` for local development
- ✅ **DO**: Use platform environment variables for production
- ❌ **DON'T**: Commit `.env` files to version control
- ❌ **DON'T**: Share API keys in plain text

### Secret Generation
```bash
# Generate secure secrets
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
```

### Production Deployment
- Use your hosting platform's environment variable system
- Rotate secrets regularly
- Use different credentials for different environments
- Monitor API usage and set up alerts

## 🧪 Environment Validation

The system automatically validates your environment on startup:

```typescript
import { checkEnvironmentHealth } from '@/lib/env';

const health = checkEnvironmentHealth();
console.log('Environment Status:', health);
```

## 🔍 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check DATABASE_URL format
   - Ensure Neon database is active
   - Verify pgbouncer=true parameter

2. **OAuth Errors**
   - Verify redirect URIs match exactly
   - Check client ID/secret are correct
   - Ensure OAuth apps are enabled

3. **AI API Errors**
   - Verify API keys are valid
   - Check API quotas and billing
   - Test with simple requests first

### Environment Health Check
```bash
# Check all environment variables
curl http://localhost:3030/api/health

# Validate specific features
bun run validate-env
```

## 📋 Environment Checklist

- [ ] Database URL configured and tested
- [ ] JWT/Session secrets generated (32+ characters)
- [ ] Google OAuth credentials added
- [ ] Twitter OAuth credentials added
- [ ] At least one AI provider configured
- [ ] UploadThing credentials added
- [ ] Environment validation passes
- [ ] Health check returns success

Your XTask platform is ready when all items are checked! ✅
