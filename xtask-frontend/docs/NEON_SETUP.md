# Neon PostgreSQL Setup Guide

This guide will help you set up a Neon PostgreSQL database for the XTask platform.

## 🚀 Quick Setup

### 1. Create Neon Account
1. Go to [https://console.neon.tech/](https://console.neon.tech/)
2. Sign up for a free account
3. Create a new project

### 2. Get Database Connection String
1. In your Neon dashboard, go to your project
2. Navigate to "Connection Details"
3. Copy the connection string (it looks like this):
   ```
   postgresql://username:<EMAIL>/dbname?sslmode=require
   ```

### 3. Update Environment Variables
1. Open your `.env` file in the `xtask-frontend` directory
2. Replace the `DATABASE_URL` with your Neon connection string:
   ```env
   DATABASE_URL="postgresql://your-username:<EMAIL>/your-dbname?sslmode=require"
   ```

### 4. Run Database Migrations
```bash
# Navigate to the frontend directory
cd xtask-frontend

# Run the initial migration to create all tables
bun prisma migrate dev --name init

# Generate the Prisma client (if not already done)
bun prisma generate
```

### 5. Verify Setup
```bash
# Test the database connection
bun run db:test

# Check database health via API
curl http://localhost:3030/api/health
```

## 📊 Database Schema

The XTask database includes the following models:

### Core Models
- **User** - User accounts and preferences
- **Agent** - AI agents with personality configurations
- **TwitterAccount** - Connected social media accounts
- **ScheduledTweet** - Scheduled content and metadata
- **AgentMemory** - AI agent memories with vector embeddings
- **MediaFile** - Uploaded media files and metadata

### Key Features
- **Vector Embeddings**: AgentMemory model supports pgvector for semantic search
- **Thread Support**: ScheduledTweet supports Twitter thread composition
- **Performance Tracking**: Built-in engagement metrics
- **Media Management**: Integrated with UploadThing for file storage

## 🔧 Useful Commands

```bash
# Database operations
bun run db:generate    # Generate Prisma client
bun run db:migrate     # Run migrations
bun run db:reset       # Reset database (careful!)
bun run db:studio      # Open Prisma Studio
bun run db:test        # Test database connection

# Development
bun run dev            # Start development server
bun run build          # Build for production
```

## 🔍 Troubleshooting

### Connection Issues
- Ensure your Neon database is active (not paused)
- Check that the connection string includes `?sslmode=require`
- Verify your IP is allowed (Neon allows all IPs by default)

### Migration Issues
- Make sure DATABASE_URL is correctly set
- Check that your Neon database is accessible
- Try running `bun prisma db push` for development

### pgvector Extension
- pgvector is automatically available in Neon
- No manual installation required
- Vector embeddings are stored as Bytes in the database

## 📚 Additional Resources

- [Neon Documentation](https://neon.tech/docs)
- [Prisma Documentation](https://www.prisma.io/docs)
- [pgvector Documentation](https://github.com/pgvector/pgvector)

## 🎯 Next Steps

After setting up Neon:
1. Run the initial migration
2. Test the connection
3. Start implementing authentication (Task #6)
4. Begin building AI agent features (Task #11)
