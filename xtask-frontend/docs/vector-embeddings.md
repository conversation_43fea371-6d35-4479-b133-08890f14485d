# Vector Embeddings for Agent Memory

This document describes the vector embeddings system implemented for XTask's AI agent memory functionality.

## Overview

The vector embeddings system enables semantic search and similarity matching for agent memories using OpenAI's text-embedding-3-large model and PostgreSQL's pgvector extension.

## Architecture

### Components

1. **EmbeddingService** (`src/services/embeddings/index.ts`)
   - Handles OpenAI API integration
   - Generates embeddings for text content
   - Provides utility functions for vector operations

2. **AgentMemoryService** (`src/services/memory/agent-memory.ts`)
   - Manages agent memory storage and retrieval
   - Integrates with embedding service
   - Provides similarity search functionality

3. **Database Schema** (`prisma/schema.prisma`)
   - AgentMemory model with embedding field
   - pgvector indexes for performance

4. **API Endpoints** (`src/app/api/agents/[agentId]/memories/`)
   - RESTful API for memory management
   - Search endpoints for similarity queries

## Configuration

### Embedding Model Selection

After comprehensive research, we selected **OpenAI text-embedding-3-large** with the following configuration:

- **Model**: `text-embedding-3-large`
- **Dimensions**: 1024 (optimized for pgvector performance)
- **Max Tokens**: 8,191
- **Batch Size**: 100

### Rationale

| Model | Dimensions | Performance | Cost | Rate Limits |
|-------|------------|-------------|------|-------------|
| text-embedding-3-small | 1536 | Good | Low | 3M TPM |
| text-embedding-3-large | 3072/1024 | Excellent | Medium | 3M TPM |
| Google embedding-001 | 768 | Good | Low | 1K QPM |
| Cohere embed-english-v3.0 | 1024 | Good | Medium | 10K QPM |

**Selected**: text-embedding-3-large with 1024 dimensions for optimal balance of performance and efficiency.

## Database Setup

### pgvector Extension

```sql
-- Enable pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;
```

### Indexes

```sql
-- Main vector similarity index (cosine distance)
CREATE INDEX CONCURRENTLY agent_memories_embedding_cosine_idx 
ON agent_memories USING ivfflat (embedding::vector(1024) vector_cosine_ops) 
WITH (lists = 100);

-- Supporting indexes for filtering
CREATE INDEX CONCURRENTLY agent_memories_agent_type_importance_idx 
ON agent_memories (agentId, type, importance DESC);

CREATE INDEX CONCURRENTLY agent_memories_embedding_not_null_idx 
ON agent_memories (agentId) WHERE embedding IS NOT NULL;

-- GIN index for tag searches
CREATE INDEX CONCURRENTLY agent_memories_tags_gin_idx 
ON agent_memories USING gin (tags);
```

## Usage Examples

### Basic Embedding Generation

```typescript
import { embeddingService } from '@/services/embeddings'

// Generate single embedding
const result = await embeddingService.generateEmbedding(
  "User prefers posting about technology"
)

console.log(result.embedding) // [0.1, 0.2, 0.3, ...]
console.log(result.dimensions) // 1024
console.log(result.tokens) // 7
```

### Batch Embedding Generation

```typescript
const texts = [
  "User likes tech content",
  "Best posting time is morning",
  "Audience prefers tutorials"
]

const batchResult = await embeddingService.generateBatchEmbeddings(texts)
console.log(batchResult.embeddings.length) // 3
console.log(batchResult.totalTokens) // Total tokens used
```

### Memory Storage

```typescript
import { agentMemoryService } from '@/services/memory/agent-memory'

// Create memory with embedding
const memory = await agentMemoryService.createMemory({
  agentId: 'agent-123',
  content: 'User prefers educational content over promotional posts',
  type: 'preference',
  importance: 0.9,
  tags: ['content', 'preferences'],
  generateEmbedding: true
})
```

### Similarity Search

```typescript
// Search by semantic similarity
const results = await agentMemoryService.searchMemoriesBySimilarity(
  'agent-123',
  'What content does the user like?',
  {
    limit: 5,
    threshold: 0.7,
    type: 'preference'
  }
)

results.forEach(result => {
  console.log(`[${result.similarity}] ${result.content}`)
})
```

## API Endpoints

### Memory Management

- `GET /api/agents/[agentId]/memories` - List memories
- `POST /api/agents/[agentId]/memories` - Create memory
- `GET /api/agents/[agentId]/memories/[memoryId]` - Get specific memory
- `PUT /api/agents/[agentId]/memories/[memoryId]` - Update memory
- `DELETE /api/agents/[agentId]/memories/[memoryId]` - Delete memory

### Search

```bash
# Similarity search
GET /api/agents/agent-123/memories?query=content preferences&limit=5&threshold=0.7

# Text search (fallback)
GET /api/agents/agent-123/memories?query=content&useTextSearch=true
```

### Batch Operations

```bash
# Create multiple memories
POST /api/agents/agent-123/memories/batch
{
  "memories": [
    {
      "content": "Memory 1",
      "type": "preference",
      "importance": 0.8
    },
    {
      "content": "Memory 2",
      "type": "interaction",
      "importance": 0.6
    }
  ]
}
```

### Statistics

```bash
# Get memory statistics
GET /api/agents/agent-123/memories/stats
```

## Performance Optimization

### Vector Index Tuning

The IVFFlat index uses `lists = 100` parameter, optimized for:
- Dataset size: 1K-100K memories per agent
- Query performance: <100ms for similarity search
- Index build time: Reasonable for production use

### Batch Processing

- Use batch embedding generation for multiple memories
- Batch size limited to 100 items per request
- Automatic retry logic for rate limiting

### Caching Strategy

- Embeddings are cached in database (no re-generation)
- Consider Redis caching for frequently accessed memories
- Implement embedding versioning for model updates

## Error Handling

### Embedding Errors

```typescript
try {
  const result = await embeddingService.generateEmbedding(text)
} catch (error) {
  if (error instanceof EmbeddingRateLimitError) {
    // Wait and retry
    await new Promise(resolve => setTimeout(resolve, error.retryAfter * 1000))
  } else if (error instanceof EmbeddingError) {
    // Handle specific embedding errors
    console.error('Embedding failed:', error.code, error.message)
  }
}
```

### Graceful Degradation

- If embedding generation fails, memories are stored without embeddings
- Text search is used as fallback when similarity search is unavailable
- System continues to function with reduced semantic capabilities

## Testing

### Setup Scripts

```bash
# Setup pgvector and indexes
bun run scripts/setup-pgvector.ts

# Test basic embedding functionality
bun run scripts/test-basic-embeddings.ts

# Run comprehensive tests
bun run scripts/test-embeddings.ts

# Performance benchmarking
bun run scripts/benchmark-embeddings.ts
```

### Test Coverage

- Embedding generation (single and batch)
- Memory storage with embeddings
- Similarity search accuracy
- Performance benchmarks
- Error handling scenarios

## Monitoring

### Key Metrics

- Embedding generation latency
- Similarity search performance
- Memory storage throughput
- API error rates
- Token usage and costs

### Alerts

- High embedding API error rates
- Slow similarity search queries (>500ms)
- Database connection issues
- Unusual token consumption

## Security Considerations

- API keys stored securely in environment variables
- User authentication required for all memory operations
- Agent ownership verification for memory access
- Input validation and sanitization
- Rate limiting on API endpoints

## Future Enhancements

1. **Multi-model Support**: Add support for other embedding providers
2. **Embedding Versioning**: Handle model updates and migrations
3. **Advanced Search**: Implement hybrid search (vector + text)
4. **Memory Clustering**: Group related memories automatically
5. **Cost Optimization**: Implement embedding caching and deduplication
6. **Real-time Updates**: WebSocket support for live memory updates

## Troubleshooting

### Common Issues

1. **pgvector not enabled**: Run setup script or enable manually
2. **Slow similarity search**: Check if vector indexes exist
3. **Embedding API errors**: Verify OpenAI API key and rate limits
4. **Memory not found**: Check agent ownership and permissions

### Debug Commands

```bash
# Check pgvector status
SELECT * FROM pg_extension WHERE extname = 'vector';

# Check vector indexes
SELECT indexname FROM pg_indexes WHERE tablename = 'agent_memories';

# Test vector operations
SELECT '[1,0,0]'::vector <-> '[0,1,0]'::vector as distance;
```
