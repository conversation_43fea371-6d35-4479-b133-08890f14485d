import express from 'express';
import next from 'next';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import { getDatabaseHealth, testDatabaseConnection } from './src/lib/db';
import { checkEnvironmentHealth, getFeatureAvailability } from './src/lib/env';

const dev = process.env.NODE_ENV !== 'production';
const hostname = 'localhost';
const port = parseInt(process.env.PORT || '3030', 10);

// Initialize Next.js app
const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();

async function startServer() {
  try {
    console.log('🔄 Validating environment...');
    // Validate environment configuration
    const envHealth = checkEnvironmentHealth();
    if (!envHealth.valid) {
      console.error('❌ Environment validation failed:');
      envHealth.errors.forEach(error => {
        console.error(`  - ${error.field}: ${error.message}`);
      });
      throw new Error('Invalid environment configuration');
    }
    console.log('✅ Environment validation passed');

    console.log('🔄 Preparing Next.js...');
    // Prepare Next.js
    await app.prepare();
    console.log('✅ Next.js prepared successfully');

    // Create Express server
    const server = express();

    // Security middleware
    server.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'", "'unsafe-eval'", "'unsafe-inline'"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'", "ws:", "wss:"],
          fontSrc: ["'self'", "https:", "data:"],
        },
      },
    }));

    // CORS configuration
    server.use(cors({
      origin: dev ? 'http://localhost:3030' : process.env.FRONTEND_URL,
      credentials: true,
    }));

    // Compression and logging
    server.use(compression());
    server.use(morgan(dev ? 'dev' : 'combined'));

    // Parse JSON bodies
    server.use(express.json({ limit: '10mb' }));
    server.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // API Routes
    server.get('/api/health', async (req, res) => {
      try {
        const dbHealth = await getDatabaseHealth();
        const envHealth = checkEnvironmentHealth();
        const features = getFeatureAvailability();

        res.json({
          status: 'ok',
          timestamp: new Date().toISOString(),
          environment: process.env.NODE_ENV || 'development',
          database: dbHealth,
          configuration: {
            valid: envHealth.valid,
            features,
            aiProviders: envHealth.aiProviders.length,
            summary: envHealth.summary
          }
        });
      } catch (error) {
        res.status(500).json({
          status: 'error',
          timestamp: new Date().toISOString(),
          environment: process.env.NODE_ENV || 'development',
          database: { status: 'unhealthy', error: 'Connection failed' },
          configuration: { valid: false, error: 'Configuration check failed' }
        });
      }
    });

    // Test route for Express integration
    server.get('/api/hello', (req, res) => {
      res.json({
        message: 'Hello from Express.js + Next.js!',
        server: 'Express.js',
        framework: 'Next.js 15.3.3'
      });
    });

    // Handle all other requests with Next.js
    server.all('*', (req, res) => {
      return handle(req, res);
    });

    // Start the server
    server.listen(port, () => {
      console.log(`🚀 Server ready at http://${hostname}:${port}`);
      console.log(`📱 Frontend: Next.js 15.3.3`);
      console.log(`⚡ Backend: Express.js ${express.version || '5.x'}`);
      console.log(`🔧 Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`📦 Package Manager: Bun`);
    });

  } catch (error) {
    console.error('❌ Error starting server:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Start the server
startServer();
