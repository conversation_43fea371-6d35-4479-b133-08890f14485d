import { generateComponents } from "@uploadthing/react";
import { generateReactHelpers } from "@uploadthing/react/hooks";

import type { OurFileRouter } from "@/app/api/uploadthing/core";

/**
 * Pre-built UploadThing components
 * These are typed to your file router and ready to use
 */
export const { UploadButton, UploadDropzone, Uploader } =
  generateComponents<OurFileRouter>();

/**
 * UploadThing React hooks
 * Use these for custom upload implementations
 */
export const { useUploadThing } = generateReactHelpers<OurFileRouter>();

/**
 * Utility function to get file type from MIME type
 */
export function getFileType(mimeType: string): 'image' | 'video' | 'unknown' {
  if (mimeType.startsWith('image/')) return 'image';
  if (mimeType.startsWith('video/')) return 'video';
  return 'unknown';
}

/**
 * Utility function to format file size
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Utility function to validate file before upload
 */
export function validateFile(file: File, maxSize: number = 4 * 1024 * 1024): string | null {
  // Check file size
  if (file.size > maxSize) {
    return `File size must be less than ${formatFileSize(maxSize)}`;
  }
  
  // Check file type
  const fileType = getFileType(file.type);
  if (fileType === 'unknown') {
    return 'File type not supported. Please upload an image or video file.';
  }
  
  return null; // No errors
}

/**
 * Upload progress callback type
 */
export type UploadProgressCallback = (progress: number) => void;

/**
 * Upload error callback type
 */
export type UploadErrorCallback = (error: Error) => void;

/**
 * Upload success callback type
 */
export type UploadSuccessCallback = (result: any) => void;
