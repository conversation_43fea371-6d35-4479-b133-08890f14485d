import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export default function Home() {
  return (
    <div className="min-h-screen bg-dark-bg">
      {/* Hero Section */}
      <div className="container mx-auto px-4 py-16">
        <div className="text-center max-w-4xl mx-auto">
          <h1 className="text-5xl font-bold text-white mb-6">
            Welcome to{" "}
            <span className="bg-gradient-primary bg-clip-text text-transparent">
              XTask
            </span>
          </h1>
          <p className="text-xl text-gray-400 mb-8">
            AI-powered social media management platform. Create intelligent agents,
            schedule content, and analyze performance with cutting-edge automation.
          </p>

          <div className="flex gap-4 justify-center mb-12">
            <Button size="lg" className="px-8">
              Get Started
            </Button>
            <Button variant="outline" size="lg" className="px-8">
              Learn More
            </Button>
          </div>

          <div className="flex gap-2 justify-center flex-wrap">
            <Badge variant="primary">AI Agents</Badge>
            <Badge variant="success">Smart Scheduling</Badge>
            <Badge variant="info">Analytics</Badge>
            <Badge variant="warning">Multi-Platform</Badge>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="container mx-auto px-4 py-16">
        <h2 className="text-3xl font-bold text-white text-center mb-12">
          Powerful Features
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card hover className="p-6">
            <CardHeader>
              <CardTitle>AI Agents</CardTitle>
              <CardDescription>
                Create intelligent agents with unique personas to generate authentic content
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-gray-400 space-y-2">
                <li>• Custom personality traits</li>
                <li>• Multi-provider AI support</li>
                <li>• Context-aware generation</li>
              </ul>
            </CardContent>
          </Card>

          <Card hover className="p-6">
            <CardHeader>
              <CardTitle>Smart Scheduling</CardTitle>
              <CardDescription>
                Advanced scheduling with optimal timing suggestions and bulk operations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-gray-400 space-y-2">
                <li>• Optimal time recommendations</li>
                <li>• Bulk scheduling</li>
                <li>• Timezone support</li>
              </ul>
            </CardContent>
          </Card>

          <Card hover className="p-6">
            <CardHeader>
              <CardTitle>Analytics Dashboard</CardTitle>
              <CardDescription>
                Comprehensive analytics with performance tracking and insights
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-gray-400 space-y-2">
                <li>• Engagement metrics</li>
                <li>• Growth tracking</li>
                <li>• Performance insights</li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
      {/* Footer */}
      <footer className="border-t border-dark-border py-8">
        <div className="container mx-auto px-4 text-center text-gray-400">
          <p>&copy; 2024 XTask. Built with Next.js 15 and modern technologies.</p>
        </div>
      </footer>
    </div >
  );
}
