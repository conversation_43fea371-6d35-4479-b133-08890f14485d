import TweetComposer from '@/components/compose/TweetComposer';

export default function TestComposerPage() {
  return (
    <div className="min-h-screen bg-dark-bg text-dark-text">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2">Rich Text Tweet Composer Test</h1>
            <p className="text-gray-400">
              Test the TipTap-based rich text editor with UploadThing media integration.
              Features include real-time character counting, media uploads, and text formatting.
            </p>
          </div>
          
          <div className="space-y-8">
            {/* Main Composer */}
            <div>
              <h2 className="text-xl font-semibold mb-4">Tweet Composer</h2>
              <TweetComposer
                mode="compose"
                onPublish={async (data) => {
                  console.log('Publishing tweet:', data);
                  alert('Tweet published! (Check console for data)');
                }}
                onSaveDraft={async (data) => {
                  console.log('Saving draft:', data);
                  alert('Draft saved! (Check console for data)');
                }}
              />
            </div>

            {/* Schedule Mode */}
            <div>
              <h2 className="text-xl font-semibold mb-4">Schedule Mode</h2>
              <TweetComposer
                mode="schedule"
                initialContent="This is a scheduled tweet with initial content!"
                onSchedule={async (data, scheduledAt) => {
                  console.log('Scheduling tweet:', data, 'for:', scheduledAt);
                  alert('Tweet scheduled! (Check console for data)');
                }}
                onSaveDraft={async (data) => {
                  console.log('Saving draft:', data);
                  alert('Draft saved! (Check console for data)');
                }}
              />
            </div>

            {/* Draft Mode */}
            <div>
              <h2 className="text-xl font-semibold mb-4">Draft Mode</h2>
              <TweetComposer
                mode="draft"
                initialContent="This is a draft tweet being edited..."
                agentId="test-agent-123"
                onPublish={async (data) => {
                  console.log('Publishing draft:', data);
                  alert('Draft published! (Check console for data)');
                }}
                onSaveDraft={async (data) => {
                  console.log('Updating draft:', data);
                  alert('Draft updated! (Check console for data)');
                }}
                onCancel={() => {
                  alert('Draft editing cancelled');
                }}
              />
            </div>
          </div>
          
          <div className="mt-8 p-4 bg-dark-surface rounded-lg border border-dark-border">
            <h2 className="text-lg font-semibold mb-2">Feature Overview</h2>
            <ul className="text-sm text-gray-400 space-y-1">
              <li>• <strong>Rich Text Editing:</strong> Bold, italic, links with TipTap</li>
              <li>• <strong>Character Counter:</strong> Real-time counting with visual feedback</li>
              <li>• <strong>Media Upload:</strong> Images and videos via UploadThing (max 4 files)</li>
              <li>• <strong>Media Preview:</strong> Responsive grid with remove functionality</li>
              <li>• <strong>Mentions & Hashtags:</strong> Quick insertion buttons</li>
              <li>• <strong>Form Validation:</strong> Zod schema with React Hook Form</li>
              <li>• <strong>Multiple Modes:</strong> Compose, Schedule, Draft editing</li>
              <li>• <strong>Error Handling:</strong> Upload errors and validation feedback</li>
              <li>• <strong>Accessibility:</strong> Keyboard navigation and screen reader support</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
