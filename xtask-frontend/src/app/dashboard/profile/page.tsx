'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { toast } from 'sonner'
import { useEffect, useState } from 'react'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'

// Zod schema for profile form validation
const profileFormSchema = z.object({
  name: z.string().min(2, {
    message: 'Name must be at least 2 characters.',
  }),
  email: z.string().email({
    message: 'Please enter a valid email address.',
  }),
  timezone: z.string().min(1, {
    message: 'Please select a timezone.',
  }),
  notifications: z.boolean(),
})

type ProfileFormValues = z.infer<typeof profileFormSchema>

interface User {
  id: string
  name: string
  email: string
  timezone?: string
  notifications?: boolean
  twitterAccounts?: Array<{
    id: string
    username: string
    displayName: string
    avatar?: string
    isActive: boolean
  }>
  accounts?: Array<{
    id: string
    provider: string
    providerAccountId: string
  }>
}

export default function ProfilePage() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState(false)

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      name: '',
      email: '',
      timezone: '',
      notifications: true,
    },
  })

  // Fetch user data on component mount
  useEffect(() => {
    async function fetchUser() {
      try {
        const response = await fetch('/api/auth/me')
        if (response.ok) {
          const userData = await response.json()
          setUser(userData)

          // Update form with fetched data
          form.reset({
            name: userData.name || '',
            email: userData.email || '',
            timezone: userData.timezone || '',
            notifications: userData.notifications ?? true,
          })
        } else {
          toast.error('Failed to load profile data')
        }
      } catch (error) {
        console.error('Error fetching user:', error)
        toast.error('Failed to load profile data')
      } finally {
        setLoading(false)
      }
    }

    fetchUser()
  }, [form])

  async function onSubmit(data: ProfileFormValues) {
    setUpdating(true)
    try {
      const response = await fetch('/api/auth/me', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (response.ok) {
        const updatedUser = await response.json()
        setUser(updatedUser)
        toast.success('Profile updated successfully!')
      } else {
        const error = await response.json()
        toast.error(error.message || 'Failed to update profile')
      }
    } catch (error) {
      console.error('Error updating profile:', error)
      toast.error('Failed to update profile')
    } finally {
      setUpdating(false)
    }
  }

  async function handleDisconnectAccount(accountId: string, provider: string) {
    try {
      const response = await fetch('/api/auth/accounts', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ accountId, provider }),
      })

      if (response.ok) {
        toast.success(`${provider} account disconnected`)
        // Refresh user data
        const userResponse = await fetch('/api/auth/me')
        if (userResponse.ok) {
          const userData = await userResponse.json()
          setUser(userData)
        }
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to disconnect account')
      }
    } catch (error) {
      console.error('Error disconnecting account:', error)
      toast.error('Failed to disconnect account')
    }
  }

  function handleConnectAccount(provider: string) {
    // Redirect to OAuth flow
    window.location.href = `/api/auth/${provider}`
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-white">Profile Settings</h1>
          <p className="text-gray-400 mt-2">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-white">Profile Settings</h1>
        <p className="text-gray-400 mt-2">
          Manage your account settings and connected social accounts.
        </p>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Profile Form */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Profile Information</CardTitle>
              <CardDescription>
                Update your personal information and preferences.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Full Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter your full name" {...field} />
                        </FormControl>
                        <FormDescription>
                          This is your public display name.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email Address</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter your email" type="email" {...field} />
                        </FormControl>
                        <FormDescription>
                          We'll use this email for important notifications.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="timezone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Timezone</FormLabel>
                        <FormControl>
                          <Input placeholder="Select your timezone" {...field} />
                        </FormControl>
                        <FormDescription>
                          Used for scheduling posts at optimal times.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="notifications"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border border-dark-border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Email Notifications
                          </FormLabel>
                          <FormDescription>
                            Receive email notifications about your account activity.
                          </FormDescription>
                        </div>
                        <FormControl>
                          <input
                            type="checkbox"
                            checked={field.value}
                            onChange={field.onChange}
                            className="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <Button type="submit" disabled={updating}>
                    {updating ? 'Updating...' : 'Update Profile'}
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>

        {/* Connected Accounts */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Connected Accounts</CardTitle>
              <CardDescription>
                Manage your social media connections.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Twitter Accounts */}
              {user?.twitterAccounts?.map((account) => (
                <div key={account.id} className="flex items-center justify-between p-4 border border-dark-border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={account.avatar} alt="Twitter" />
                      <AvatarFallback>T</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="text-sm font-medium text-white">Twitter</p>
                      <p className="text-xs text-gray-400">@{account.username}</p>
                      <p className="text-xs text-gray-500">{account.displayName}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant={account.isActive ? "success" : "secondary"}>
                      {account.isActive ? "Active" : "Inactive"}
                    </Badge>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDisconnectAccount(account.id, 'twitter')}
                    >
                      Disconnect
                    </Button>
                  </div>
                </div>
              ))}

              {/* OAuth Accounts */}
              {user?.accounts?.map((account) => (
                <div key={account.id} className="flex items-center justify-between p-4 border border-dark-border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-10 w-10">
                      <AvatarFallback>
                        {account.provider.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="text-sm font-medium text-white capitalize">
                        {account.provider}
                      </p>
                      <p className="text-xs text-gray-400">
                        ID: {account.providerAccountId}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="success">Connected</Badge>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDisconnectAccount(account.id, account.provider)}
                    >
                      Disconnect
                    </Button>
                  </div>
                </div>
              ))}

              {/* Show message if no accounts */}
              {(!user?.twitterAccounts?.length && !user?.accounts?.length) && (
                <div className="text-center py-8">
                  <p className="text-gray-400">No connected accounts</p>
                  <p className="text-sm text-gray-500 mt-1">
                    Connect your social media accounts to get started
                  </p>
                </div>
              )}

              <Separator />

              <div className="space-y-2">
                <h4 className="text-sm font-medium text-white">Add New Account</h4>
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleConnectAccount('twitter')}
                  >
                    + Twitter
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleConnectAccount('google')}
                  >
                    + Google
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
