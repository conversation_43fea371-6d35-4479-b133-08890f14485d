'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useRouter } from 'next/navigation'

export default function SettingsPage() {
  const router = useRouter()

  const settingsCategories = [
    {
      title: 'Profile Settings',
      description: 'Manage your personal information, preferences, and account details.',
      icon: '👤',
      href: '/dashboard/settings/profile',
    },
    {
      title: 'Connected Accounts',
      description: 'Manage your connected social media accounts and OAuth providers.',
      icon: '🔗',
      href: '/dashboard/settings/accounts',
    },
    {
      title: 'AI Configuration',
      description: 'Configure AI models, API keys, and generation preferences.',
      icon: '🤖',
      href: '/dashboard/settings/ai',
    },
    {
      title: 'Billing & Usage',
      description: 'View your usage statistics, billing information, and subscription details.',
      icon: '💳',
      href: '/dashboard/settings/billing',
    },
  ]

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Settings</h1>
        <p className="text-muted-foreground">
          Manage your account settings and preferences.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {settingsCategories.map((category) => (
          <Card key={category.href} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <span className="text-2xl">{category.icon}</span>
                {category.title}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-muted-foreground">{category.description}</p>
              <Button
                onClick={() => router.push(category.href)}
                className="w-full"
                variant="outline"
              >
                Manage {category.title}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
