"use client"

import { useState } from "react"
import { useQuery } from "@tanstack/react-query"
import { Brain, Zap, DollarSign, RefreshCw } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ProviderConfig } from "@/components/ai/ProviderConfig"
import { ModelSelector } from "@/components/ai/ModelSelector"
import { Spinner } from "@/components/ui/spinner"

export default function AISettingsPage() {
  const [refreshing, setRefreshing] = useState(false)

  // Fetch AI provider status
  const { data: statusData, isLoading, refetch } = useQuery({
    queryKey: ['ai-provider-status'],
    queryFn: async () => {
      const response = await fetch('/api/ai/status')
      if (!response.ok) throw new Error('Failed to fetch AI status')
      return response.json()
    },
    refetchInterval: 30000, // Refresh every 30 seconds
  })

  const handleRefreshAll = async () => {
    setRefreshing(true)
    try {
      // Refresh models for all providers
      await fetch('/api/ai/models/refresh', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
      })
      
      // Refresh status
      await refetch()
    } catch (error) {
      console.error('Failed to refresh:', error)
    } finally {
      setRefreshing(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center space-y-4">
          <Spinner className="h-8 w-8 mx-auto" />
          <p className="text-muted-foreground">Loading AI settings...</p>
        </div>
      </div>
    )
  }

  const availableProviders = statusData?.availableProviders || []
  const providerStatuses = statusData?.providerStatuses || []
  const connectedCount = providerStatuses.filter((p: any) => p.available).length

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center space-x-2">
            <Brain className="h-8 w-8" />
            <span>AI Settings</span>
          </h1>
          <p className="text-muted-foreground mt-1">
            Configure AI providers and manage model settings
          </p>
        </div>
        
        <Button
          onClick={handleRefreshAll}
          disabled={refreshing}
          variant="outline"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh All
        </Button>
      </div>

      {/* Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Connected Providers</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{connectedCount}</div>
            <p className="text-xs text-muted-foreground">
              out of {providerStatuses.length} configured
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Available Models</CardTitle>
            <Brain className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statusData?.totalModels || 0}</div>
            <p className="text-xs text-muted-foreground">
              across all providers
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cost Optimization</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Active</div>
            <p className="text-xs text-muted-foreground">
              Dynamic model selection
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="providers" className="space-y-4">
        <TabsList>
          <TabsTrigger value="providers">Provider Configuration</TabsTrigger>
          <TabsTrigger value="models">Model Testing</TabsTrigger>
          <TabsTrigger value="defaults">Default Settings</TabsTrigger>
        </TabsList>

        {/* Provider Configuration Tab */}
        <TabsContent value="providers" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ProviderConfig
              provider="openai"
              title="OpenAI"
              description="Configure OpenAI GPT models (GPT-4, GPT-3.5, etc.)"
              apiKeyLabel="OpenAI API Key"
              baseUrlLabel="Custom OpenAI Endpoint"
              showBaseUrl={true}
            />

            <ProviderConfig
              provider="gemini"
              title="Google Gemini"
              description="Configure Google Gemini models (Gemini Pro, Flash, etc.)"
              apiKeyLabel="Gemini API Key"
              baseUrlLabel="Custom Gemini Endpoint"
              showBaseUrl={true}
            />

            <ProviderConfig
              provider="mistral"
              title="Mistral AI"
              description="Configure Mistral models (Mistral 7B, Mixtral, etc.)"
              apiKeyLabel="Mistral API Key"
              baseUrlLabel="Custom Mistral Endpoint"
              showBaseUrl={true}
            />

            <ProviderConfig
              provider="groq"
              title="Groq"
              description="Configure Groq models (Llama, Mixtral on Groq)"
              apiKeyLabel="Groq API Key"
              baseUrlLabel="Custom Groq Endpoint"
              showBaseUrl={true}
            />
          </div>
        </TabsContent>

        {/* Model Testing Tab */}
        <TabsContent value="models" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Test AI Models</CardTitle>
              <CardDescription>
                Select and test different AI models to see their capabilities
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">OpenAI Models</h3>
                  <ModelSelector
                    provider="openai"
                    placeholder="Select OpenAI model..."
                    onValueChange={(value) => console.log('OpenAI model:', value)}
                    showDetails={true}
                  />
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Gemini Models</h3>
                  <ModelSelector
                    provider="gemini"
                    placeholder="Select Gemini model..."
                    onValueChange={(value) => console.log('Gemini model:', value)}
                    showDetails={true}
                  />
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium">All Available Models</h3>
                <ModelSelector
                  placeholder="Select any model..."
                  onValueChange={(value) => console.log('Selected model:', value)}
                  showDetails={true}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Default Settings Tab */}
        <TabsContent value="defaults" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Default AI Settings</CardTitle>
              <CardDescription>
                Configure default settings for new agents and content generation
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Default Provider</h3>
                  <ModelSelector
                    placeholder="Select default model..."
                    onValueChange={(value) => console.log('Default model:', value)}
                    showDetails={false}
                  />
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Fallback Provider</h3>
                  <ModelSelector
                    placeholder="Select fallback model..."
                    onValueChange={(value) => console.log('Fallback model:', value)}
                    showDetails={false}
                  />
                </div>
              </div>

              <div className="pt-4">
                <Button>Save Default Settings</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Provider Status List */}
      <Card>
        <CardHeader>
          <CardTitle>Provider Status</CardTitle>
          <CardDescription>
            Real-time status of all configured AI providers
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {providerStatuses.map((provider: any) => (
              <div
                key={provider.name}
                className="flex items-center justify-between p-3 rounded-lg border"
              >
                <div className="flex items-center space-x-3">
                  <div className="font-medium">{provider.name}</div>
                  <Badge variant={provider.available ? "default" : "destructive"}>
                    {provider.available ? "Connected" : "Disconnected"}
                  </Badge>
                  {!provider.configured && (
                    <Badge variant="secondary">Not Configured</Badge>
                  )}
                </div>
                
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  {provider.responseTime && (
                    <span>{provider.responseTime}ms</span>
                  )}
                  {provider.lastError && (
                    <span className="text-destructive">Error</span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
