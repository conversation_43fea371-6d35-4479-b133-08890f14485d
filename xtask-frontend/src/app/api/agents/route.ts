import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { auth } from "@/auth"
import { prisma } from "@/lib/db"
import { withAuth, optionalAuth } from "@/lib/auth-middleware"

// Zod schema for agent validation
const createAgentSchema = z.object({
  name: z.string().min(1, "Name is required").max(100, "Name must be less than 100 characters"),
  description: z.string().optional(),
  avatar: z.string().url().optional(),
  personality: z.record(z.any()).optional(),
  writingStyle: z.record(z.any()).optional(),
  topics: z.array(z.string()).optional(),
  tone: z.string().optional(),
  creativity: z.number().min(0).max(1).optional(),
  preferredModel: z.string().optional(),
  temperature: z.number().min(0).max(1).optional(),
  maxTokens: z.number().int().positive().optional(),
  postingFrequency: z.string().optional(),
  preferredTimes: z.array(z.string()).optional(),
  timezone: z.string().optional(),
})

// GET /api/agents - List all agents for the authenticated user
export async function GET(request: NextRequest) {
  try {
    // Try JWT authentication first, fallback to NextAuth
    const { user: jwtUser } = await optionalAuth(request)
    let userId = jwtUser?.id

    // Fallback to NextAuth if JWT auth failed
    if (!userId) {
      const session = await auth()
      userId = session?.user?.id
    }

    if (!userId) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    const agents = await prisma.agent.findMany({
      where: {
        userId,
      },
      orderBy: {
        createdAt: "desc",
      },
      select: {
        id: true,
        name: true,
        description: true,
        avatar: true,
        isActive: true,
        totalTweets: true,
        totalEngagement: true,
        avgEngagement: true,
        createdAt: true,
        updatedAt: true,
      },
    })

    return NextResponse.json(agents)
  } catch (error) {
    console.error("Error fetching agents:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

// POST /api/agents - Create a new agent
export async function POST(request: NextRequest) {
  try {
    // Try JWT authentication first, fallback to NextAuth
    const { user: jwtUser } = await optionalAuth(request)
    let userId = jwtUser?.id

    // Fallback to NextAuth if JWT auth failed
    if (!userId) {
      const session = await auth()
      userId = session?.user?.id
    }

    if (!userId) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createAgentSchema.parse(body)

    const agent = await prisma.agent.create({
      data: {
        ...validatedData,
        userId,
        isActive: true,
        totalTweets: 0,
        totalEngagement: 0,
        avgEngagement: 0,
      },
    })

    return NextResponse.json(agent, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.flatten() },
        { status: 400 }
      )
    }

    console.error("Error creating agent:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}
