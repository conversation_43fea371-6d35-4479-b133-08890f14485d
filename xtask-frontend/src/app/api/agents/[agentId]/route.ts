import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { auth } from "@/auth"
import { prisma } from "@/lib/db"

// Zod schema for agent updates
const updateAgentSchema = z.object({
  name: z.string().min(1, "Name is required").max(100, "Name must be less than 100 characters").optional(),
  description: z.string().optional(),
  avatar: z.string().url().optional(),
  isActive: z.boolean().optional(),
  personality: z.record(z.any()).optional(),
  writingStyle: z.record(z.any()).optional(),
  topics: z.array(z.string()).optional(),
  tone: z.string().optional(),
  creativity: z.number().min(0).max(1).optional(),
  preferredModel: z.string().optional(),
  temperature: z.number().min(0).max(1).optional(),
  maxTokens: z.number().int().positive().optional(),
  postingFrequency: z.string().optional(),
  preferredTimes: z.array(z.string()).optional(),
  timezone: z.string().optional(),
})

// GET /api/agents/[agentId] - Get a specific agent
export async function GET(
  request: NextRequest,
  { params }: { params: { agentId: string } }
) {
  try {
    const session = await auth()

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    const agent = await prisma.agent.findFirst({
      where: {
        id: params.agentId,
        userId: session.user.id, // Ensure user can only access their own agents
      },
    })

    if (!agent) {
      return NextResponse.json({ message: "Agent not found" }, { status: 404 })
    }

    return NextResponse.json(agent)
  } catch (error) {
    console.error("Error fetching agent:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

// PUT /api/agents/[agentId] - Update a specific agent
export async function PUT(
  request: NextRequest,
  { params }: { params: { agentId: string } }
) {
  try {
    const session = await auth()

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = updateAgentSchema.parse(body)

    // First check if the agent exists and belongs to the user
    const existingAgent = await prisma.agent.findFirst({
      where: {
        id: params.agentId,
        userId: session.user.id,
      },
    })

    if (!existingAgent) {
      return NextResponse.json({ message: "Agent not found" }, { status: 404 })
    }

    const updatedAgent = await prisma.agent.update({
      where: {
        id: params.agentId,
      },
      data: validatedData,
    })

    return NextResponse.json(updatedAgent)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.flatten() },
        { status: 400 }
      )
    }

    console.error("Error updating agent:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

// DELETE /api/agents/[agentId] - Delete a specific agent
export async function DELETE(
  request: NextRequest,
  { params }: { params: { agentId: string } }
) {
  try {
    const session = await auth()

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    // First check if the agent exists and belongs to the user
    const existingAgent = await prisma.agent.findFirst({
      where: {
        id: params.agentId,
        userId: session.user.id,
      },
    })

    if (!existingAgent) {
      return NextResponse.json({ message: "Agent not found" }, { status: 404 })
    }

    await prisma.agent.delete({
      where: {
        id: params.agentId,
      },
    })

    return NextResponse.json({ message: "Agent deleted successfully" })
  } catch (error) {
    console.error("Error deleting agent:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}
