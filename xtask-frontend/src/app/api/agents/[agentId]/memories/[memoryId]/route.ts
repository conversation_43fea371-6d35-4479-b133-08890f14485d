import { NextRequest, NextResponse } from 'next/server'
import { auth } from '../../../../../../../auth'
import { agentMemoryService } from '@/services/memory/agent-memory'
import { prisma } from '@/lib/db'
import { z } from 'zod'

// Validation schema for memory updates
const updateMemorySchema = z.object({
  content: z.string().min(1).max(10000).optional(),
  context: z.string().optional(),
  type: z.string().optional(),
  importance: z.number().min(0).max(1).optional(),
  tags: z.array(z.string()).optional(),
  relatedTo: z.string().optional(),
  regenerateEmbedding: z.boolean().default(false),
})

// GET /api/agents/[agentId]/memories/[memoryId] - Get specific memory
export async function GET(
  request: NextRequest,
  { params }: { params: { agentId: string; memoryId: string } }
) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 })
    }

    const { agentId, memoryId } = params
    const { searchParams } = new URL(request.url)
    const includeEmbedding = searchParams.get('includeEmbedding') === 'true'

    // Verify agent ownership
    const agent = await prisma.agent.findFirst({
      where: {
        id: agentId,
        userId: session.user.id,
      },
    })

    if (!agent) {
      return NextResponse.json({ message: 'Agent not found' }, { status: 404 })
    }

    // Get memory
    const memory = await agentMemoryService.getMemory(memoryId, includeEmbedding)

    if (!memory) {
      return NextResponse.json({ message: 'Memory not found' }, { status: 404 })
    }

    // Verify memory belongs to the agent
    if (memory.agentId !== agentId) {
      return NextResponse.json({ message: 'Memory not found' }, { status: 404 })
    }

    return NextResponse.json({ memory })
  } catch (error: any) {
    console.error('Error fetching memory:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/agents/[agentId]/memories/[memoryId] - Update memory
export async function PUT(
  request: NextRequest,
  { params }: { params: { agentId: string; memoryId: string } }
) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 })
    }

    const { agentId, memoryId } = params
    const body = await request.json()

    // Verify agent ownership
    const agent = await prisma.agent.findFirst({
      where: {
        id: agentId,
        userId: session.user.id,
      },
    })

    if (!agent) {
      return NextResponse.json({ message: 'Agent not found' }, { status: 404 })
    }

    // Verify memory exists and belongs to agent
    const existingMemory = await prisma.agentMemory.findFirst({
      where: {
        id: memoryId,
        agentId,
      },
    })

    if (!existingMemory) {
      return NextResponse.json({ message: 'Memory not found' }, { status: 404 })
    }

    // Validate request body
    const validatedData = updateMemorySchema.parse(body)

    // Update memory
    const updatedMemory = await agentMemoryService.updateMemory(
      memoryId,
      validatedData,
      validatedData.regenerateEmbedding
    )

    if (!updatedMemory) {
      return NextResponse.json({ message: 'Failed to update memory' }, { status: 500 })
    }

    return NextResponse.json({
      message: 'Memory updated successfully',
      memory: {
        id: updatedMemory.id,
        content: updatedMemory.content,
        context: updatedMemory.context,
        type: updatedMemory.type,
        importance: updatedMemory.importance,
        tags: updatedMemory.tags,
        relatedTo: updatedMemory.relatedTo,
        embeddingModel: updatedMemory.embeddingModel,
        embeddingDim: updatedMemory.embeddingDim,
        createdAt: updatedMemory.createdAt,
        updatedAt: updatedMemory.updatedAt,
      },
    })
  } catch (error: any) {
    console.error('Error updating memory:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: 'Invalid request data', errors: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/agents/[agentId]/memories/[memoryId] - Delete memory
export async function DELETE(
  request: NextRequest,
  { params }: { params: { agentId: string; memoryId: string } }
) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 })
    }

    const { agentId, memoryId } = params

    // Verify agent ownership
    const agent = await prisma.agent.findFirst({
      where: {
        id: agentId,
        userId: session.user.id,
      },
    })

    if (!agent) {
      return NextResponse.json({ message: 'Agent not found' }, { status: 404 })
    }

    // Verify memory exists and belongs to agent
    const existingMemory = await prisma.agentMemory.findFirst({
      where: {
        id: memoryId,
        agentId,
      },
    })

    if (!existingMemory) {
      return NextResponse.json({ message: 'Memory not found' }, { status: 404 })
    }

    // Delete memory
    const success = await agentMemoryService.deleteMemory(memoryId)

    if (!success) {
      return NextResponse.json({ message: 'Failed to delete memory' }, { status: 500 })
    }

    return NextResponse.json({
      message: 'Memory deleted successfully',
    })
  } catch (error: any) {
    console.error('Error deleting memory:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}
