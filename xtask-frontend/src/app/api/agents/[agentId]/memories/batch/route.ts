import { NextRequest, NextResponse } from 'next/server'
import { auth } from '../../../../../../../auth'
import { agentMemoryService } from '@/services/memory/agent-memory'
import { prisma } from '@/lib/db'
import { z } from 'zod'

// Validation schema for batch memory creation
const batchCreateMemorySchema = z.object({
  memories: z.array(z.object({
    content: z.string().min(1).max(10000),
    context: z.string().optional(),
    type: z.string().default('interaction'),
    importance: z.number().min(0).max(1).default(0.5),
    tags: z.array(z.string()).default([]),
    relatedTo: z.string().optional(),
    generateEmbedding: z.boolean().default(true),
  })).min(1).max(50), // Limit batch size to prevent abuse
})

// POST /api/agents/[agentId]/memories/batch - Create multiple memories
export async function POST(
  request: NextRequest,
  { params }: { params: { agentId: string } }
) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 })
    }

    const { agentId } = params
    const body = await request.json()

    // Verify agent ownership
    const agent = await prisma.agent.findFirst({
      where: {
        id: agentId,
        userId: session.user.id,
      },
    })

    if (!agent) {
      return NextResponse.json({ message: 'Agent not found' }, { status: 404 })
    }

    // Validate request body
    const validatedData = batchCreateMemorySchema.parse(body)

    // Add agentId to each memory input
    const memoryInputs = validatedData.memories.map(memory => ({
      agentId,
      ...memory,
    }))

    // Create memories in batch
    const createdMemories = await agentMemoryService.createBatchMemories(memoryInputs)

    return NextResponse.json({
      message: 'Memories created successfully',
      memories: createdMemories.map(memory => ({
        id: memory.id,
        content: memory.content,
        context: memory.context,
        type: memory.type,
        importance: memory.importance,
        tags: memory.tags,
        relatedTo: memory.relatedTo,
        embeddingModel: memory.embeddingModel,
        embeddingDim: memory.embeddingDim,
        createdAt: memory.createdAt,
        updatedAt: memory.updatedAt,
      })),
      count: createdMemories.length,
    }, { status: 201 })
  } catch (error: any) {
    console.error('Error creating batch memories:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: 'Invalid request data', errors: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}
