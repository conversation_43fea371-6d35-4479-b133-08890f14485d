import { NextRequest, NextResponse } from 'next/server'
import { auth } from '../../../../../../auth'
import { agentMemoryService } from '@/services/memory/agent-memory'
import { prisma } from '@/lib/db'
import { z } from 'zod'

// Validation schemas
const createMemorySchema = z.object({
  content: z.string().min(1).max(10000),
  context: z.string().optional(),
  type: z.string().default('interaction'),
  importance: z.number().min(0).max(1).default(0.5),
  tags: z.array(z.string()).default([]),
  relatedTo: z.string().optional(),
  generateEmbedding: z.boolean().default(true),
})

const searchMemoriesSchema = z.object({
  query: z.string().min(1),
  limit: z.number().min(1).max(100).default(10),
  threshold: z.number().min(0).max(1).default(0.7),
  includeEmbedding: z.boolean().default(false),
  type: z.string().optional(),
  tags: z.array(z.string()).optional(),
  minImportance: z.number().min(0).max(1).optional(),
  useTextSearch: z.boolean().default(false),
})

// GET /api/agents/[agentId]/memories - Get agent memories or search
export async function GET(
  request: NextRequest,
  { params }: { params: { agentId: string } }
) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 })
    }

    const { agentId } = params
    const { searchParams } = new URL(request.url)

    // Verify agent ownership
    const agent = await prisma.agent.findFirst({
      where: {
        id: agentId,
        userId: session.user.id,
      },
    })

    if (!agent) {
      return NextResponse.json({ message: 'Agent not found' }, { status: 404 })
    }

    // Check if this is a search request
    const query = searchParams.get('query')
    if (query) {
      // Parse search parameters
      const searchOptions = {
        query,
        limit: parseInt(searchParams.get('limit') || '10'),
        threshold: parseFloat(searchParams.get('threshold') || '0.7'),
        includeEmbedding: searchParams.get('includeEmbedding') === 'true',
        type: searchParams.get('type') || undefined,
        tags: searchParams.get('tags')?.split(',').filter(Boolean) || undefined,
        minImportance: searchParams.get('minImportance') 
          ? parseFloat(searchParams.get('minImportance')!) 
          : undefined,
        useTextSearch: searchParams.get('useTextSearch') === 'true',
      }

      // Validate search parameters
      const validatedOptions = searchMemoriesSchema.parse(searchOptions)

      // Perform search
      let memories
      if (validatedOptions.useTextSearch) {
        memories = await agentMemoryService.searchMemoriesByText(
          agentId,
          validatedOptions.query,
          validatedOptions
        )
      } else {
        memories = await agentMemoryService.searchMemoriesBySimilarity(
          agentId,
          validatedOptions.query,
          validatedOptions
        )
      }

      return NextResponse.json({
        memories,
        query: validatedOptions.query,
        searchType: validatedOptions.useTextSearch ? 'text' : 'similarity',
        count: memories.length,
      })
    }

    // Regular get all memories request
    const limit = parseInt(searchParams.get('limit') || '50')
    const type = searchParams.get('type') || undefined
    const tags = searchParams.get('tags')?.split(',').filter(Boolean) || undefined
    const minImportance = searchParams.get('minImportance') 
      ? parseFloat(searchParams.get('minImportance')!) 
      : undefined
    const includeEmbedding = searchParams.get('includeEmbedding') === 'true'

    const memories = await agentMemoryService.getAgentMemories(agentId, {
      limit,
      type,
      tags,
      minImportance,
      includeEmbedding,
    })

    return NextResponse.json({
      memories,
      count: memories.length,
    })
  } catch (error: any) {
    console.error('Error fetching agent memories:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: 'Invalid parameters', errors: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/agents/[agentId]/memories - Create new memory
export async function POST(
  request: NextRequest,
  { params }: { params: { agentId: string } }
) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 })
    }

    const { agentId } = params
    const body = await request.json()

    // Verify agent ownership
    const agent = await prisma.agent.findFirst({
      where: {
        id: agentId,
        userId: session.user.id,
      },
    })

    if (!agent) {
      return NextResponse.json({ message: 'Agent not found' }, { status: 404 })
    }

    // Validate request body
    const validatedData = createMemorySchema.parse(body)

    // Create memory
    const memory = await agentMemoryService.createMemory({
      agentId,
      ...validatedData,
    })

    return NextResponse.json({
      message: 'Memory created successfully',
      memory: {
        id: memory.id,
        content: memory.content,
        context: memory.context,
        type: memory.type,
        importance: memory.importance,
        tags: memory.tags,
        relatedTo: memory.relatedTo,
        embeddingModel: memory.embeddingModel,
        embeddingDim: memory.embeddingDim,
        createdAt: memory.createdAt,
        updatedAt: memory.updatedAt,
      },
    }, { status: 201 })
  } catch (error: any) {
    console.error('Error creating agent memory:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: 'Invalid request data', errors: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/agents/[agentId]/memories - Delete all memories for agent
export async function DELETE(
  request: NextRequest,
  { params }: { params: { agentId: string } }
) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 })
    }

    const { agentId } = params

    // Verify agent ownership
    const agent = await prisma.agent.findFirst({
      where: {
        id: agentId,
        userId: session.user.id,
      },
    })

    if (!agent) {
      return NextResponse.json({ message: 'Agent not found' }, { status: 404 })
    }

    // Delete all memories for the agent
    const deleteResult = await prisma.agentMemory.deleteMany({
      where: { agentId },
    })

    return NextResponse.json({
      message: 'All agent memories deleted successfully',
      deletedCount: deleteResult.count,
    })
  } catch (error: any) {
    console.error('Error deleting agent memories:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}
