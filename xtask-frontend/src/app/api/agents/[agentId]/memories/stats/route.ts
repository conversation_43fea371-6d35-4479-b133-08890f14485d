import { NextRequest, NextResponse } from 'next/server'
import { auth } from '../../../../../../../auth'
import { agentMemoryService } from '@/services/memory/agent-memory'
import { prisma } from '@/lib/db'

// GET /api/agents/[agentId]/memories/stats - Get memory statistics
export async function GET(
  request: NextRequest,
  { params }: { params: { agentId: string } }
) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 })
    }

    const { agentId } = params

    // Verify agent ownership
    const agent = await prisma.agent.findFirst({
      where: {
        id: agentId,
        userId: session.user.id,
      },
    })

    if (!agent) {
      return NextResponse.json({ message: 'Agent not found' }, { status: 404 })
    }

    // Get memory statistics
    const stats = await agentMemoryService.getMemoryStats(agentId)

    return NextResponse.json({
      stats,
      agent: {
        id: agent.id,
        name: agent.name,
        description: agent.description,
      },
    })
  } catch (error: any) {
    console.error('Error fetching memory statistics:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}
