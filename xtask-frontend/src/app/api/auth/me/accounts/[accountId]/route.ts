import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { prisma } from '@/lib/db'

// DELETE /api/auth/me/accounts/:accountId - Disconnect a connected account
export async function DELETE(
  request: NextRequest,
  { params }: { params: { accountId: string } }
) {
  try {
    const session = await auth()
    const { accountId } = params

    if (!session?.user?.id) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 })
    }

    if (!accountId) {
      return NextResponse.json({ message: 'Account ID is required' }, { status: 400 })
    }

    // Verify the account belongs to the authenticated user
    const accountToDelete = await prisma.account.findUnique({
      where: { id: accountId },
      include: {
        user: {
          select: {
            accounts: {
              select: { id: true, provider: true }
            }
          }
        }
      }
    })

    if (!accountToDelete) {
      return NextResponse.json({ message: 'Account not found' }, { status: 404 })
    }

    if (accountToDelete.userId !== session.user.id) {
      return NextResponse.json(
        { message: 'Forbidden: Account does not belong to user' },
        { status: 403 }
      )
    }

    // Check if this is the user's last account - prevent lockout
    const userAccountsCount = accountToDelete.user.accounts.length
    if (userAccountsCount <= 1) {
      return NextResponse.json(
        { 
          message: 'Cannot disconnect the last connected account. Please connect another account first.' 
        },
        { status: 400 }
      )
    }

    // Delete the account
    await prisma.account.delete({
      where: { id: accountId },
    })

    return NextResponse.json(
      { message: 'Account disconnected successfully' },
      { status: 200 }
    )
  } catch (error) {
    console.error(`Error disconnecting account ${params.accountId}:`, error)
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 })
  }
}

// GET /api/auth/me/accounts/:accountId - Get specific account details
export async function GET(
  request: NextRequest,
  { params }: { params: { accountId: string } }
) {
  try {
    const session = await auth()
    const { accountId } = params

    if (!session?.user?.id) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 })
    }

    if (!accountId) {
      return NextResponse.json({ message: 'Account ID is required' }, { status: 400 })
    }

    const account = await prisma.account.findUnique({
      where: { id: accountId },
      select: {
        id: true,
        provider: true,
        providerAccountId: true,
        type: true,
        // Do NOT include access_token, refresh_token for security
      },
    })

    if (!account) {
      return NextResponse.json({ message: 'Account not found' }, { status: 404 })
    }

    // Verify the account belongs to the authenticated user
    const userAccount = await prisma.account.findFirst({
      where: {
        id: accountId,
        userId: session.user.id,
      },
    })

    if (!userAccount) {
      return NextResponse.json(
        { message: 'Forbidden: Account does not belong to user' },
        { status: 403 }
      )
    }

    return NextResponse.json(account, { status: 200 })
  } catch (error) {
    console.error(`Error fetching account ${params.accountId}:`, error)
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 })
  }
}
