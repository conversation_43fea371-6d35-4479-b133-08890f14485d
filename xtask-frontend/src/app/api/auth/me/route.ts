import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { prisma } from '@/lib/db'
import { z } from 'zod'

// Zod schema for updating user profile
const updateUserProfileSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255).optional(),
  avatar: z.string().url('Invalid avatar URL').optional(),
  timezone: z.string().optional(),
  theme: z.enum(['light', 'dark']).optional(),
  notifications: z.boolean().optional(),
  defaultModel: z.string().optional(),
  fallbackModel: z.string().optional(),
  maxTokensPerDay: z.number().int().min(1000).max(100000).optional(),
})

type UserProfileFormValues = z.infer<typeof updateUserProfileSchema>

// GET /api/auth/me - Get current user profile and connected accounts
export async function GET() {
  try {
    const session = await auth()

    if (!session?.user?.id) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 })
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        name: true,
        email: true,
        avatar: true,
        createdAt: true,
        updatedAt: true,
        timezone: true,
        theme: true,
        notifications: true,
        defaultModel: true,
        fallbackModel: true,
        maxTokensPerDay: true,
        tokensUsedToday: true,
        lastTokenReset: true,
        accounts: {
          select: {
            id: true,
            provider: true,
            providerAccountId: true,
            type: true,
            // Do NOT include access_token, refresh_token for security
          },
        },
        twitterAccounts: {
          select: {
            id: true,
            twitterId: true,
            username: true,
            displayName: true,
            avatar: true,
            isActive: true,
            followersCount: true,
            followingCount: true,
            tweetsCount: true,
            lastSyncAt: true,
            createdAt: true,
          },
        },
      },
    })

    if (!user) {
      return NextResponse.json({ message: 'User not found' }, { status: 404 })
    }

    return NextResponse.json(user, { status: 200 })
  } catch (error) {
    console.error('Error fetching user profile:', error)
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 })
  }
}

// PUT /api/auth/me - Update user profile
export async function PUT(request: NextRequest) {
  try {
    const session = await auth()

    if (!session?.user?.id) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = updateUserProfileSchema.safeParse(body)

    if (!validatedData.success) {
      return NextResponse.json(
        { 
          message: 'Validation failed',
          errors: validatedData.error.flatten().fieldErrors 
        },
        { status: 400 }
      )
    }

    const updateData = validatedData.data

    const updatedUser = await prisma.user.update({
      where: { id: session.user.id },
      data: {
        ...updateData,
        updatedAt: new Date(),
      },
      select: {
        id: true,
        name: true,
        email: true,
        avatar: true,
        timezone: true,
        theme: true,
        notifications: true,
        defaultModel: true,
        fallbackModel: true,
        maxTokensPerDay: true,
        tokensUsedToday: true,
        updatedAt: true,
      },
    })

    return NextResponse.json(updatedUser, { status: 200 })
  } catch (error) {
    console.error('Error updating user profile:', error)
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 })
  }
}
