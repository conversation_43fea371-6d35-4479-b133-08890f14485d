import { NextRequest, NextResponse } from 'next/server'
import { sessionService } from '@/lib/session'
import { clearAuthCookie, getJTIFromRequest, createAuthMiddleware } from '@/lib/auth-middleware'
import { z } from 'zod'

const logoutSchema = z.object({
  logoutAll: z.boolean().optional().default(false)
})

// POST /api/auth/jwt/logout - JWT-based logout
export async function POST(request: NextRequest) {
  try {
    const body = await request.json().catch(() => ({}))
    const { logoutAll } = logoutSchema.parse(body)

    // Get current session from token
    const authMiddleware = createAuthMiddleware({ required: false })
    const { user } = await authMiddleware(request)

    const response = NextResponse.json({
      success: true,
      message: logoutAll ? 'Logged out from all devices' : 'Logout successful'
    })

    // Clear auth cookies
    clearAuthCookie(response)
    response.cookies.set('refresh-token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      expires: new Date(0),
      path: '/api/auth/jwt/refresh'
    })

    // If user is authenticated, revoke session(s)
    if (user) {
      if (logoutAll) {
        // Revoke all user sessions
        const revokedCount = await sessionService.revokeAllUserSessions(
          user.id,
          'user',
          'logout_all'
        )
        console.log(`Revoked ${revokedCount} sessions for user ${user.id}`)
      } else {
        // Revoke current session only
        const jti = getJTIFromRequest(request)
        if (jti) {
          await sessionService.revokeSession(jti, 'user', 'logout')
        }
      }
    }

    return response

  } catch (error) {
    console.error('Logout error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: 'Invalid input', errors: error.errors },
        { status: 400 }
      )
    }

    // Even if there's an error, we should still clear cookies
    const response = NextResponse.json(
      { success: true, message: 'Logout completed' }
    )
    clearAuthCookie(response)
    
    return response
  }
}
