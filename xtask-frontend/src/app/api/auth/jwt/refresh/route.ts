import { NextRequest, NextResponse } from 'next/server'
import { jwtService } from '@/lib/jwt'
import { sessionService } from '@/lib/session'
import { setAuthCookie } from '@/lib/auth-middleware'

// POST /api/auth/jwt/refresh - Refresh JWT tokens
export async function POST(request: NextRequest) {
  try {
    // Get refresh token from cookie
    const refreshTokenCookie = request.cookies.get('refresh-token')
    if (!refreshTokenCookie?.value) {
      return NextResponse.json(
        { message: 'Refresh token not found' },
        { status: 401 }
      )
    }

    const refreshToken = refreshTokenCookie.value

    // Verify refresh token
    const payload = jwtService.verifyToken(refreshToken)
    if (!payload || payload.type !== 'refresh') {
      return NextResponse.json(
        { message: 'Invalid refresh token' },
        { status: 401 }
      )
    }

    // Validate session and refresh
    const refreshResult = await sessionService.refreshSession(payload.jti)
    if (!refreshResult) {
      return NextResponse.json(
        { message: 'Session not found or expired' },
        { status: 401 }
      )
    }

    const { tokens, session } = refreshResult

    // Create response
    const response = NextResponse.json({
      success: true,
      message: 'Tokens refreshed successfully',
      session: {
        id: session.id,
        expiresAt: session.expiresAt
      }
    })

    // Set new access token cookie
    setAuthCookie(response, tokens.accessToken, tokens.accessTokenExpiry)

    // Update refresh token cookie
    response.cookies.set('refresh-token', tokens.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      expires: tokens.refreshTokenExpiry,
      path: '/api/auth/jwt/refresh'
    })

    return response

  } catch (error) {
    console.error('Token refresh error:', error)

    // Clear invalid cookies
    const response = NextResponse.json(
      { message: 'Token refresh failed' },
      { status: 401 }
    )

    response.cookies.set('auth-token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      expires: new Date(0),
      path: '/'
    })

    response.cookies.set('refresh-token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      expires: new Date(0),
      path: '/api/auth/jwt/refresh'
    })

    return response
  }
}
