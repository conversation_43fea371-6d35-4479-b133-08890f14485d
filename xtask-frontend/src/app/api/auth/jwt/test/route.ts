import { NextRequest, NextResponse } from 'next/server'
import { jwtService } from '@/lib/jwt'
import { sessionService } from '@/lib/session'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'

// POST /api/auth/jwt/test - Test JWT authentication system
export async function POST(request: NextRequest) {
  try {
    const testResults: any[] = []
    let allTestsPassed = true

    // Test 1: JWT Token Generation
    try {
      const testUserId = 'test-user-id'
      const tokens = jwtService.generateTokenPair(testUserId)
      
      testResults.push({
        test: 'JWT Token Generation',
        passed: !!(tokens.accessToken && tokens.refreshToken && tokens.jti),
        details: {
          hasAccessToken: !!tokens.accessToken,
          hasRefreshToken: !!tokens.refreshToken,
          hasJTI: !!tokens.jti,
          accessTokenExpiry: tokens.accessTokenExpiry,
          refreshTokenExpiry: tokens.refreshTokenExpiry
        }
      })
    } catch (error) {
      allTestsPassed = false
      testResults.push({
        test: 'JWT Token Generation',
        passed: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }

    // Test 2: JWT Token Verification
    try {
      const testUserId = 'test-user-id'
      const tokens = jwtService.generateTokenPair(testUserId)
      const payload = jwtService.verifyToken(tokens.accessToken)
      
      testResults.push({
        test: 'JWT Token Verification',
        passed: !!(payload && payload.sub === testUserId && payload.type === 'access'),
        details: {
          payloadExists: !!payload,
          correctUserId: payload?.sub === testUserId,
          correctType: payload?.type === 'access',
          payload: payload
        }
      })
    } catch (error) {
      allTestsPassed = false
      testResults.push({
        test: 'JWT Token Verification',
        passed: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }

    // Test 3: Database Session Creation (requires test user)
    let testUser = null
    try {
      // Create or find test user
      testUser = await prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
          email: '<EMAIL>',
          name: 'JWT Test User',
          password: await bcrypt.hash('test-password', 12),
          emailVerified: new Date()
        }
      })

      const { tokens, session } = await sessionService.createSession(testUser.id, {
        ipAddress: '127.0.0.1',
        userAgent: 'JWT Test',
        loginMethod: 'test'
      })

      testResults.push({
        test: 'Database Session Creation',
        passed: !!(session && session.jti === tokens.jti),
        details: {
          sessionCreated: !!session,
          jtiMatches: session?.jti === tokens.jti,
          sessionId: session?.id
        }
      })

      // Clean up test session
      if (session) {
        await sessionService.revokeSession(session.jti, 'system', 'test_cleanup')
      }
    } catch (error) {
      allTestsPassed = false
      testResults.push({
        test: 'Database Session Creation',
        passed: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }

    // Test 4: Session Validation
    if (testUser) {
      try {
        const { tokens, session } = await sessionService.createSession(testUser.id, {
          ipAddress: '127.0.0.1',
          userAgent: 'JWT Test',
          loginMethod: 'test'
        })

        const validatedSession = await sessionService.validateSession(session.jti)
        
        testResults.push({
          test: 'Session Validation',
          passed: !!(validatedSession && validatedSession.id === session.id),
          details: {
            sessionValidated: !!validatedSession,
            idsMatch: validatedSession?.id === session.id
          }
        })

        // Clean up test session
        await sessionService.revokeSession(session.jti, 'system', 'test_cleanup')
      } catch (error) {
        allTestsPassed = false
        testResults.push({
          test: 'Session Validation',
          passed: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    // Test 5: Session Revocation
    if (testUser) {
      try {
        const { tokens, session } = await sessionService.createSession(testUser.id, {
          ipAddress: '127.0.0.1',
          userAgent: 'JWT Test',
          loginMethod: 'test'
        })

        const revoked = await sessionService.revokeSession(session.jti, 'system', 'test')
        const validatedAfterRevoke = await sessionService.validateSession(session.jti)
        
        testResults.push({
          test: 'Session Revocation',
          passed: revoked && !validatedAfterRevoke,
          details: {
            revocationSucceeded: revoked,
            sessionInvalidAfterRevoke: !validatedAfterRevoke
          }
        })
      } catch (error) {
        allTestsPassed = false
        testResults.push({
          test: 'Session Revocation',
          passed: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    // Clean up test user
    if (testUser) {
      try {
        await prisma.user.delete({
          where: { id: testUser.id }
        })
      } catch (error) {
        console.warn('Failed to clean up test user:', error)
      }
    }

    return NextResponse.json({
      success: allTestsPassed,
      message: allTestsPassed ? 'All JWT tests passed' : 'Some JWT tests failed',
      testResults,
      summary: {
        total: testResults.length,
        passed: testResults.filter(t => t.passed).length,
        failed: testResults.filter(t => !t.passed).length
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('JWT test error:', error)
    return NextResponse.json(
      { 
        success: false,
        message: 'JWT test suite failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
