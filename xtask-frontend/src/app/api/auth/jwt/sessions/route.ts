import { NextRequest, NextResponse } from 'next/server'
import { sessionService } from '@/lib/session'
import { withAuth } from '@/lib/auth-middleware'
import { z } from 'zod'

const revokeSessionSchema = z.object({
  sessionId: z.string().optional(),
  jti: z.string().optional(),
  reason: z.string().optional().default('user_revoked')
}).refine(data => data.sessionId || data.jti, {
  message: "Either sessionId or jti must be provided"
})

// GET /api/auth/jwt/sessions - Get user's active sessions
export const GET = withAuth(async (request, { user }) => {
  try {
    const sessions = await sessionService.getUserSessions(user.id)
    const stats = await sessionService.getSessionStats(user.id)

    // Format sessions for frontend
    const formattedSessions = sessions.map(session => ({
      id: session.id,
      jti: session.jti,
      createdAt: session.createdAt,
      lastUsedAt: session.lastUsedAt,
      expiresAt: session.expiresAt,
      ipAddress: session.ipAddress,
      userAgent: session.userAgent,
      loginMethod: session.loginMethod,
      isCurrent: false // Will be determined by frontend based on current token
    }))

    return NextResponse.json({
      sessions: formattedSessions,
      stats,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error fetching sessions:', error)
    return NextResponse.json(
      { message: 'Failed to fetch sessions' },
      { status: 500 }
    )
  }
})

// DELETE /api/auth/jwt/sessions - Revoke a specific session
export const DELETE = withAuth(async (request, { user }) => {
  try {
    const body = await request.json()
    const { sessionId, jti, reason } = revokeSessionSchema.parse(body)

    let success = false
    let message = ''

    if (jti) {
      // Revoke by JWT ID
      success = await sessionService.revokeSession(jti, 'user', reason)
      message = success ? 'Session revoked successfully' : 'Session not found'
    } else if (sessionId) {
      // Find session by ID and revoke by JTI
      const session = await sessionService.validateSession(sessionId)
      if (session && session.userId === user.id) {
        success = await sessionService.revokeSession(session.jti, 'user', reason)
        message = success ? 'Session revoked successfully' : 'Failed to revoke session'
      } else {
        message = 'Session not found or access denied'
      }
    }

    if (!success) {
      return NextResponse.json(
        { message },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message
    })

  } catch (error) {
    console.error('Error revoking session:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: 'Invalid input', errors: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { message: 'Failed to revoke session' },
      { status: 500 }
    )
  }
})

// POST /api/auth/jwt/sessions/revoke-all - Revoke all sessions except current
export async function POST(request: NextRequest) {
  return withAuth(async (request, { user }) => {
    try {
      const currentJti = (request as any).jti

      // Get all user sessions
      const sessions = await sessionService.getUserSessions(user.id)
      
      // Revoke all sessions except the current one
      let revokedCount = 0
      for (const session of sessions) {
        if (session.jti !== currentJti) {
          const success = await sessionService.revokeSession(
            session.jti, 
            'user', 
            'revoke_all_others'
          )
          if (success) revokedCount++
        }
      }

      return NextResponse.json({
        success: true,
        message: `Revoked ${revokedCount} sessions`,
        revokedCount
      })

    } catch (error) {
      console.error('Error revoking all sessions:', error)
      return NextResponse.json(
        { message: 'Failed to revoke sessions' },
        { status: 500 }
      )
    }
  })(request, {})
}
