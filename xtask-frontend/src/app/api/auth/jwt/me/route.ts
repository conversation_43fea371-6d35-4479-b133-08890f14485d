import { NextRequest, NextResponse } from 'next/server'
import { withAuth } from '@/lib/auth-middleware'
import { sessionService } from '@/lib/session'

// GET /api/auth/jwt/me - Get current user profile
export const GET = withAuth(async (request, { user }) => {
  try {
    // Get current session info
    const jti = (request as any).jti
    const sessionId = (request as any).sessionId

    let currentSession = null
    if (jti) {
      currentSession = await sessionService.validateSession(jti)
    }

    // Get session statistics
    const sessionStats = await sessionService.getSessionStats(user.id)

    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        image: user.image,
        emailVerified: user.emailVerified,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      },
      session: currentSession ? {
        id: currentSession.id,
        jti: currentSession.jti,
        createdAt: currentSession.createdAt,
        lastUsedAt: currentSession.lastUsedAt,
        expiresAt: currentSession.expiresAt,
        ipAddress: currentSession.ipAddress,
        userAgent: currentSession.userAgent,
        loginMethod: currentSession.loginMethod
      } : null,
      sessionStats,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error fetching user profile:', error)
    return NextResponse.json(
      { message: 'Failed to fetch user profile' },
      { status: 500 }
    )
  }
})
