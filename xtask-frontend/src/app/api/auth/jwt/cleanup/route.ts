import { NextRequest, NextResponse } from 'next/server'
import { sessionCleanupService } from '@/lib/session-cleanup'
import { withAuth } from '@/lib/auth-middleware'

// GET /api/auth/jwt/cleanup - Get cleanup statistics
export const GET = withAuth(async (request, { user }) => {
  try {
    // Only allow admin users to access cleanup stats
    // For now, we'll allow any authenticated user
    // In production, you'd check user.role === 'admin'
    
    const stats = await sessionCleanupService.getCleanupStats()
    
    return NextResponse.json({
      stats,
      cleanupInterval: '1 hour',
      maxSessionAge: '30 days',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error fetching cleanup stats:', error)
    return NextResponse.json(
      { message: 'Failed to fetch cleanup statistics' },
      { status: 500 }
    )
  }
})

// POST /api/auth/jwt/cleanup - Force cleanup
export const POST = withAuth(async (request, { user }) => {
  try {
    // Only allow admin users to force cleanup
    // For now, we'll allow any authenticated user
    // In production, you'd check user.role === 'admin'
    
    const result = await sessionCleanupService.forceCleanup()
    
    return NextResponse.json({
      success: true,
      message: 'Cleanup completed successfully',
      result,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error during forced cleanup:', error)
    return NextResponse.json(
      { message: 'Cleanup failed' },
      { status: 500 }
    )
  }
})
