import { NextRequest, NextResponse } from 'next/server'
import bcrypt from 'bcryptjs'
import { prisma } from '@/lib/prisma'
import { sessionService } from '@/lib/session'
import { setAuthCookie } from '@/lib/auth-middleware'
import { z } from 'zod'

const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().optional().default(false)
})

// POST /api/auth/jwt/login - JWT-based login
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email, password, rememberMe } = loginSchema.parse(body)

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email: email.toLowerCase() }
    })

    if (!user) {
      return NextResponse.json(
        { message: 'Invalid email or password' },
        { status: 401 }
      )
    }

    // Check if user has a password (might be OAuth-only user)
    if (!user.password) {
      return NextResponse.json(
        { message: 'Please sign in with your OAuth provider' },
        { status: 401 }
      )
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password)
    if (!isValidPassword) {
      return NextResponse.json(
        { message: 'Invalid email or password' },
        { status: 401 }
      )
    }

    // Check if email is verified (if email verification is enabled)
    if (user.emailVerified === null) {
      return NextResponse.json(
        { message: 'Please verify your email address before signing in' },
        { status: 401 }
      )
    }

    // Extract request metadata
    const ipAddress = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown'
    const userAgent = request.headers.get('user-agent') || 'unknown'

    // Create session
    const { tokens, session } = await sessionService.createSession(user.id, {
      ipAddress,
      userAgent,
      loginMethod: 'password'
    })

    // Create response
    const response = NextResponse.json({
      success: true,
      message: 'Login successful',
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        image: user.image,
        emailVerified: user.emailVerified
      },
      session: {
        id: session.id,
        expiresAt: session.expiresAt
      }
    })

    // Set HTTP-only cookie with access token
    setAuthCookie(response, tokens.accessToken, tokens.accessTokenExpiry)

    // Optionally set refresh token cookie for "remember me"
    if (rememberMe) {
      response.cookies.set('refresh-token', tokens.refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        expires: tokens.refreshTokenExpiry,
        path: '/api/auth/jwt/refresh'
      })
    }

    return response

  } catch (error) {
    console.error('Login error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: 'Invalid input', errors: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}
