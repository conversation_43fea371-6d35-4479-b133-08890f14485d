import { NextRequest, NextResponse } from 'next/server'
import { auth } from '../../../../auth'
import { prisma } from '@/lib/db'
import { z } from 'zod'

// Validation schema for account operations
const disconnectAccountSchema = z.object({
  accountId: z.string().min(1, 'Account ID is required'),
  provider: z.enum(['google', 'twitter'], {
    required_error: 'Provider must be either google or twitter',
  }),
})

// GET /api/auth/accounts - Get all connected accounts for current user
export async function GET() {
  try {
    const session = await auth()

    if (!session?.user?.id) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 })
    }

    // Fetch OAuth accounts
    const accounts = await prisma.account.findMany({
      where: { userId: session.user.id },
      select: {
        id: true,
        provider: true,
        providerAccountId: true,
        type: true,
        // Do NOT include sensitive tokens
      },
    })

    // Fetch Twitter accounts
    const twitterAccounts = await prisma.twitterAccount.findMany({
      where: { userId: session.user.id },
      select: {
        id: true,
        twitterId: true,
        username: true,
        displayName: true,
        avatar: true,
        isActive: true,
        followersCount: true,
        followingCount: true,
        tweetsCount: true,
        lastSyncAt: true,
        createdAt: true,
      },
    })

    return NextResponse.json({
      success: true,
      accounts: {
        oauth: accounts,
        twitter: twitterAccounts,
      },
    })
  } catch (error) {
    console.error('Error fetching connected accounts:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch connected accounts' },
      { status: 500 }
    )
  }
}

// DELETE /api/auth/accounts - Disconnect an account
export async function DELETE(request: NextRequest) {
  try {
    const session = await auth()

    if (!session?.user?.id) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = disconnectAccountSchema.safeParse(body)

    if (!validatedData.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation failed',
          details: validatedData.error.flatten().fieldErrors
        },
        { status: 400 }
      )
    }

    const { accountId, provider } = validatedData.data

    if (provider === 'twitter') {
      // Disconnect Twitter account
      const deletedAccount = await prisma.twitterAccount.delete({
        where: {
          id: accountId,
          userId: session.user.id, // Ensure user owns this account
        },
      })

      return NextResponse.json({
        success: true,
        message: 'Twitter account disconnected successfully',
        disconnectedAccount: {
          id: deletedAccount.id,
          username: deletedAccount.username,
          provider: 'twitter',
        },
      })
    } else {
      // Disconnect OAuth account (Google, etc.)
      const deletedAccount = await prisma.account.delete({
        where: {
          id: accountId,
          userId: session.user.id, // Ensure user owns this account
        },
      })

      return NextResponse.json({
        success: true,
        message: `${provider} account disconnected successfully`,
        disconnectedAccount: {
          id: deletedAccount.id,
          provider: deletedAccount.provider,
          providerAccountId: deletedAccount.providerAccountId,
        },
      })
    }
  } catch (error) {
    console.error('Error disconnecting account:', error)

    // Handle specific Prisma errors
    if (error instanceof Error && error.message.includes('Record to delete does not exist')) {
      return NextResponse.json(
        { success: false, error: 'Account not found or already disconnected' },
        { status: 404 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to disconnect account' },
      { status: 500 }
    )
  }
}
