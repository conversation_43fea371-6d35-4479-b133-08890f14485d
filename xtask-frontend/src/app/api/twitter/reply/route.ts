import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/auth"
import { prisma } from "@/lib/db"
import { TwitterServiceFactory, withRetry } from "@/services/twitter"
import { replyPostSchema, ReplyPostRequest } from "@/lib/validations/twitter"
import { z } from "zod"

// POST /api/twitter/reply - Post a reply to a tweet
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = replyPostSchema.parse(body)

    // Verify agent ownership
    const agent = await prisma.agent.findFirst({
      where: {
        id: validatedData.agentId,
        userId: session.user.id,
      },
      include: {
        user: {
          include: {
            twitterAccounts: true
          }
        }
      }
    })

    if (!agent) {
      return NextResponse.json({ message: "Agent not found" }, { status: 404 })
    }

    // Check if user has Twitter connected
    if (!agent.user.twitterAccounts.length) {
      return NextResponse.json({ 
        message: "Twitter account not connected" 
      }, { status: 400 })
    }

    const twitterService = await TwitterServiceFactory.createAgentClient(validatedData.agentId)

    // Verify the original tweet exists
    const originalTweet = await twitterService.getTweet(validatedData.inReplyToTweetId)
    if (!originalTweet) {
      return NextResponse.json({ 
        message: "Original tweet not found or not accessible" 
      }, { status: 404 })
    }

    // Handle scheduled replies
    if (validatedData.scheduledFor) {
      const scheduledFor = new Date(validatedData.scheduledFor)
      
      if (scheduledFor <= new Date()) {
        return NextResponse.json({ 
          message: "Scheduled time must be in the future" 
        }, { status: 400 })
      }

      // Store scheduled reply in database
      const scheduledReply = await prisma.scheduledTweet.create({
        data: {
          agentId: validatedData.agentId,
          content: validatedData.text,
          mediaIds: validatedData.mediaIds || [],
          scheduledFor,
          status: 'pending',
          type: 'reply',
          metadata: {
            inReplyToTweetId: validatedData.inReplyToTweetId,
            excludeReplyUserIds: validatedData.excludeReplyUserIds,
            originalTweetAuthor: originalTweet.author_id
          }
        }
      })

      return NextResponse.json({
        message: "Reply scheduled successfully",
        scheduledReply: {
          id: scheduledReply.id,
          scheduledFor: scheduledReply.scheduledFor,
          status: scheduledReply.status,
          inReplyToTweetId: validatedData.inReplyToTweetId
        }
      })
    }

    // Apply agent preferences to reply content
    let replyText = validatedData.text

    // Add agent tone/personality if configured
    if (agent.tone && !replyText.toLowerCase().includes(agent.tone.toLowerCase())) {
      // Subtle tone adjustment based on agent settings
      switch (agent.tone) {
        case 'professional':
          if (!replyText.match(/^(Thank you|Thanks|Great|Excellent)/i)) {
            replyText = `Thank you for sharing. ${replyText}`
          }
          break
        case 'casual':
          if (!replyText.match(/^(Hey|Hi|Cool|Nice)/i)) {
            replyText = `Hey! ${replyText}`
          }
          break
        case 'humorous':
          // Add emoji if not present and appropriate
          if (!replyText.includes('😄') && !replyText.includes('😊') && replyText.length < 260) {
            replyText += ' 😄'
          }
          break
      }
    }

    // Ensure reply doesn't exceed character limit after modifications
    if (replyText.length > 280) {
      replyText = validatedData.text // Fallback to original text
    }

    const replyOptions = {
      text: replyText,
      reply: {
        in_reply_to_tweet_id: validatedData.inReplyToTweetId,
        exclude_reply_user_ids: validatedData.excludeReplyUserIds
      },
      media: validatedData.mediaIds?.length ? {
        media_ids: validatedData.mediaIds
      } : undefined
    }

    const reply = await withRetry(
      () => twitterService.postTweet(replyOptions),
      3,
      1000
    )

    // Store reply in database for analytics
    await prisma.tweet.create({
      data: {
        id: reply.id,
        agentId: validatedData.agentId,
        content: reply.text,
        twitterCreatedAt: new Date(reply.created_at || Date.now()),
        mediaIds: validatedData.mediaIds || [],
        replyToTweetId: validatedData.inReplyToTweetId,
        conversationId: reply.conversation_id,
        metrics: {
          retweets: 0,
          likes: 0,
          replies: 0,
          quotes: 0,
          bookmarks: 0,
          impressions: 0,
        }
      }
    })

    // Update agent's tweet count
    await prisma.agent.update({
      where: { id: validatedData.agentId },
      data: {
        totalTweets: {
          increment: 1
        }
      }
    })

    return NextResponse.json({
      message: "Reply posted successfully",
      reply: {
        id: reply.id,
        text: reply.text,
        created_at: reply.created_at,
        in_reply_to_tweet_id: validatedData.inReplyToTweetId,
        conversation_id: reply.conversation_id,
        public_metrics: reply.public_metrics
      }
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.flatten() },
        { status: 400 }
      )
    }

    console.error("Error posting reply:", error)
    
    // Handle Twitter API errors
    if (error instanceof Error && error.name === 'TwitterApiError') {
      const twitterError = error as any
      return NextResponse.json(
        { 
          message: twitterError.message,
          code: twitterError.code,
          type: twitterError.type 
        },
        { status: twitterError.code === 429 ? 429 : 400 }
      )
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

// GET /api/twitter/reply - Get replies for a conversation
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const conversationId = searchParams.get('conversationId')
    const agentId = searchParams.get('agentId')

    if (!conversationId || !agentId) {
      return NextResponse.json({ 
        message: "Conversation ID and Agent ID are required" 
      }, { status: 400 })
    }

    // Verify agent ownership
    const agent = await prisma.agent.findFirst({
      where: {
        id: agentId,
        userId: session.user.id,
      }
    })

    if (!agent) {
      return NextResponse.json({ message: "Agent not found" }, { status: 404 })
    }

    // Get replies from database
    const replies = await prisma.tweet.findMany({
      where: {
        conversationId: conversationId,
        agentId: agentId,
        deletedAt: null,
        replyToTweetId: {
          not: null
        }
      },
      orderBy: {
        twitterCreatedAt: 'asc'
      }
    })

    return NextResponse.json({
      replies: replies.map(reply => ({
        id: reply.id,
        text: reply.content,
        created_at: reply.twitterCreatedAt,
        in_reply_to_tweet_id: reply.replyToTweetId,
        conversation_id: reply.conversationId,
        metrics: reply.metrics
      }))
    })

  } catch (error) {
    console.error("Error fetching replies:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}
