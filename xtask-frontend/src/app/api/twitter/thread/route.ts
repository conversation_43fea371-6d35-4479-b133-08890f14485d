import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/auth"
import { prisma } from "@/lib/db"
import { TwitterServiceFactory, withRetry } from "@/services/twitter"
import { threadPostSchema, ThreadPostRequest } from "@/lib/validations/twitter"
import { z } from "zod"

// POST /api/twitter/thread - Post a thread of tweets
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = threadPostSchema.parse(body)

    // Verify agent ownership
    const agent = await prisma.agent.findFirst({
      where: {
        id: validatedData.agentId,
        userId: session.user.id,
      },
      include: {
        user: {
          include: {
            twitterAccounts: true
          }
        }
      }
    })

    if (!agent) {
      return NextResponse.json({ message: "Agent not found" }, { status: 404 })
    }

    // Check if user has Twitter connected
    if (!agent.user.twitterAccounts.length) {
      return NextResponse.json({ 
        message: "Twitter account not connected" 
      }, { status: 400 })
    }

    // Handle scheduled threads
    if (validatedData.scheduledFor) {
      const scheduledFor = new Date(validatedData.scheduledFor)
      
      if (scheduledFor <= new Date()) {
        return NextResponse.json({ 
          message: "Scheduled time must be in the future" 
        }, { status: 400 })
      }

      // Store scheduled thread in database
      const scheduledThread = await prisma.scheduledTweet.create({
        data: {
          agentId: validatedData.agentId,
          content: JSON.stringify(validatedData.tweets),
          scheduledFor,
          status: 'pending',
          type: 'thread',
          metadata: {
            replySettings: validatedData.replySettings,
            tweetCount: validatedData.tweets.length
          }
        }
      })

      return NextResponse.json({
        message: "Thread scheduled successfully",
        scheduledThread: {
          id: scheduledThread.id,
          scheduledFor: scheduledThread.scheduledFor,
          status: scheduledThread.status,
          tweetCount: validatedData.tweets.length
        }
      })
    }

    // Post thread immediately
    const twitterService = await TwitterServiceFactory.createAgentClient(validatedData.agentId)

    // Apply agent preferences to thread content
    const processedTweets = validatedData.tweets.map((tweet, index) => {
      let tweetText = tweet.text

      // Add thread numbering for clarity (except first tweet)
      if (index > 0 && !tweetText.match(/^\d+\//)) {
        tweetText = `${index + 1}/${validatedData.tweets.length} ${tweetText}`
      }

      // Add agent hashtags to the last tweet only
      if (index === validatedData.tweets.length - 1 && agent.topics && agent.topics.length > 0) {
        const agentHashtags = agent.topics
          .slice(0, 2) // Limit to 2 hashtags for threads
          .map(topic => `#${topic.replace(/\s+/g, '')}`)
          .join(' ')
        
        // Only add if there's room
        if ((tweetText + ' ' + agentHashtags).length <= 280) {
          tweetText += ' ' + agentHashtags
        }
      }

      return {
        text: tweetText,
        media_ids: tweet.mediaIds
      }
    })

    const threadOptions = {
      tweets: processedTweets,
      reply_settings: validatedData.replySettings
    }

    const postedTweets = await withRetry(
      () => twitterService.postThread(threadOptions),
      3,
      2000 // Longer delay for threads
    )

    // Store all tweets in database for analytics
    const tweetRecords = await Promise.all(
      postedTweets.map((tweet, index) => 
        prisma.tweet.create({
          data: {
            id: tweet.id,
            agentId: validatedData.agentId,
            content: tweet.text,
            twitterCreatedAt: new Date(tweet.created_at || Date.now()),
            mediaIds: validatedData.tweets[index].mediaIds || [],
            threadId: postedTweets[0].id, // Use first tweet ID as thread ID
            threadPosition: index + 1,
            metrics: {
              retweets: 0,
              likes: 0,
              replies: 0,
              quotes: 0,
              bookmarks: 0,
              impressions: 0,
            }
          }
        })
      )
    )

    // Update agent's tweet count
    await prisma.agent.update({
      where: { id: validatedData.agentId },
      data: {
        totalTweets: {
          increment: postedTweets.length
        }
      }
    })

    return NextResponse.json({
      message: "Thread posted successfully",
      thread: {
        id: postedTweets[0].id, // Thread ID is the first tweet's ID
        tweets: postedTweets.map((tweet, index) => ({
          id: tweet.id,
          text: tweet.text,
          created_at: tweet.created_at,
          position: index + 1,
          public_metrics: tweet.public_metrics
        })),
        totalTweets: postedTweets.length
      }
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.flatten() },
        { status: 400 }
      )
    }

    console.error("Error posting thread:", error)
    
    // Handle Twitter API errors
    if (error instanceof Error && error.name === 'TwitterApiError') {
      const twitterError = error as any
      return NextResponse.json(
        { 
          message: twitterError.message,
          code: twitterError.code,
          type: twitterError.type 
        },
        { status: twitterError.code === 429 ? 429 : 400 }
      )
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

// GET /api/twitter/thread - Get thread by ID
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const threadId = searchParams.get('id')
    const agentId = searchParams.get('agentId')

    if (!threadId || !agentId) {
      return NextResponse.json({ 
        message: "Thread ID and Agent ID are required" 
      }, { status: 400 })
    }

    // Verify agent ownership
    const agent = await prisma.agent.findFirst({
      where: {
        id: agentId,
        userId: session.user.id,
      }
    })

    if (!agent) {
      return NextResponse.json({ message: "Agent not found" }, { status: 404 })
    }

    // Get thread tweets from database
    const threadTweets = await prisma.tweet.findMany({
      where: {
        threadId: threadId,
        agentId: agentId,
        deletedAt: null
      },
      orderBy: {
        threadPosition: 'asc'
      }
    })

    if (!threadTweets.length) {
      return NextResponse.json({ message: "Thread not found" }, { status: 404 })
    }

    return NextResponse.json({
      thread: {
        id: threadId,
        tweets: threadTweets.map(tweet => ({
          id: tweet.id,
          text: tweet.content,
          created_at: tweet.twitterCreatedAt,
          position: tweet.threadPosition,
          metrics: tweet.metrics
        })),
        totalTweets: threadTweets.length
      }
    })

  } catch (error) {
    console.error("Error fetching thread:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}
