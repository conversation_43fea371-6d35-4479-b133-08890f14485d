import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/auth"
import { prisma } from "@/lib/db"
import { TwitterServiceFactory, withRetry } from "@/services/twitter"
import { tweetPostSchema, TweetPostRequest } from "@/lib/validations/twitter"
import { z } from "zod"

// POST /api/twitter/tweet - Post a single tweet
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = tweetPostSchema.parse(body)

    // Verify agent ownership
    const agent = await prisma.agent.findFirst({
      where: {
        id: validatedData.agentId,
        userId: session.user.id,
      },
      include: {
        user: {
          include: {
            twitterAccounts: true
          }
        }
      }
    })

    if (!agent) {
      return NextResponse.json({ message: "Agent not found" }, { status: 404 })
    }

    // Check if user has Twitter connected
    if (!agent.user.twitterAccounts.length) {
      return NextResponse.json({ 
        message: "Twitter account not connected" 
      }, { status: 400 })
    }

    // Handle scheduled tweets
    if (validatedData.scheduledFor) {
      const scheduledFor = new Date(validatedData.scheduledFor)
      
      if (scheduledFor <= new Date()) {
        return NextResponse.json({ 
          message: "Scheduled time must be in the future" 
        }, { status: 400 })
      }

      // Store scheduled tweet in database
      const scheduledTweet = await prisma.scheduledTweet.create({
        data: {
          agentId: validatedData.agentId,
          content: validatedData.text,
          mediaIds: validatedData.mediaIds || [],
          scheduledFor,
          status: 'pending',
          metadata: {
            replyToTweetId: validatedData.replyToTweetId,
            quoteTweetId: validatedData.quoteTweetId,
            taggedUserIds: validatedData.taggedUserIds,
            replySettings: validatedData.replySettings,
            geoPlaceId: validatedData.geoPlaceId,
            forSuperFollowersOnly: validatedData.forSuperFollowersOnly,
          }
        }
      })

      return NextResponse.json({
        message: "Tweet scheduled successfully",
        scheduledTweet: {
          id: scheduledTweet.id,
          scheduledFor: scheduledTweet.scheduledFor,
          status: scheduledTweet.status
        }
      })
    }

    // Post tweet immediately
    const twitterService = await TwitterServiceFactory.createAgentClient(validatedData.agentId)

    // Apply agent preferences to tweet content
    let tweetText = validatedData.text
    
    // Add agent-specific hashtags if configured
    if (agent.topics && agent.topics.length > 0) {
      const agentHashtags = agent.topics
        .slice(0, 3) // Limit to 3 hashtags
        .map(topic => `#${topic.replace(/\s+/g, '')}`)
        .join(' ')
      
      // Only add if there's room
      if ((tweetText + ' ' + agentHashtags).length <= 280) {
        tweetText += ' ' + agentHashtags
      }
    }

    const tweetOptions = {
      text: tweetText,
      media: validatedData.mediaIds?.length ? {
        media_ids: validatedData.mediaIds,
        tagged_user_ids: validatedData.taggedUserIds
      } : undefined,
      reply: validatedData.replyToTweetId ? {
        in_reply_to_tweet_id: validatedData.replyToTweetId
      } : undefined,
      quote_tweet_id: validatedData.quoteTweetId,
      geo: validatedData.geoPlaceId ? {
        place_id: validatedData.geoPlaceId
      } : undefined,
      reply_settings: validatedData.replySettings,
      for_super_followers_only: validatedData.forSuperFollowersOnly,
    }

    const tweet = await withRetry(
      () => twitterService.postTweet(tweetOptions),
      3,
      1000
    )

    // Store tweet in database for analytics
    await prisma.tweet.create({
      data: {
        id: tweet.id,
        agentId: validatedData.agentId,
        content: tweet.text,
        twitterCreatedAt: new Date(tweet.created_at || Date.now()),
        mediaIds: validatedData.mediaIds || [],
        metrics: {
          retweets: 0,
          likes: 0,
          replies: 0,
          quotes: 0,
          bookmarks: 0,
          impressions: 0,
        }
      }
    })

    // Update agent's tweet count
    await prisma.agent.update({
      where: { id: validatedData.agentId },
      data: {
        totalTweets: {
          increment: 1
        }
      }
    })

    return NextResponse.json({
      message: "Tweet posted successfully",
      tweet: {
        id: tweet.id,
        text: tweet.text,
        created_at: tweet.created_at,
        public_metrics: tweet.public_metrics
      }
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.flatten() },
        { status: 400 }
      )
    }

    console.error("Error posting tweet:", error)
    
    // Handle Twitter API errors
    if (error instanceof Error && error.name === 'TwitterApiError') {
      const twitterError = error as any
      return NextResponse.json(
        { 
          message: twitterError.message,
          code: twitterError.code,
          type: twitterError.type 
        },
        { status: twitterError.code === 429 ? 429 : 400 }
      )
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

// GET /api/twitter/tweet - Get tweet by ID
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const tweetId = searchParams.get('id')
    const agentId = searchParams.get('agentId')

    if (!tweetId || !agentId) {
      return NextResponse.json({ 
        message: "Tweet ID and Agent ID are required" 
      }, { status: 400 })
    }

    // Verify agent ownership
    const agent = await prisma.agent.findFirst({
      where: {
        id: agentId,
        userId: session.user.id,
      }
    })

    if (!agent) {
      return NextResponse.json({ message: "Agent not found" }, { status: 404 })
    }

    const twitterService = await TwitterServiceFactory.createAgentClient(agentId)
    const tweet = await twitterService.getTweet(tweetId)

    if (!tweet) {
      return NextResponse.json({ message: "Tweet not found" }, { status: 404 })
    }

    return NextResponse.json({ tweet })

  } catch (error) {
    console.error("Error fetching tweet:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

// DELETE /api/twitter/tweet - Delete a tweet
export async function DELETE(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const tweetId = searchParams.get('id')
    const agentId = searchParams.get('agentId')

    if (!tweetId || !agentId) {
      return NextResponse.json({ 
        message: "Tweet ID and Agent ID are required" 
      }, { status: 400 })
    }

    // Verify agent ownership
    const agent = await prisma.agent.findFirst({
      where: {
        id: agentId,
        userId: session.user.id,
      }
    })

    if (!agent) {
      return NextResponse.json({ message: "Agent not found" }, { status: 404 })
    }

    const twitterService = await TwitterServiceFactory.createAgentClient(agentId)
    const deleted = await twitterService.deleteTweet(tweetId)

    if (deleted) {
      // Update tweet status in database
      await prisma.tweet.updateMany({
        where: {
          id: tweetId,
          agentId: agentId
        },
        data: {
          deletedAt: new Date()
        }
      })

      // Update agent's tweet count
      await prisma.agent.update({
        where: { id: agentId },
        data: {
          totalTweets: {
            decrement: 1
          }
        }
      })
    }

    return NextResponse.json({
      message: deleted ? "Tweet deleted successfully" : "Tweet not found or already deleted",
      deleted
    })

  } catch (error) {
    console.error("Error deleting tweet:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}
