import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { auth } from "@/auth"
import { prisma } from "@/lib/db"
import { 
  createFactoryFromEnv, 
  AIProviderType, 
  ContentGenerationRequest,
  ContentGenerationResponse,
  AIMessage,
  AIGenerationOptions
} from "@/services/ai"

// Request validation schema
const generateRequestSchema = z.object({
  agentId: z.string().uuid("Invalid agent ID"),
  prompt: z.string().min(1, "Prompt is required").max(4000, "Prompt too long"),
  context: z.string().optional(),
  type: z.enum(['tweet', 'thread', 'reply', 'custom']).default('tweet'),
  provider: z.nativeEnum(AIProviderType).optional(),
  model: z.string().optional(),
  temperature: z.number().min(0).max(1).optional(),
  maxTokens: z.number().int().positive().max(4000).optional(),
  metadata: z.record(z.any()).optional(),
})

type GenerateRequest = z.infer<typeof generateRequestSchema>

// Simple in-memory cache (in production, use Redis or similar)
const cache = new Map<string, { response: ContentGenerationResponse; expiresAt: number }>()

// Cache TTL: 1 hour
const CACHE_TTL = 60 * 60 * 1000

// POST /api/ai/generate - Generate content using AI
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = generateRequestSchema.parse(body)

    // Verify agent ownership
    const agent = await prisma.agent.findFirst({
      where: {
        id: validatedData.agentId,
        userId: session.user.id,
      },
    })

    if (!agent) {
      return NextResponse.json({ message: "Agent not found" }, { status: 404 })
    }

    // Generate cache key
    const cacheKey = generateCacheKey(validatedData, agent)
    
    // Check cache first
    const cached = cache.get(cacheKey)
    if (cached && cached.expiresAt > Date.now()) {
      return NextResponse.json(cached.response)
    }

    // Initialize AI provider factory
    const factory = createFactoryFromEnv()
    await factory.initialize()

    // Determine provider and model
    const providerType = validatedData.provider || 
                        (agent.preferredModel ? getProviderFromModel(agent.preferredModel) : null) ||
                        AIProviderType.OPENAI

    const model = validatedData.model || 
                  agent.preferredModel || 
                  getDefaultModelForProvider(providerType)

    // Get AI provider
    const provider = await factory.getProvider(providerType)

    // Prepare generation options
    const options: AIGenerationOptions = {
      model,
      temperature: validatedData.temperature ?? agent.temperature ?? 0.7,
      maxTokens: validatedData.maxTokens ?? agent.maxTokens ?? 280, // Twitter-like default
      topP: 0.9,
    }

    // Prepare messages
    const messages: AIMessage[] = []

    // Add system message with agent personality
    if (agent.personality || agent.writingStyle || agent.tone) {
      const systemPrompt = buildSystemPrompt(agent, validatedData.type)
      messages.push({
        role: 'system',
        content: systemPrompt
      })
    }

    // Add context if provided
    if (validatedData.context) {
      messages.push({
        role: 'user',
        content: `Context: ${validatedData.context}`
      })
    }

    // Add main prompt
    messages.push({
      role: 'user',
      content: validatedData.prompt
    })

    // Generate content
    const startTime = Date.now()
    const aiResponse = await provider.generateContent(messages, options)
    const processingTime = Date.now() - startTime

    // Calculate estimated cost
    const modelInfo = provider.getModelInfo(model)
    const estimatedCost = modelInfo ? 
      (aiResponse.usage.promptTokens / 1000) * modelInfo.costPer1kTokens.input +
      (aiResponse.usage.completionTokens / 1000) * modelInfo.costPer1kTokens.output : 0

    // Prepare response
    const response: ContentGenerationResponse = {
      content: aiResponse.content,
      provider: providerType,
      model: aiResponse.model,
      usage: {
        promptTokens: aiResponse.usage.promptTokens,
        completionTokens: aiResponse.usage.completionTokens,
        totalTokens: aiResponse.usage.totalTokens,
        estimatedCost
      },
      metadata: {
        agentId: validatedData.agentId,
        requestId: crypto.randomUUID(),
        timestamp: new Date().toISOString(),
        processingTime
      }
    }

    // Cache the response
    cache.set(cacheKey, {
      response,
      expiresAt: Date.now() + CACHE_TTL
    })

    // Clean up expired cache entries periodically
    if (Math.random() < 0.01) { // 1% chance
      cleanupCache()
    }

    return NextResponse.json(response)

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.flatten() },
        { status: 400 }
      )
    }

    console.error("Error generating AI content:", error)
    
    // Handle AI provider errors
    if (error instanceof Error && error.name === 'AIProviderError') {
      const status = (error as any).status || 500
      return NextResponse.json(
        { message: error.message, type: (error as any).type },
        { status }
      )
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

// Helper functions
function generateCacheKey(request: GenerateRequest, agent: any): string {
  const key = {
    agentId: request.agentId,
    prompt: request.prompt,
    context: request.context,
    type: request.type,
    provider: request.provider,
    model: request.model,
    temperature: request.temperature ?? agent.temperature,
    maxTokens: request.maxTokens ?? agent.maxTokens,
  }
  return Buffer.from(JSON.stringify(key)).toString('base64')
}

function getProviderFromModel(model: string): AIProviderType {
  if (model.startsWith('gpt-')) return AIProviderType.OPENAI
  if (model.startsWith('gemini-')) return AIProviderType.GEMINI
  if (model.startsWith('mistral-')) return AIProviderType.MISTRAL
  if (model.startsWith('llama-')) return AIProviderType.GROQ
  return AIProviderType.OPENAI // default
}

function getDefaultModelForProvider(provider: AIProviderType): string {
  switch (provider) {
    case AIProviderType.OPENAI:
      return 'gpt-4-turbo'
    case AIProviderType.GEMINI:
      return 'gemini-pro'
    case AIProviderType.MISTRAL:
      return 'mistral-medium'
    case AIProviderType.GROQ:
      return 'llama2-70b-4096'
    default:
      return 'gpt-4-turbo'
  }
}

function buildSystemPrompt(agent: any, type: string): string {
  let prompt = "You are an AI assistant that generates social media content."

  if (agent.personality) {
    prompt += ` Your personality: ${JSON.stringify(agent.personality)}.`
  }

  if (agent.tone) {
    prompt += ` Your tone should be ${agent.tone}.`
  }

  if (agent.writingStyle) {
    prompt += ` Your writing style: ${JSON.stringify(agent.writingStyle)}.`
  }

  if (agent.topics && agent.topics.length > 0) {
    prompt += ` You focus on topics like: ${agent.topics.join(', ')}.`
  }

  switch (type) {
    case 'tweet':
      prompt += " Generate a single tweet (max 280 characters). Be engaging and concise."
      break
    case 'thread':
      prompt += " Generate a Twitter thread with multiple connected tweets. Number each tweet."
      break
    case 'reply':
      prompt += " Generate a reply to a social media post. Be relevant and engaging."
      break
    case 'custom':
      prompt += " Generate content as requested."
      break
  }

  return prompt
}

function cleanupCache(): void {
  const now = Date.now()
  for (const [key, value] of cache.entries()) {
    if (value.expiresAt <= now) {
      cache.delete(key)
    }
  }
}
