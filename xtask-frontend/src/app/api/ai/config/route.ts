import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/auth"

// GET /api/ai/config - Get AI provider configurations (without sensitive data)
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const provider = searchParams.get('provider')

    // In a real implementation, you would fetch from database
    // For now, we'll check environment variables
    const configs: Record<string, any> = {}

    const providers = provider ? [provider] : ['openai', 'gemini', 'mistral', 'groq']

    for (const providerName of providers) {
      const envPrefix = providerName.toUpperCase()
      const apiKeyEnv = `${envPrefix}_API_KEY`
      const baseUrlEnv = `${envPrefix}_BASE_URL`

      configs[providerName] = {
        provider: providerName,
        configured: !!process.env[apiKeyEnv],
        hasApiKey: !!process.env[apiKeyEnv],
        hasBaseUrl: !!process.env[baseUrlEnv],
        baseUrl: process.env[baseUrlEnv] || null,
        // Never return the actual API key
        apiKeyMasked: process.env[apiKeyEnv] 
          ? `${process.env[apiKeyEnv].slice(0, 8)}...${process.env[apiKeyEnv].slice(-4)}`
          : null
      }
    }

    if (provider && configs[provider]) {
      return NextResponse.json(configs[provider])
    }

    return NextResponse.json({
      configs,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error("Error getting AI provider configs:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

// POST /api/ai/config - Save AI provider configuration
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { provider, apiKey, baseUrl } = body

    if (!provider || !apiKey) {
      return NextResponse.json(
        { message: "Provider and API key are required" },
        { status: 400 }
      )
    }

    // In a real implementation, you would save to database
    // For now, we'll provide instructions for environment variables
    const envPrefix = provider.toUpperCase()
    const apiKeyEnv = `${envPrefix}_API_KEY`
    const baseUrlEnv = `${envPrefix}_BASE_URL`

    // Simulate saving configuration
    // In production, you would:
    // 1. Encrypt the API key
    // 2. Store in database associated with user
    // 3. Update runtime configuration

    const instructions = [
      `Set environment variable: ${apiKeyEnv}=${apiKey}`,
      ...(baseUrl ? [`Set environment variable: ${baseUrlEnv}=${baseUrl}`] : []),
      'Restart the application to apply changes'
    ]

    return NextResponse.json({
      success: true,
      message: `${provider} configuration saved successfully`,
      provider,
      instructions,
      note: "In this demo, configurations are managed via environment variables. In production, these would be securely stored in the database.",
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error("Error saving AI provider config:", error)
    return NextResponse.json(
      { 
        success: false,
        message: "Internal server error",
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// DELETE /api/ai/config - Remove AI provider configuration
export async function DELETE(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const provider = searchParams.get('provider')

    if (!provider) {
      return NextResponse.json(
        { message: "Provider parameter is required" },
        { status: 400 }
      )
    }

    // In a real implementation, you would remove from database
    const envPrefix = provider.toUpperCase()
    const apiKeyEnv = `${envPrefix}_API_KEY`
    const baseUrlEnv = `${envPrefix}_BASE_URL`

    const instructions = [
      `Remove environment variable: ${apiKeyEnv}`,
      `Remove environment variable: ${baseUrlEnv}`,
      'Restart the application to apply changes'
    ]

    return NextResponse.json({
      success: true,
      message: `${provider} configuration removed successfully`,
      provider,
      instructions,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error("Error removing AI provider config:", error)
    return NextResponse.json(
      { 
        success: false,
        message: "Internal server error",
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
