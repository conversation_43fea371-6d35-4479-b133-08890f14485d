import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/auth"
import { createFactoryFromEnv } from "@/services/ai"

// GET /api/ai/models - Get available models from all providers
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const provider = searchParams.get('provider') // Optional: filter by specific provider

    // Initialize AI provider factory
    const factory = createFactoryFromEnv()
    await factory.initialize()

    const availableProviders = await factory.getAvailableProviders()
    const modelsByProvider: Record<string, any> = {}

    // Get models from each available provider
    for (const providerType of availableProviders) {
      try {
        // Skip if filtering by specific provider
        if (provider && provider !== providerType) {
          continue
        }

        const providerInstance = await factory.getProvider(providerType)
        
        const models = providerInstance.models.map(modelName => {
          const modelInfo = providerInstance.getModelInfo(modelName)
          return {
            id: modelName,
            name: modelName,
            provider: providerType,
            maxTokens: modelInfo?.maxTokens || 4096,
            supportsStreaming: modelInfo?.supportsStreaming || false,
            costPer1kTokens: modelInfo?.costPer1kTokens || { input: 0, output: 0 },
            description: generateModelDescription(modelName, providerType)
          }
        })

        modelsByProvider[providerType] = {
          provider: providerType,
          available: providerInstance.isAvailable(),
          models: models
        }
      } catch (error) {
        console.warn(`Failed to get models for provider ${providerType}:`, error)
        modelsByProvider[providerType] = {
          provider: providerType,
          available: false,
          models: [],
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      }
    }

    // If filtering by provider, return just that provider's data
    if (provider && modelsByProvider[provider]) {
      return NextResponse.json({
        provider: provider,
        ...modelsByProvider[provider]
      })
    }

    // Return all providers and their models
    return NextResponse.json({
      providers: availableProviders,
      modelsByProvider,
      totalModels: Object.values(modelsByProvider).reduce(
        (total, providerData: any) => total + (providerData.models?.length || 0), 
        0
      ),
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error("Error getting AI models:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

// Helper function to generate model descriptions
function generateModelDescription(modelName: string, provider: string): string {
  const descriptions: Record<string, Record<string, string>> = {
    openai: {
      'gpt-4o': 'Latest GPT-4 Omni model with enhanced capabilities',
      'gpt-4o-mini': 'Faster, cost-effective version of GPT-4 Omni',
      'gpt-4-turbo': 'High-performance GPT-4 with 128K context window',
      'gpt-4': 'Original GPT-4 model with excellent reasoning',
      'gpt-3.5-turbo': 'Fast and efficient model for most tasks'
    },
    gemini: {
      'gemini-2.0-flash-exp': 'Latest experimental Gemini model (free preview)',
      'gemini-2.0-flash-001': 'Production Gemini 2.0 Flash model',
      'gemini-1.5-pro': 'Advanced Gemini model with 2M token context',
      'gemini-1.5-flash': 'Fast Gemini model with 1M token context',
      'gemini-1.0-pro': 'Stable Gemini Pro model',
      'gemini-pro': 'Original Gemini Pro model'
    }
  }

  const providerDescriptions = descriptions[provider.toLowerCase()]
  if (providerDescriptions && providerDescriptions[modelName]) {
    return providerDescriptions[modelName]
  }

  // Generate generic description based on model name patterns
  if (modelName.includes('turbo')) {
    return 'Fast and efficient model optimized for speed'
  }
  if (modelName.includes('pro')) {
    return 'Professional-grade model with enhanced capabilities'
  }
  if (modelName.includes('flash')) {
    return 'High-speed model for quick responses'
  }
  if (modelName.includes('mini')) {
    return 'Compact model optimized for cost and speed'
  }
  if (modelName.includes('exp') || modelName.includes('preview')) {
    return 'Experimental model with latest features'
  }

  return `${provider} model: ${modelName}`
}

// POST /api/ai/models/refresh - Refresh models from providers
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { provider } = body

    // Initialize AI provider factory
    const factory = createFactoryFromEnv()
    await factory.initialize()

    if (provider) {
      // Refresh specific provider
      try {
        const providerInstance = await factory.getProvider(provider)
        // Re-initialize to fetch fresh models
        const config = factory.getProviderConfig?.(provider)
        if (config) {
          await providerInstance.initialize(config)
        }
        
        return NextResponse.json({
          message: `Models refreshed for ${provider}`,
          provider: provider,
          models: providerInstance.models
        })
      } catch (error) {
        return NextResponse.json(
          { message: `Failed to refresh models for ${provider}`, error: error instanceof Error ? error.message : 'Unknown error' },
          { status: 400 }
        )
      }
    } else {
      // Refresh all providers
      const availableProviders = await factory.getAvailableProviders()
      const refreshResults: Record<string, any> = {}

      for (const providerType of availableProviders) {
        try {
          const providerInstance = await factory.getProvider(providerType)
          // Re-initialize to fetch fresh models
          const config = factory.getProviderConfig?.(providerType)
          if (config) {
            await providerInstance.initialize(config)
          }
          refreshResults[providerType] = {
            success: true,
            models: providerInstance.models
          }
        } catch (error) {
          refreshResults[providerType] = {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          }
        }
      }

      return NextResponse.json({
        message: "Models refreshed for all providers",
        results: refreshResults,
        timestamp: new Date().toISOString()
      })
    }

  } catch (error) {
    console.error("Error refreshing AI models:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}
