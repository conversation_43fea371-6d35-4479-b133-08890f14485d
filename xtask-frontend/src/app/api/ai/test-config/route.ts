import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/auth"
import { OpenAIProvider } from "@/services/ai/providers/openai"
import { GeminiProvider } from "@/services/ai/providers/gemini"

// POST /api/ai/test-config - Test AI provider configuration
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { provider, apiKey, baseUrl } = body

    if (!provider || !apiKey) {
      return NextResponse.json(
        { message: "Provider and API key are required" },
        { status: 400 }
      )
    }

    let providerInstance
    const config = {
      apiKey,
      baseURL: baseUrl,
      timeout: 10000 // 10 second timeout for testing
    }

    // Create provider instance based on type
    switch (provider.toLowerCase()) {
      case 'openai':
        providerInstance = new OpenAIProvider()
        break
      case 'gemini':
        providerInstance = new GeminiProvider()
        break
      case 'mistral':
        // TODO: Add MistralProvider when implemented
        return NextResponse.json(
          { message: "Mistral provider not yet implemented" },
          { status: 501 }
        )
      case 'groq':
        // TODO: Add GroqProvider when implemented
        return NextResponse.json(
          { message: "Groq provider not yet implemented" },
          { status: 501 }
        )
      default:
        return NextResponse.json(
          { message: `Unknown provider: ${provider}` },
          { status: 400 }
        )
    }

    // Test the configuration
    const startTime = Date.now()
    try {
      const isValid = await providerInstance.validateConfig(config)
      const responseTime = Date.now() - startTime

      if (isValid) {
        // Initialize to get available models
        await providerInstance.initialize(config)
        
        return NextResponse.json({
          success: true,
          message: `${provider} configuration is valid`,
          responseTime,
          modelCount: providerInstance.models.length,
          models: providerInstance.models,
          timestamp: new Date().toISOString()
        })
      } else {
        return NextResponse.json(
          { 
            success: false,
            message: `${provider} configuration is invalid`,
            responseTime
          },
          { status: 400 }
        )
      }
    } catch (error) {
      const responseTime = Date.now() - startTime
      
      return NextResponse.json(
        { 
          success: false,
          message: error instanceof Error ? error.message : 'Configuration test failed',
          responseTime,
          error: {
            name: error instanceof Error ? error.constructor.name : 'UnknownError',
            message: error instanceof Error ? error.message : 'Unknown error occurred'
          }
        },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error("Error testing AI provider config:", error)
    return NextResponse.json(
      { 
        success: false,
        message: "Internal server error",
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
