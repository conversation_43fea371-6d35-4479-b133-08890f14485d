import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/auth"
import { createFactoryFromEnv } from "@/services/ai"

// GET /api/ai/status - Get AI provider status
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    // Initialize AI provider factory
    const factory = createFactoryFromEnv()
    await factory.initialize()

    // Get provider status
    const providerStatuses = await factory.getProviderStatus()
    const availableProviders = await factory.getAvailableProviders()

    return NextResponse.json({
      availableProviders,
      providerStatuses,
      totalProviders: providerStatuses.length,
      availableCount: availableProviders.length,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error("Error getting AI provider status:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}
