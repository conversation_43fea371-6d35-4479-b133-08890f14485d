import { createUploadthing, type FileRouter } from "uploadthing/next";
import { UploadThingError } from "uploadthing/server";
import { auth } from "@/auth";
import { optionalAuth } from "@/lib/auth-middleware";
import { prisma } from "@/lib/db";

const f = createUploadthing();

/**
 * Get authenticated user ID from request
 * Uses hybrid authentication: JWT first, then NextAuth fallback
 */
async function getAuthenticatedUserId(req: Request): Promise<string> {
  // Try JWT authentication first
  const { user: jwtUser } = await optionalAuth(req as any);
  let userId = jwtUser?.id;

  // Fallback to NextAuth if JWT auth failed
  if (!userId) {
    const session = await auth();
    userId = session?.user?.id;
  }

  if (!userId) {
    throw new UploadThingError("Unauthorized - Please sign in to upload files");
  }

  return userId;
}

/**
 * UploadThing File Router
 * Defines different upload endpoints with specific configurations
 */
export const ourFileRouter = {
  // Image uploader - for profile pictures, tweet images, etc.
  imageUploader: f({ 
    image: { 
      maxFileSize: "4MB", 
      maxFileCount: 5 
    } 
  })
    .middleware(async ({ req }) => {
      const userId = await getAuthenticatedUserId(req);
      return { userId };
    })
    .onUploadComplete(async ({ metadata, file }) => {
      console.log("Image upload complete for userId:", metadata.userId);
      console.log("File URL:", file.url);

      try {
        // Save file metadata to database
        const mediaFile = await prisma.mediaFile.create({
          data: {
            filename: file.name,
            originalName: file.name,
            mimeType: file.type || "image/unknown",
            size: file.size,
            url: file.url,
            key: file.key,
            userId: metadata.userId,
            status: "ready",
            processedAt: new Date(),
          },
        });

        console.log("Saved media file to database:", mediaFile.id);
        return { uploadedBy: metadata.userId, fileId: mediaFile.id };
      } catch (error) {
        console.error("Error saving media file to database:", error);
        throw new UploadThingError("Failed to save file metadata");
      }
    }),

  // Video uploader - for video content
  videoUploader: f({ 
    video: { 
      maxFileSize: "256MB", 
      maxFileCount: 1 
    } 
  })
    .middleware(async ({ req }) => {
      const userId = await getAuthenticatedUserId(req);
      return { userId };
    })
    .onUploadComplete(async ({ metadata, file }) => {
      console.log("Video upload complete for userId:", metadata.userId);
      console.log("File URL:", file.url);

      try {
        // Save file metadata to database
        const mediaFile = await prisma.mediaFile.create({
          data: {
            filename: file.name,
            originalName: file.name,
            mimeType: file.type || "video/unknown",
            size: file.size,
            url: file.url,
            key: file.key,
            userId: metadata.userId,
            status: "uploaded", // Videos might need processing
            processedAt: new Date(),
          },
        });

        console.log("Saved video file to database:", mediaFile.id);
        return { uploadedBy: metadata.userId, fileId: mediaFile.id };
      } catch (error) {
        console.error("Error saving video file to database:", error);
        throw new UploadThingError("Failed to save file metadata");
      }
    }),

  // Combined media uploader - for mixed content
  mediaUploader: f({ 
    image: { 
      maxFileSize: "4MB", 
      maxFileCount: 4 
    },
    video: { 
      maxFileSize: "256MB", 
      maxFileCount: 1 
    }
  })
    .middleware(async ({ req }) => {
      const userId = await getAuthenticatedUserId(req);
      return { userId };
    })
    .onUploadComplete(async ({ metadata, file }) => {
      console.log("Media upload complete for userId:", metadata.userId);
      console.log("File URL:", file.url);

      try {
        // Determine if it's an image or video
        const isVideo = file.type?.startsWith("video/");
        
        // Save file metadata to database
        const mediaFile = await prisma.mediaFile.create({
          data: {
            filename: file.name,
            originalName: file.name,
            mimeType: file.type || "unknown",
            size: file.size,
            url: file.url,
            key: file.key,
            userId: metadata.userId,
            status: isVideo ? "uploaded" : "ready",
            processedAt: new Date(),
          },
        });

        console.log("Saved media file to database:", mediaFile.id);
        return { uploadedBy: metadata.userId, fileId: mediaFile.id };
      } catch (error) {
        console.error("Error saving media file to database:", error);
        throw new UploadThingError("Failed to save file metadata");
      }
    }),

  // Avatar uploader - specifically for profile pictures
  avatarUploader: f({ 
    image: { 
      maxFileSize: "2MB", 
      maxFileCount: 1 
    } 
  })
    .middleware(async ({ req }) => {
      const userId = await getAuthenticatedUserId(req);
      return { userId };
    })
    .onUploadComplete(async ({ metadata, file }) => {
      console.log("Avatar upload complete for userId:", metadata.userId);
      console.log("File URL:", file.url);

      try {
        // Save file metadata to database
        const mediaFile = await prisma.mediaFile.create({
          data: {
            filename: file.name,
            originalName: file.name,
            mimeType: file.type || "image/unknown",
            size: file.size,
            url: file.url,
            key: file.key,
            userId: metadata.userId,
            status: "ready",
            processedAt: new Date(),
            altText: "User avatar",
          },
        });

        // Update user's avatar URL
        await prisma.user.update({
          where: { id: metadata.userId },
          data: { avatar: file.url },
        });

        console.log("Updated user avatar and saved to database:", mediaFile.id);
        return { uploadedBy: metadata.userId, fileId: mediaFile.id };
      } catch (error) {
        console.error("Error saving avatar file to database:", error);
        throw new UploadThingError("Failed to save avatar");
      }
    }),
} satisfies FileRouter;

export type OurFileRouter = typeof ourFileRouter;
