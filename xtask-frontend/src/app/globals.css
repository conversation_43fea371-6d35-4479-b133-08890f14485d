@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  /* XTask Dark Theme Colors */
  --primary-50: #faf5ff;
  --primary-500: #8b5cf6;
  --primary-600: #7c3aed;
  --primary-700: #6d28d9;
  --primary-900: #4c1d95;

  --dark-bg: #0a0a0a;
  --dark-surface: #1a1a1a;
  --dark-border: #2a2a2a;
  --dark-text: #ffffff;
  --dark-text-muted: #a1a1aa;

  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Inter', system-ui, sans-serif;
}

body {
  color: var(--dark-text);
  background: var(--dark-bg);
  line-height: 1.6;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--dark-surface);
}

::-webkit-scrollbar-thumb {
  background: var(--dark-border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-500);
}

/* Focus styles */
*:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}