import { UploadTestComponent } from "@/components/upload/UploadTestComponent";

export default function TestUploadPage() {
  return (
    <div className="min-h-screen bg-dark-bg text-dark-text">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2">UploadThing Integration Test</h1>
            <p className="text-gray-400">
              Test the UploadThing integration for media uploads. This page demonstrates
              different upload methods and file handling capabilities.
            </p>
          </div>
          
          <UploadTestComponent />
          
          <div className="mt-8 p-4 bg-dark-surface rounded-lg border border-dark-border">
            <h2 className="text-lg font-semibold mb-2">Integration Details</h2>
            <ul className="text-sm text-gray-400 space-y-1">
              <li>• Image uploads: Max 4MB, up to 5 files</li>
              <li>• Video uploads: Max 256MB, 1 file</li>
              <li>• Avatar uploads: Max 2MB, 1 file</li>
              <li>• Files are saved to UploadThing and metadata stored in database</li>
              <li>• Authentication required via JWT or NextAuth</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
