import { z } from "zod"

// Tweet text validation
const tweetTextSchema = z.string()
  .min(1, "Tweet text cannot be empty")
  .max(280, "Tweet text cannot exceed 280 characters")

// Media ID validation
const mediaIdSchema = z.string().regex(/^\d+$/, "Invalid media ID format")

// Tweet ID validation
const tweetIdSchema = z.string().regex(/^\d+$/, "Invalid tweet ID format")

// User ID validation
const userIdSchema = z.string().regex(/^\d+$/, "Invalid user ID format")

// Basic tweet post schema
export const tweetPostSchema = z.object({
  text: tweetTextSchema,
  agentId: z.string().uuid("Invalid agent ID"),
  mediaIds: z.array(mediaIdSchema).max(4, "Maximum 4 media attachments allowed").optional(),
  replyToTweetId: tweetIdSchema.optional(),
  quoteTweetId: tweetIdSchema.optional(),
  taggedUserIds: z.array(userIdSchema).max(10, "Maximum 10 users can be tagged").optional(),
  replySettings: z.enum(['everyone', 'mentionedUsers', 'following']).default('everyone'),
  scheduledFor: z.string().datetime().optional(),
  geoPlaceId: z.string().optional(),
  forSuperFollowersOnly: z.boolean().default(false),
})

// Thread post schema
export const threadPostSchema = z.object({
  tweets: z.array(z.object({
    text: tweetTextSchema,
    mediaIds: z.array(mediaIdSchema).max(4).optional(),
  })).min(2, "Thread must contain at least 2 tweets").max(25, "Thread cannot exceed 25 tweets"),
  agentId: z.string().uuid("Invalid agent ID"),
  replySettings: z.enum(['everyone', 'mentionedUsers', 'following']).default('everyone'),
  scheduledFor: z.string().datetime().optional(),
})

// Reply post schema
export const replyPostSchema = z.object({
  text: tweetTextSchema,
  agentId: z.string().uuid("Invalid agent ID"),
  inReplyToTweetId: tweetIdSchema,
  mediaIds: z.array(mediaIdSchema).max(4).optional(),
  excludeReplyUserIds: z.array(userIdSchema).optional(),
  scheduledFor: z.string().datetime().optional(),
})

// Media upload schema
export const mediaUploadSchema = z.object({
  agentId: z.string().uuid("Invalid agent ID"),
  mediaCategory: z.enum(['tweet_image', 'tweet_video', 'tweet_gif']).default('tweet_image'),
  altText: z.string().max(1000, "Alt text cannot exceed 1000 characters").optional(),
  additionalOwners: z.array(userIdSchema).optional(),
})

// Scheduled tweet update schema
export const scheduledTweetUpdateSchema = z.object({
  status: z.enum(['pending', 'cancelled']),
  scheduledFor: z.string().datetime().optional(),
  text: tweetTextSchema.optional(),
})

// Tweet deletion schema
export const tweetDeleteSchema = z.object({
  tweetId: tweetIdSchema,
  agentId: z.string().uuid("Invalid agent ID"),
})

// AI content generation request schema
export const aiContentRequestSchema = z.object({
  agentId: z.string().uuid("Invalid agent ID"),
  prompt: z.string().min(1, "Prompt cannot be empty").max(1000, "Prompt too long"),
  context: z.string().max(2000, "Context too long").optional(),
  type: z.enum(['tweet', 'thread', 'reply']).default('tweet'),
  includeMedia: z.boolean().default(false),
  tone: z.enum(['professional', 'casual', 'humorous', 'informative', 'engaging']).optional(),
  hashtags: z.array(z.string().regex(/^[a-zA-Z0-9_]+$/, "Invalid hashtag format")).max(5).optional(),
  mentions: z.array(z.string().regex(/^[a-zA-Z0-9_]+$/, "Invalid username format")).max(5).optional(),
})

// Poll creation schema
export const pollSchema = z.object({
  options: z.array(z.string().min(1).max(25)).min(2, "Poll must have at least 2 options").max(4, "Poll cannot have more than 4 options"),
  durationMinutes: z.number().int().min(5, "Poll duration must be at least 5 minutes").max(10080, "Poll duration cannot exceed 7 days"),
})

// Enhanced tweet post schema with poll support
export const enhancedTweetPostSchema = tweetPostSchema.extend({
  poll: pollSchema.optional(),
})

// Bulk tweet operations schema
export const bulkTweetSchema = z.object({
  agentId: z.string().uuid("Invalid agent ID"),
  tweets: z.array(tweetPostSchema.omit({ agentId: true })).min(1).max(10, "Maximum 10 tweets in bulk operation"),
  delayBetweenTweets: z.number().int().min(1000, "Minimum 1 second delay").max(3600000, "Maximum 1 hour delay").default(60000), // 1 minute default
})

// Tweet analytics request schema
export const tweetAnalyticsSchema = z.object({
  tweetIds: z.array(tweetIdSchema).min(1).max(100, "Maximum 100 tweets per request"),
  agentId: z.string().uuid("Invalid agent ID"),
  metrics: z.array(z.enum([
    'impressions', 'engagements', 'engagement_rate', 'retweets', 'likes', 
    'replies', 'quotes', 'bookmarks', 'profile_clicks', 'url_clicks'
  ])).optional(),
})

// Export types
export type TweetPostRequest = z.infer<typeof tweetPostSchema>
export type ThreadPostRequest = z.infer<typeof threadPostSchema>
export type ReplyPostRequest = z.infer<typeof replyPostSchema>
export type MediaUploadRequest = z.infer<typeof mediaUploadSchema>
export type ScheduledTweetUpdateRequest = z.infer<typeof scheduledTweetUpdateSchema>
export type TweetDeleteRequest = z.infer<typeof tweetDeleteSchema>
export type AIContentRequest = z.infer<typeof aiContentRequestSchema>
export type PollRequest = z.infer<typeof pollSchema>
export type EnhancedTweetPostRequest = z.infer<typeof enhancedTweetPostSchema>
export type BulkTweetRequest = z.infer<typeof bulkTweetSchema>
export type TweetAnalyticsRequest = z.infer<typeof tweetAnalyticsSchema>

// Validation helper functions
export function validateTweetLength(text: string, mediaCount: number = 0): boolean {
  // Twitter's character counting is complex, but this is a basic implementation
  // URLs are counted as 23 characters regardless of actual length
  // Media attachments don't count against character limit
  const urlRegex = /https?:\/\/[^\s]+/g
  const urls = text.match(urlRegex) || []
  const urlCharacters = urls.reduce((total, url) => total + Math.min(url.length, 23), 0)
  const nonUrlCharacters = text.replace(urlRegex, '').length
  
  return (nonUrlCharacters + urlCharacters) <= 280
}

export function extractHashtags(text: string): string[] {
  const hashtagRegex = /#([a-zA-Z0-9_]+)/g
  const matches = text.match(hashtagRegex) || []
  return matches.map(tag => tag.substring(1)) // Remove the # symbol
}

export function extractMentions(text: string): string[] {
  const mentionRegex = /@([a-zA-Z0-9_]+)/g
  const matches = text.match(mentionRegex) || []
  return matches.map(mention => mention.substring(1)) // Remove the @ symbol
}

export function extractUrls(text: string): string[] {
  const urlRegex = /https?:\/\/[^\s]+/g
  return text.match(urlRegex) || []
}
