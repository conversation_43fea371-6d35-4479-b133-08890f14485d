import passport from 'passport';
import { Strategy as GoogleStrategy } from 'passport-google-oauth20';
import { Strategy as TwitterStrategy } from 'passport-twitter';
import { prisma } from '../db';
import { getEnv } from '../env';

// Get validated environment variables
const env = getEnv();

// Configure Google OAuth Strategy
passport.use(new GoogleStrategy({
  clientID: env.GOOGLE_CLIENT_ID,
  clientSecret: env.GOOGLE_CLIENT_SECRET,
  callbackURL: "/api/auth/google/callback"
}, async (accessToken, refreshToken, profile, done) => {
  try {
    console.log('Google OAuth callback received:', profile.id);
    
    // Check if user already exists
    let user = await prisma.user.findUnique({
      where: { googleId: profile.id }
    });

    if (user) {
      // Update existing user with latest profile info
      user = await prisma.user.update({
        where: { id: user.id },
        data: {
          name: profile.displayName || user.name,
          avatar: profile.photos?.[0]?.value || user.avatar,
        }
      });
      console.log('Existing Google user logged in:', user.id);
      return done(null, user);
    }

    // Check if user exists with same email
    const existingEmailUser = await prisma.user.findUnique({
      where: { email: profile.emails?.[0]?.value || '' }
    });

    if (existingEmailUser) {
      // Link Google account to existing user
      user = await prisma.user.update({
        where: { id: existingEmailUser.id },
        data: {
          googleId: profile.id,
          name: profile.displayName || existingEmailUser.name,
          avatar: profile.photos?.[0]?.value || existingEmailUser.avatar,
        }
      });
      console.log('Linked Google account to existing user:', user.id);
      return done(null, user);
    }

    // Create new user
    user = await prisma.user.create({
      data: {
        googleId: profile.id,
        email: profile.emails?.[0]?.value || '',
        name: profile.displayName || 'Google User',
        avatar: profile.photos?.[0]?.value,
        timezone: 'UTC',
        theme: 'dark',
        notifications: true,
        defaultModel: 'gpt-4',
        fallbackModel: 'gemini-pro',
        maxTokensPerDay: 10000,
        tokensUsedToday: 0,
        lastTokenReset: new Date(),
      }
    });

    console.log('New Google user created:', user.id);
    return done(null, user);
  } catch (error) {
    console.error('Google OAuth error:', error);
    return done(error, null);
  }
}));

// Configure Twitter OAuth Strategy
passport.use(new TwitterStrategy({
  consumerKey: env.TWITTER_CLIENT_ID,
  consumerSecret: env.TWITTER_CLIENT_SECRET,
  callbackURL: "/api/auth/twitter/callback",
  includeEmail: true
}, async (token, tokenSecret, profile, done) => {
  try {
    console.log('Twitter OAuth callback received:', profile.id);
    
    // Check if user already exists
    let user = await prisma.user.findUnique({
      where: { twitterId: profile.id }
    });

    if (user) {
      // Update existing user with latest profile info
      user = await prisma.user.update({
        where: { id: user.id },
        data: {
          name: profile.displayName || user.name,
          avatar: profile.photos?.[0]?.value || user.avatar,
        }
      });
      console.log('Existing Twitter user logged in:', user.id);
      return done(null, user);
    }

    // Check if user exists with same email
    const email = profile.emails?.[0]?.value;
    if (email) {
      const existingEmailUser = await prisma.user.findUnique({
        where: { email }
      });

      if (existingEmailUser) {
        // Link Twitter account to existing user
        user = await prisma.user.update({
          where: { id: existingEmailUser.id },
          data: {
            twitterId: profile.id,
            name: profile.displayName || existingEmailUser.name,
            avatar: profile.photos?.[0]?.value || existingEmailUser.avatar,
          }
        });
        console.log('Linked Twitter account to existing user:', user.id);
        return done(null, user);
      }
    }

    // Create new user
    user = await prisma.user.create({
      data: {
        twitterId: profile.id,
        email: email || `twitter_${profile.id}@xtask.local`,
        name: profile.displayName || profile.username || 'Twitter User',
        avatar: profile.photos?.[0]?.value,
        timezone: 'UTC',
        theme: 'dark',
        notifications: true,
        defaultModel: 'gpt-4',
        fallbackModel: 'gemini-pro',
        maxTokensPerDay: 10000,
        tokensUsedToday: 0,
        lastTokenReset: new Date(),
      }
    });

    console.log('New Twitter user created:', user.id);
    return done(null, user);
  } catch (error) {
    console.error('Twitter OAuth error:', error);
    return done(error, null);
  }
}));

// Serialize user for session
passport.serializeUser((user: any, done) => {
  done(null, user.id);
});

// Deserialize user from session
passport.deserializeUser(async (id: string, done) => {
  try {
    const user = await prisma.user.findUnique({
      where: { id },
      include: {
        agents: true,
        twitterAccounts: true,
      }
    });
    done(null, user);
  } catch (error) {
    done(error, null);
  }
});

export default passport;
