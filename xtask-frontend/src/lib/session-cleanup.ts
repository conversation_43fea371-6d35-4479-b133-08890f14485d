import { sessionService } from './session'

/**
 * Session cleanup service for removing expired sessions
 */
class SessionCleanupService {
  private cleanupInterval: NodeJS.Timeout | null = null
  private readonly CLEANUP_INTERVAL_MS = 60 * 60 * 1000 // 1 hour
  private readonly MAX_SESSION_AGE_DAYS = 30 // Delete sessions older than 30 days

  /**
   * Start the cleanup service
   */
  start(): void {
    if (this.cleanupInterval) {
      console.log('Session cleanup service is already running')
      return
    }

    console.log('Starting session cleanup service...')
    
    // Run cleanup immediately
    this.runCleanup()

    // Schedule periodic cleanup
    this.cleanupInterval = setInterval(() => {
      this.runCleanup()
    }, this.CLEANUP_INTERVAL_MS)

    console.log(`Session cleanup service started (interval: ${this.CLEANUP_INTERVAL_MS / 1000}s)`)
  }

  /**
   * Stop the cleanup service
   */
  stop(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
      console.log('Session cleanup service stopped')
    }
  }

  /**
   * Run the cleanup process
   */
  private async runCleanup(): Promise<void> {
    try {
      console.log('Running session cleanup...')

      // Clean up expired sessions
      const expiredCount = await sessionService.cleanupExpiredSessions()
      
      // Clean up old revoked sessions (older than MAX_SESSION_AGE_DAYS)
      const oldSessionsCount = await this.cleanupOldSessions()

      console.log(`Session cleanup completed: ${expiredCount} expired, ${oldSessionsCount} old sessions removed`)
    } catch (error) {
      console.error('Session cleanup failed:', error)
    }
  }

  /**
   * Remove old revoked sessions to prevent database bloat
   */
  private async cleanupOldSessions(): Promise<number> {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - this.MAX_SESSION_AGE_DAYS)

    try {
      const { prisma } = await import('@/lib/prisma')
      
      const result = await prisma.jWTSession.deleteMany({
        where: {
          isRevoked: true,
          revokedAt: {
            lt: cutoffDate
          }
        }
      })

      return result.count
    } catch (error) {
      console.error('Failed to cleanup old sessions:', error)
      return 0
    }
  }

  /**
   * Get cleanup statistics
   */
  async getCleanupStats(): Promise<{
    totalSessions: number
    activeSessions: number
    expiredSessions: number
    revokedSessions: number
    oldSessions: number
  }> {
    try {
      const { prisma } = await import('@/lib/prisma')
      const now = new Date()
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - this.MAX_SESSION_AGE_DAYS)

      const [
        totalSessions,
        activeSessions,
        expiredSessions,
        revokedSessions,
        oldSessions
      ] = await Promise.all([
        prisma.jWTSession.count(),
        prisma.jWTSession.count({
          where: {
            isRevoked: false,
            expiresAt: { gt: now }
          }
        }),
        prisma.jWTSession.count({
          where: {
            isRevoked: false,
            expiresAt: { lt: now }
          }
        }),
        prisma.jWTSession.count({
          where: {
            isRevoked: true
          }
        }),
        prisma.jWTSession.count({
          where: {
            isRevoked: true,
            revokedAt: { lt: cutoffDate }
          }
        })
      ])

      return {
        totalSessions,
        activeSessions,
        expiredSessions,
        revokedSessions,
        oldSessions
      }
    } catch (error) {
      console.error('Failed to get cleanup stats:', error)
      return {
        totalSessions: 0,
        activeSessions: 0,
        expiredSessions: 0,
        revokedSessions: 0,
        oldSessions: 0
      }
    }
  }

  /**
   * Force cleanup (for manual triggering)
   */
  async forceCleanup(): Promise<{ expiredCount: number; oldSessionsCount: number }> {
    const expiredCount = await sessionService.cleanupExpiredSessions()
    const oldSessionsCount = await this.cleanupOldSessions()
    
    return { expiredCount, oldSessionsCount }
  }
}

// Export singleton instance
export const sessionCleanupService = new SessionCleanupService()

// Auto-start in production
if (process.env.NODE_ENV === 'production') {
  // Start cleanup service when module is imported
  sessionCleanupService.start()
}

// Graceful shutdown
if (typeof process !== 'undefined') {
  process.on('SIGTERM', () => {
    sessionCleanupService.stop()
  })
  
  process.on('SIGINT', () => {
    sessionCleanupService.stop()
  })
}
