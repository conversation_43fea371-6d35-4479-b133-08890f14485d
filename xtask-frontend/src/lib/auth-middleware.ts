import { NextRequest, NextResponse } from 'next/server'
import { jwtService } from './jwt'
import { sessionService } from './session'
import type { User } from '@/generated/prisma'

export interface AuthenticatedRequest extends NextRequest {
  user?: User
  sessionId?: string
  jti?: string
}

export interface AuthMiddlewareOptions {
  required?: boolean // If true, returns 401 when no valid token
  roles?: string[] // Future: role-based access control
  skipPaths?: string[] // Paths to skip authentication
}

/**
 * Extract JWT token from request cookies
 */
function extractTokenFromRequest(request: NextRequest): string | null {
  // Try to get token from HTTP-only cookie
  const tokenCookie = request.cookies.get('auth-token')
  if (tokenCookie?.value) {
    return tokenCookie.value
  }

  // Fallback: try Authorization header (for API clients)
  const authHeader = request.headers.get('authorization')
  if (authHeader?.startsWith('Bearer ')) {
    return authHeader.substring(7)
  }

  return null
}

/**
 * Create authentication middleware
 */
export function createAuthMiddleware(options: AuthMiddlewareOptions = {}) {
  return async function authMiddleware(
    request: NextRequest,
    response?: NextResponse
  ): Promise<{ user: User | null; error: string | null }> {
    const { required = false, skipPaths = [] } = options

    // Check if path should be skipped
    const pathname = request.nextUrl.pathname
    if (skipPaths.some(path => pathname.startsWith(path))) {
      return { user: null, error: null }
    }

    // Extract token from request
    const token = extractTokenFromRequest(request)
    if (!token) {
      if (required) {
        return { user: null, error: 'Authentication required' }
      }
      return { user: null, error: null }
    }

    try {
      // Verify JWT token
      const payload = jwtService.verifyToken(token)
      if (!payload) {
        if (required) {
          return { user: null, error: 'Invalid or expired token' }
        }
        return { user: null, error: null }
      }

      // Validate session in database
      const sessionData = await sessionService.getSessionFromToken(token)
      if (!sessionData) {
        if (required) {
          return { user: null, error: 'Session not found or expired' }
        }
        return { user: null, error: null }
      }

      // Attach user data to request (if using in API routes)
      if (request as any) {
        (request as any).user = sessionData.user
        (request as any).sessionId = sessionData.session.id
        (request as any).jti = sessionData.session.jti
      }

      return { user: sessionData.user, error: null }
    } catch (error) {
      console.error('Auth middleware error:', error)
      if (required) {
        return { user: null, error: 'Authentication failed' }
      }
      return { user: null, error: null }
    }
  }
}

/**
 * Middleware for API routes that require authentication
 */
export const requireAuth = createAuthMiddleware({ required: true })

/**
 * Middleware for API routes with optional authentication
 */
export const optionalAuth = createAuthMiddleware({ required: false })

/**
 * Higher-order function to protect API routes
 */
export function withAuth(
  handler: (request: NextRequest, context: { user: User }) => Promise<NextResponse>,
  options: AuthMiddlewareOptions = {}
) {
  return async function protectedHandler(
    request: NextRequest,
    context: any
  ): Promise<NextResponse> {
    const authMiddleware = createAuthMiddleware({ ...options, required: true })
    const { user, error } = await authMiddleware(request)

    if (error || !user) {
      return NextResponse.json(
        { message: error || 'Authentication required' },
        { status: 401 }
      )
    }

    return handler(request, { ...context, user })
  }
}

/**
 * Set authentication cookie
 */
export function setAuthCookie(response: NextResponse, token: string, expiresAt: Date): void {
  response.cookies.set('auth-token', token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    expires: expiresAt,
    path: '/'
  })
}

/**
 * Clear authentication cookie
 */
export function clearAuthCookie(response: NextResponse): void {
  response.cookies.set('auth-token', '', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    expires: new Date(0),
    path: '/'
  })
}

/**
 * Get user from request (for use in API routes)
 */
export function getUserFromRequest(request: NextRequest): User | null {
  return (request as any).user || null
}

/**
 * Get session ID from request (for use in API routes)
 */
export function getSessionIdFromRequest(request: NextRequest): string | null {
  return (request as any).sessionId || null
}

/**
 * Get JWT ID from request (for use in API routes)
 */
export function getJTIFromRequest(request: NextRequest): string | null {
  return (request as any).jti || null
}

/**
 * Validate request and return user or throw error
 */
export async function validateAuthenticatedRequest(request: NextRequest): Promise<User> {
  const { user, error } = await requireAuth(request)
  
  if (error || !user) {
    throw new Error(error || 'Authentication required')
  }
  
  return user
}

// Export types
export type { AuthenticatedRequest, AuthMiddlewareOptions }
