import { prisma } from '@/lib/prisma'
import { jwtService, type TokenPair } from './jwt'
import type { JWTSession, User } from '@/generated/prisma'

export interface SessionData {
  userId: string
  jti: string
  ipAddress?: string
  userAgent?: string
  deviceInfo?: any
  loginMethod?: string
}

export interface CreateSessionOptions {
  ipAddress?: string
  userAgent?: string
  deviceInfo?: any
  loginMethod?: string
}

class SessionService {
  /**
   * Create a new JWT session in the database
   */
  async createSession(
    userId: string, 
    options: CreateSessionOptions = {}
  ): Promise<{ tokens: TokenPair; session: JWTSession }> {
    // Generate JWT tokens
    const tokens = jwtService.generateTokenPair(userId)
    
    // Create session record in database
    const session = await prisma.jWTSession.create({
      data: {
        jti: tokens.jti,
        userId,
        expiresAt: tokens.refreshTokenExpiry,
        ipAddress: options.ipAddress,
        userAgent: options.userAgent,
        deviceInfo: options.deviceInfo,
        loginMethod: options.loginMethod || 'oauth',
        lastUsedAt: new Date()
      }
    })

    return { tokens, session }
  }

  /**
   * Validate a session by JWT ID
   */
  async validateSession(jti: string): Promise<JWTSession | null> {
    const session = await prisma.jWTSession.findUnique({
      where: { jti },
      include: { user: true }
    })

    // Check if session exists and is not revoked
    if (!session || session.isRevoked) {
      return null
    }

    // Check if session is expired
    if (session.expiresAt < new Date()) {
      // Auto-revoke expired session
      await this.revokeSession(jti, 'system', 'expired')
      return null
    }

    // Update last used timestamp
    await this.updateLastUsed(jti)

    return session
  }

  /**
   * Update the last used timestamp for a session
   */
  async updateLastUsed(jti: string): Promise<void> {
    await prisma.jWTSession.update({
      where: { jti },
      data: { lastUsedAt: new Date() }
    })
  }

  /**
   * Revoke a specific session
   */
  async revokeSession(
    jti: string, 
    revokedBy: string = 'user', 
    reason: string = 'logout'
  ): Promise<boolean> {
    try {
      await prisma.jWTSession.update({
        where: { jti },
        data: {
          isRevoked: true,
          revokedAt: new Date(),
          revokedBy,
          revokeReason: reason
        }
      })
      return true
    } catch (error) {
      console.error('Failed to revoke session:', error)
      return false
    }
  }

  /**
   * Revoke all sessions for a user
   */
  async revokeAllUserSessions(
    userId: string, 
    revokedBy: string = 'user', 
    reason: string = 'logout_all'
  ): Promise<number> {
    const result = await prisma.jWTSession.updateMany({
      where: {
        userId,
        isRevoked: false
      },
      data: {
        isRevoked: true,
        revokedAt: new Date(),
        revokedBy,
        revokeReason: reason
      }
    })

    return result.count
  }

  /**
   * Get all active sessions for a user
   */
  async getUserSessions(userId: string): Promise<JWTSession[]> {
    return prisma.jWTSession.findMany({
      where: {
        userId,
        isRevoked: false,
        expiresAt: { gt: new Date() }
      },
      orderBy: { lastUsedAt: 'desc' }
    })
  }

  /**
   * Clean up expired sessions
   */
  async cleanupExpiredSessions(): Promise<number> {
    const result = await prisma.jWTSession.updateMany({
      where: {
        expiresAt: { lt: new Date() },
        isRevoked: false
      },
      data: {
        isRevoked: true,
        revokedAt: new Date(),
        revokedBy: 'system',
        revokeReason: 'expired'
      }
    })

    return result.count
  }

  /**
   * Get session statistics for a user
   */
  async getSessionStats(userId: string) {
    const [total, active, revoked] = await Promise.all([
      prisma.jWTSession.count({
        where: { userId }
      }),
      prisma.jWTSession.count({
        where: {
          userId,
          isRevoked: false,
          expiresAt: { gt: new Date() }
        }
      }),
      prisma.jWTSession.count({
        where: {
          userId,
          isRevoked: true
        }
      })
    ])

    return { total, active, revoked }
  }

  /**
   * Refresh a session with new tokens
   */
  async refreshSession(jti: string): Promise<{ tokens: TokenPair; session: JWTSession } | null> {
    const session = await this.validateSession(jti)
    if (!session) {
      return null
    }

    // Revoke the old session
    await this.revokeSession(jti, 'system', 'refresh')

    // Create a new session
    return this.createSession(session.userId, {
      ipAddress: session.ipAddress || undefined,
      userAgent: session.userAgent || undefined,
      deviceInfo: session.deviceInfo || undefined,
      loginMethod: session.loginMethod || undefined
    })
  }

  /**
   * Get session by JWT token
   */
  async getSessionFromToken(token: string): Promise<{ session: JWTSession; user: User } | null> {
    const payload = jwtService.verifyToken(token)
    if (!payload) {
      return null
    }

    const session = await prisma.jWTSession.findUnique({
      where: { jti: payload.jti },
      include: { user: true }
    })

    if (!session || session.isRevoked || session.expiresAt < new Date()) {
      return null
    }

    return { session, user: session.user }
  }
}

// Export singleton instance
export const sessionService = new SessionService()

// Export types
export type { SessionData, CreateSessionOptions }
