import jwt from 'jsonwebtoken'
import { randomBytes } from 'crypto'
import { getEnv } from './env'

export interface JWTPayload {
  sub: string // User ID
  jti: string // JWT ID for revocation
  iat: number // Issued at
  exp: number // Expires at
  type: 'access' | 'refresh'
  sessionId?: string // Database session ID
}

export interface TokenPair {
  accessToken: string
  refreshToken: string
  accessTokenExpiry: Date
  refreshTokenExpiry: Date
  jti: string
}

class JWTService {
  private readonly jwtSecret: string
  private readonly accessTokenExpiry = '15m' // 15 minutes
  private readonly refreshTokenExpiry = '7d' // 7 days

  constructor() {
    const env = getEnv()
    this.jwtSecret = env.JWT_SECRET
  }

  /**
   * Generate a unique JWT ID
   */
  private generateJTI(): string {
    return randomBytes(16).toString('hex')
  }

  /**
   * Generate access and refresh token pair
   */
  generateTokenPair(userId: string, sessionId?: string): TokenPair {
    const jti = this.generateJTI()
    const now = Math.floor(Date.now() / 1000)

    // Access token payload
    const accessPayload: JWTPayload = {
      sub: userId,
      jti,
      iat: now,
      exp: now + 15 * 60, // 15 minutes
      type: 'access',
      sessionId
    }

    // Refresh token payload
    const refreshPayload: JWTPayload = {
      sub: userId,
      jti,
      iat: now,
      exp: now + 7 * 24 * 60 * 60, // 7 days
      type: 'refresh',
      sessionId
    }

    const accessToken = jwt.sign(accessPayload, this.jwtSecret, {
      algorithm: 'HS256',
      expiresIn: this.accessTokenExpiry
    })

    const refreshToken = jwt.sign(refreshPayload, this.jwtSecret, {
      algorithm: 'HS256',
      expiresIn: this.refreshTokenExpiry
    })

    return {
      accessToken,
      refreshToken,
      accessTokenExpiry: new Date(accessPayload.exp * 1000),
      refreshTokenExpiry: new Date(refreshPayload.exp * 1000),
      jti
    }
  }

  /**
   * Verify and decode a JWT token
   */
  verifyToken(token: string): JWTPayload | null {
    try {
      const decoded = jwt.verify(token, this.jwtSecret, {
        algorithms: ['HS256']
      }) as JWTPayload

      // Validate required fields
      if (!decoded.sub || !decoded.jti || !decoded.type) {
        return null
      }

      return decoded
    } catch (error) {
      // Token is invalid, expired, or malformed
      return null
    }
  }

  /**
   * Decode token without verification (for debugging)
   */
  decodeToken(token: string): JWTPayload | null {
    try {
      const decoded = jwt.decode(token) as JWTPayload
      return decoded
    } catch (error) {
      return null
    }
  }

  /**
   * Check if token is expired
   */
  isTokenExpired(token: string): boolean {
    const decoded = this.decodeToken(token)
    if (!decoded || !decoded.exp) {
      return true
    }

    const now = Math.floor(Date.now() / 1000)
    return decoded.exp < now
  }

  /**
   * Extract user ID from token
   */
  getUserIdFromToken(token: string): string | null {
    const decoded = this.verifyToken(token)
    return decoded?.sub || null
  }

  /**
   * Extract JWT ID from token
   */
  getJTIFromToken(token: string): string | null {
    const decoded = this.verifyToken(token)
    return decoded?.jti || null
  }

  /**
   * Generate a secure random session ID
   */
  generateSessionId(): string {
    return randomBytes(32).toString('hex')
  }

  /**
   * Create a token for password reset or email verification
   */
  generateVerificationToken(userId: string, purpose: 'password-reset' | 'email-verification'): string {
    const payload = {
      sub: userId,
      purpose,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (purpose === 'password-reset' ? 3600 : 86400) // 1h for reset, 24h for verification
    }

    return jwt.sign(payload, this.jwtSecret, {
      algorithm: 'HS256'
    })
  }

  /**
   * Verify a verification token
   */
  verifyVerificationToken(token: string, purpose: 'password-reset' | 'email-verification'): { userId: string } | null {
    try {
      const decoded = jwt.verify(token, this.jwtSecret, {
        algorithms: ['HS256']
      }) as any

      if (decoded.purpose !== purpose) {
        return null
      }

      return { userId: decoded.sub }
    } catch (error) {
      return null
    }
  }
}

// Export singleton instance
export const jwtService = new JWTService()

// Export types
export type { JWTPayload, TokenPair }
