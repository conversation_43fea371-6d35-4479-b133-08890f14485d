import { PrismaClient } from '@/generated/prisma';

// Global variable to store the Prisma client instance
declare global {
  var __prisma: PrismaClient | undefined;
}

// Create a singleton Prisma client instance
// This prevents multiple instances in development due to hot reloading
export const prisma = globalThis.__prisma || new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
});

// In development, store the client on the global object to prevent multiple instances
if (process.env.NODE_ENV === 'development') {
  globalThis.__prisma = prisma;
}

// Database connection test function
export async function testDatabaseConnection() {
  try {
    await prisma.$connect();
    console.log('✅ Database connected successfully');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return false;
  }
}

// Graceful shutdown function
export async function disconnectDatabase() {
  try {
    await prisma.$disconnect();
    console.log('🔌 Database disconnected');
  } catch (error) {
    console.error('❌ Error disconnecting from database:', error);
  }
}

// Database health check function
export async function getDatabaseHealth() {
  try {
    const result = await prisma.$queryRaw`SELECT 1 as health`;
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      result
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Vector embedding utility functions for AgentMemory (Neon + pgvector optimized)
export async function storeAgentMemory(
  agentId: string,
  content: string,
  embedding?: number[],
  options?: {
    context?: string;
    type?: string;
    importance?: number;
    tags?: string[];
    relatedTo?: string;
  }
) {
  // Convert embedding array to pgvector string format: [0.1,0.2,0.3]
  const embeddingString = embedding ? `[${embedding.join(',')}]` : null;

  if (embedding) {
    // Use raw SQL for proper pgvector insertion
    return await prisma.$executeRaw`
      INSERT INTO "AgentMemory" (
        id, "agentId", content, context, type, importance, tags, "relatedTo",
        embedding, "embeddingModel", "embeddingDim", "createdAt", "updatedAt"
      ) VALUES (
        gen_random_uuid()::text, ${agentId}, ${content}, ${options?.context || null},
        ${options?.type || 'interaction'}, ${options?.importance || 0.5},
        ${options?.tags || []}::text[], ${options?.relatedTo || null},
        ${embeddingString}::vector, ${'text-embedding-ada-002'}, ${embedding.length},
        NOW(), NOW()
      )
    `;
  } else {
    // Regular Prisma create for non-vector data
    return await prisma.agentMemory.create({
      data: {
        agentId,
        content,
        context: options?.context,
        type: options?.type || 'interaction',
        importance: options?.importance || 0.5,
        tags: options?.tags || [],
        relatedTo: options?.relatedTo,
        embeddingModel: null,
        embeddingDim: null,
      },
    });
  }
}

// Search agent memories by similarity using pgvector (Neon optimized)
export async function searchAgentMemories(
  agentId: string,
  queryEmbedding?: number[],
  textQuery?: string,
  limit: number = 10
) {
  if (queryEmbedding) {
    // Vector similarity search using pgvector cosine distance
    const queryVectorString = `[${queryEmbedding.join(',')}]`;

    return await prisma.$queryRaw`
      SELECT
        id,
        "agentId",
        content,
        context,
        type,
        importance,
        tags,
        "relatedTo",
        embedding,
        "embeddingModel",
        "embeddingDim",
        "createdAt",
        "updatedAt",
        embedding <-> ${queryVectorString}::vector AS distance
      FROM "AgentMemory"
      WHERE "agentId" = ${agentId}
        AND embedding IS NOT NULL
      ORDER BY distance ASC
      LIMIT ${limit}
    `;
  } else if (textQuery) {
    // Fallback to text search when no embedding provided
    return await prisma.agentMemory.findMany({
      where: {
        agentId,
        OR: [
          { content: { contains: textQuery, mode: 'insensitive' } },
          { context: { contains: textQuery, mode: 'insensitive' } },
          { tags: { has: textQuery } },
        ],
      },
      orderBy: [
        { importance: 'desc' },
        { createdAt: 'desc' },
      ],
      take: limit,
    });
  } else {
    throw new Error('Either queryEmbedding or textQuery must be provided');
  }
}

export default prisma;
