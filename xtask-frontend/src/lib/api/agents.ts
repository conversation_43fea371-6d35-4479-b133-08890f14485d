import { Agent, AgentListItem, CreateAgentData, UpdateAgentData, AgentApiError } from "@/types/agent"

const API_BASE = "/api/agents"

// Custom error class for API errors
export class AgentApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public errors?: any
  ) {
    super(message)
    this.name = "AgentApiError"
  }
}

// Helper function to handle API responses
async function handleResponse<T>(response: Response): Promise<T> {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: "Unknown error" }))
    throw new AgentApiError(
      errorData.message || `HTTP ${response.status}`,
      response.status,
      errorData.errors
    )
  }
  return response.json()
}

// Fetch all agents for the current user
export async function fetchAgents(): Promise<AgentListItem[]> {
  const response = await fetch(API_BASE, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  })
  
  return handleResponse<AgentListItem[]>(response)
}

// Fetch a specific agent by ID
export async function fetchAgent(id: string): Promise<Agent> {
  const response = await fetch(`${API_BASE}/${id}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  })
  
  return handleResponse<Agent>(response)
}

// Create a new agent
export async function createAgent(data: CreateAgentData): Promise<Agent> {
  const response = await fetch(API_BASE, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  })
  
  return handleResponse<Agent>(response)
}

// Update an existing agent
export async function updateAgent(id: string, data: UpdateAgentData): Promise<Agent> {
  const response = await fetch(`${API_BASE}/${id}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  })
  
  return handleResponse<Agent>(response)
}

// Delete an agent
export async function deleteAgent(id: string): Promise<{ message: string }> {
  const response = await fetch(`${API_BASE}/${id}`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
    },
  })
  
  return handleResponse<{ message: string }>(response)
}

// Toggle agent active status
export async function toggleAgentStatus(id: string, isActive: boolean): Promise<Agent> {
  return updateAgent(id, { isActive })
}

// Batch operations (for future use)
export async function batchUpdateAgents(updates: Array<{ id: string; data: UpdateAgentData }>): Promise<Agent[]> {
  // This would require a batch endpoint on the backend
  // For now, we'll do individual updates
  const results = await Promise.all(
    updates.map(({ id, data }) => updateAgent(id, data))
  )
  return results
}

// Search/filter agents (client-side for now, could be moved to backend)
export function filterAgents(
  agents: AgentListItem[],
  filters: {
    search?: string
    isActive?: boolean
    sortBy?: 'name' | 'createdAt' | 'totalTweets' | 'avgEngagement'
    sortOrder?: 'asc' | 'desc'
  }
): AgentListItem[] {
  let filtered = [...agents]

  // Apply search filter
  if (filters.search) {
    const searchLower = filters.search.toLowerCase()
    filtered = filtered.filter(
      agent =>
        agent.name.toLowerCase().includes(searchLower) ||
        agent.description?.toLowerCase().includes(searchLower)
    )
  }

  // Apply active status filter
  if (filters.isActive !== undefined) {
    filtered = filtered.filter(agent => agent.isActive === filters.isActive)
  }

  // Apply sorting
  if (filters.sortBy) {
    filtered.sort((a, b) => {
      const aValue = a[filters.sortBy!]
      const bValue = b[filters.sortBy!]
      
      let comparison = 0
      if (aValue < bValue) comparison = -1
      if (aValue > bValue) comparison = 1
      
      return filters.sortOrder === 'desc' ? -comparison : comparison
    })
  }

  return filtered
}
