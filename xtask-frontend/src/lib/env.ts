import { z } from 'zod';

// Environment variable validation schema
const envSchema = z.object({
  // Server Configuration
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().default('3030'),

  // Database
  DATABASE_URL: z.string().min(1, 'Database URL is required'),

  // Authentication & Security
  JWT_SECRET: z.string().min(32, 'JWT secret must be at least 32 characters'),
  SESSION_SECRET: z.string().min(32, 'Session secret must be at least 32 characters'),
  AUTH_SECRET: z.string().min(32, 'NextAuth secret must be at least 32 characters'),

  // Google OAuth (NextAuth.js format)
  AUTH_GOOGLE_ID: z.string().min(1, 'Google Client ID is required'),
  AUTH_GOOGLE_SECRET: z.string().min(1, 'Google Client Secret is required'),
  // Legacy format (optional)
  GOOGLE_CLIENT_ID: z.string().optional(),
  GOOGLE_CLIENT_SECRET: z.string().optional(),

  // Twitter/X OAuth (NextAuth.js format)
  AUTH_TWITTER_ID: z.string().min(1, 'Twitter Client ID is required'),
  AUTH_TWITTER_SECRET: z.string().min(1, 'Twitter Client Secret is required'),
  // Legacy format (optional)
  TWITTER_CLIENT_ID: z.string().optional(),
  TWITTER_CLIENT_SECRET: z.string().optional(),

  // AI API Keys
  GEMINI_API_KEY: z.string().optional(),
  GOOGLE_API_KEY: z.string().optional(),
  OPENAI_API_KEY: z.string().optional(),
  MISTRAL_API_KEY: z.string().optional(),
  HUGGINGFACE_API_KEY: z.string().optional(),
  GROQ_API_KEY: z.string().optional(),
  OPENROUTER_API_KEY: z.string().optional(),

  // UploadThing (Optional - for media uploads)
  UPLOADTHING_SECRET: z.string().optional(),
  UPLOADTHING_APP_ID: z.string().optional(),
  UPLOADTHING_TOKEN: z.string().optional(),

  // Optional Development
  DEBUG: z.string().optional(),
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
});

// Validate and parse environment variables
function validateEnv() {
  try {
    const env = envSchema.parse(process.env);
    return { success: true, data: env, errors: null };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors = error.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message,
        code: err.code,
      }));
      return { success: false, data: null, errors };
    }
    return {
      success: false,
      data: null,
      errors: [{ field: 'unknown', message: 'Unknown validation error', code: 'unknown' }]
    };
  }
}

// Get validated environment variables
export function getEnv() {
  const result = validateEnv();

  if (!result.success) {
    console.error('❌ Environment validation failed:');
    result.errors?.forEach(error => {
      console.error(`  - ${error.field}: ${error.message}`);
    });
    throw new Error('Invalid environment configuration');
  }

  return result.data;
}

// Check if specific features are available based on API keys
export function getFeatureAvailability() {
  const env = getEnv();

  return {
    // AI Providers
    openai: !!env.OPENAI_API_KEY,
    gemini: !!env.GEMINI_API_KEY || !!env.GOOGLE_API_KEY,
    mistral: !!env.MISTRAL_API_KEY,
    huggingface: !!env.HUGGINGFACE_API_KEY,
    groq: !!env.GROQ_API_KEY,
    openrouter: !!env.OPENROUTER_API_KEY,

    // OAuth Providers (NextAuth.js format)
    googleOAuth: !!env.AUTH_GOOGLE_ID && !!env.AUTH_GOOGLE_SECRET,
    twitterOAuth: !!env.AUTH_TWITTER_ID && !!env.AUTH_TWITTER_SECRET,

    // Media Upload
    uploadthing: !!env.UPLOADTHING_SECRET && !!env.UPLOADTHING_APP_ID,

    // Database
    database: !!env.DATABASE_URL,
  };
}

// Get AI provider configuration
export function getAIProviders() {
  const env = getEnv();
  const providers = [];

  if (env.OPENAI_API_KEY) {
    providers.push({
      name: 'OpenAI',
      id: 'openai',
      models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
      available: true,
    });
  }

  if (env.GEMINI_API_KEY || env.GOOGLE_API_KEY) {
    providers.push({
      name: 'Google Gemini',
      id: 'gemini',
      models: ['gemini-pro', 'gemini-pro-vision'],
      available: true,
    });
  }

  if (env.MISTRAL_API_KEY) {
    providers.push({
      name: 'Mistral',
      id: 'mistral',
      models: ['mistral-large', 'mistral-medium', 'mistral-small'],
      available: true,
    });
  }

  if (env.GROQ_API_KEY) {
    providers.push({
      name: 'Groq',
      id: 'groq',
      models: ['llama2-70b-4096', 'mixtral-8x7b-32768'],
      available: true,
    });
  }

  if (env.OPENROUTER_API_KEY) {
    providers.push({
      name: 'OpenRouter',
      id: 'openrouter',
      models: ['openai/gpt-4', 'anthropic/claude-3-opus'],
      available: true,
    });
  }

  return providers;
}

// Environment health check
export function checkEnvironmentHealth() {
  const validation = validateEnv();
  const features = getFeatureAvailability();
  const providers = getAIProviders();

  return {
    valid: validation.success,
    errors: validation.errors || [],
    features,
    aiProviders: providers,
    summary: {
      totalProviders: providers.length,
      oauthReady: features.googleOAuth && features.twitterOAuth,
      databaseReady: features.database,
      mediaReady: features.uploadthing,
    },
  };
}

export default getEnv;
