import { prisma } from '@/lib/db'
import { embeddingService, EmbeddingService, EmbeddingResult } from '@/services/embeddings'
import { AgentMemory } from '@prisma/client'

// Agent memory interfaces
export interface CreateMemoryInput {
  agentId: string
  content: string
  context?: string
  type?: string
  importance?: number
  tags?: string[]
  relatedTo?: string
  generateEmbedding?: boolean
}

export interface MemorySearchOptions {
  limit?: number
  threshold?: number
  includeEmbedding?: boolean
  type?: string
  tags?: string[]
  minImportance?: number
}

export interface MemorySearchResult extends Omit<AgentMemory, 'embedding'> {
  similarity?: number
  embedding?: number[]
}

export interface MemoryStats {
  totalMemories: number
  memoriesWithEmbeddings: number
  averageImportance: number
  memoryTypes: { type: string; count: number }[]
  topTags: { tag: string; count: number }[]
}

/**
 * Agent Memory Service
 * Handles storage, retrieval, and similarity search of agent memories with vector embeddings
 */
export class AgentMemoryService {
  private embeddingService: EmbeddingService

  constructor(embeddingService?: EmbeddingService) {
    this.embeddingService = embeddingService || embeddingService
  }

  /**
   * Create a new agent memory with optional embedding generation
   */
  async createMemory(input: CreateMemoryInput): Promise<AgentMemory> {
    const {
      agentId,
      content,
      context,
      type = 'interaction',
      importance = 0.5,
      tags = [],
      relatedTo,
      generateEmbedding = true
    } = input

    // Validate input
    if (!agentId || !content?.trim()) {
      throw new Error('Agent ID and content are required')
    }

    if (importance < 0 || importance > 1) {
      throw new Error('Importance must be between 0 and 1')
    }

    let embeddingData: EmbeddingResult | null = null
    let embeddingString: string | null = null

    // Generate embedding if requested
    if (generateEmbedding) {
      try {
        embeddingData = await this.embeddingService.generateEmbedding(content)
        embeddingString = EmbeddingService.embeddingToVector(embeddingData.embedding)
      } catch (error) {
        console.error('Failed to generate embedding for memory:', error)
        // Continue without embedding rather than failing completely
      }
    }

    // Create memory record
    const memory = await prisma.agentMemory.create({
      data: {
        agentId,
        content: content.trim(),
        context: context?.trim() || null,
        type,
        importance,
        tags,
        relatedTo,
        embedding: embeddingString,
        embeddingModel: embeddingData?.model || null,
        embeddingDim: embeddingData?.dimensions || null,
      }
    })

    return memory
  }

  /**
   * Create multiple memories in batch with embeddings
   */
  async createBatchMemories(inputs: CreateMemoryInput[]): Promise<AgentMemory[]> {
    if (!inputs || inputs.length === 0) {
      return []
    }

    // Separate inputs that need embeddings
    const withEmbeddings = inputs.filter(input => input.generateEmbedding !== false)
    const withoutEmbeddings = inputs.filter(input => input.generateEmbedding === false)

    const results: AgentMemory[] = []

    // Process memories without embeddings first
    for (const input of withoutEmbeddings) {
      const memory = await this.createMemory({ ...input, generateEmbedding: false })
      results.push(memory)
    }

    // Generate embeddings in batch for efficiency
    if (withEmbeddings.length > 0) {
      try {
        const texts = withEmbeddings.map(input => input.content)
        const batchResult = await this.embeddingService.generateBatchEmbeddings(texts)

        // Create memories with embeddings
        for (let i = 0; i < withEmbeddings.length; i++) {
          const input = withEmbeddings[i]
          const embeddingData = batchResult.embeddings[i]
          const embeddingString = EmbeddingService.embeddingToVector(embeddingData.embedding)

          const memory = await prisma.agentMemory.create({
            data: {
              agentId: input.agentId,
              content: input.content.trim(),
              context: input.context?.trim() || null,
              type: input.type || 'interaction',
              importance: input.importance || 0.5,
              tags: input.tags || [],
              relatedTo: input.relatedTo,
              embedding: embeddingString,
              embeddingModel: embeddingData.model,
              embeddingDim: embeddingData.dimensions,
            }
          })

          results.push(memory)
        }
      } catch (error) {
        console.error('Failed to generate batch embeddings:', error)
        
        // Fallback: create memories without embeddings
        for (const input of withEmbeddings) {
          const memory = await this.createMemory({ ...input, generateEmbedding: false })
          results.push(memory)
        }
      }
    }

    return results
  }

  /**
   * Search memories by semantic similarity using vector embeddings
   */
  async searchMemoriesBySimilarity(
    agentId: string,
    query: string,
    options: MemorySearchOptions = {}
  ): Promise<MemorySearchResult[]> {
    const {
      limit = 10,
      threshold = 0.7,
      includeEmbedding = false,
      type,
      tags,
      minImportance
    } = options

    // Generate embedding for query
    let queryEmbedding: EmbeddingResult
    try {
      queryEmbedding = await this.embeddingService.generateEmbedding(query)
    } catch (error) {
      console.error('Failed to generate query embedding:', error)
      throw new Error('Failed to generate embedding for search query')
    }

    const queryVector = EmbeddingService.embeddingToVector(queryEmbedding.embedding)

    // Build WHERE conditions
    const whereConditions = [`"agentId" = $1`, `embedding IS NOT NULL`]
    const params: any[] = [agentId]
    let paramIndex = 2

    if (type) {
      whereConditions.push(`type = $${paramIndex}`)
      params.push(type)
      paramIndex++
    }

    if (minImportance !== undefined) {
      whereConditions.push(`importance >= $${paramIndex}`)
      params.push(minImportance)
      paramIndex++
    }

    if (tags && tags.length > 0) {
      whereConditions.push(`tags && $${paramIndex}::text[]`)
      params.push(tags)
      paramIndex++
    }

    // Add similarity threshold
    whereConditions.push(`embedding <-> $${paramIndex}::vector <= $${paramIndex + 1}`)
    params.push(queryVector, 1 - threshold) // pgvector distance is 1 - similarity
    paramIndex += 2

    const whereClause = whereConditions.join(' AND ')

    // Execute similarity search using raw SQL for pgvector
    const query_sql = `
      SELECT
        id,
        "agentId",
        content,
        context,
        type,
        importance,
        tags,
        "relatedTo",
        "embeddingModel",
        "embeddingDim",
        "createdAt",
        "updatedAt",
        ${includeEmbedding ? 'embedding,' : ''}
        1 - (embedding <-> $${paramIndex - 2}::vector) AS similarity
      FROM "agent_memories"
      WHERE ${whereClause}
      ORDER BY embedding <-> $${paramIndex - 2}::vector ASC
      LIMIT $${paramIndex}
    `

    params.push(limit)

    const results = await prisma.$queryRawUnsafe(query_sql, ...params) as any[]

    return results.map(row => ({
      id: row.id,
      agentId: row.agentId,
      content: row.content,
      context: row.context,
      type: row.type,
      importance: row.importance,
      tags: row.tags,
      relatedTo: row.relatedTo,
      embeddingModel: row.embeddingModel,
      embeddingDim: row.embeddingDim,
      createdAt: row.createdAt,
      updatedAt: row.updatedAt,
      similarity: parseFloat(row.similarity),
      embedding: includeEmbedding && row.embedding 
        ? EmbeddingService.vectorToEmbedding(row.embedding)
        : undefined
    }))
  }

  /**
   * Search memories by text content (fallback when embeddings are not available)
   */
  async searchMemoriesByText(
    agentId: string,
    query: string,
    options: MemorySearchOptions = {}
  ): Promise<MemorySearchResult[]> {
    const {
      limit = 10,
      type,
      tags,
      minImportance
    } = options

    const whereConditions: any = {
      agentId,
      OR: [
        { content: { contains: query, mode: 'insensitive' } },
        { context: { contains: query, mode: 'insensitive' } },
      ]
    }

    if (type) {
      whereConditions.type = type
    }

    if (minImportance !== undefined) {
      whereConditions.importance = { gte: minImportance }
    }

    if (tags && tags.length > 0) {
      whereConditions.tags = { hasSome: tags }
    }

    const memories = await prisma.agentMemory.findMany({
      where: whereConditions,
      orderBy: [
        { importance: 'desc' },
        { createdAt: 'desc' }
      ],
      take: limit
    })

    return memories.map(memory => ({
      ...memory,
      embedding: undefined // Don't include embedding in text search
    }))
  }

  /**
   * Get memory by ID
   */
  async getMemory(id: string, includeEmbedding = false): Promise<MemorySearchResult | null> {
    const memory = await prisma.agentMemory.findUnique({
      where: { id }
    })

    if (!memory) {
      return null
    }

    return {
      ...memory,
      embedding: includeEmbedding && memory.embedding 
        ? EmbeddingService.vectorToEmbedding(memory.embedding)
        : undefined
    }
  }

  /**
   * Update memory content and regenerate embedding if needed
   */
  async updateMemory(
    id: string,
    updates: Partial<CreateMemoryInput>,
    regenerateEmbedding = false
  ): Promise<AgentMemory | null> {
    const existingMemory = await prisma.agentMemory.findUnique({
      where: { id }
    })

    if (!existingMemory) {
      return null
    }

    let embeddingData: EmbeddingResult | null = null
    let embeddingString: string | null = existingMemory.embedding

    // Regenerate embedding if content changed or explicitly requested
    if (regenerateEmbedding || (updates.content && updates.content !== existingMemory.content)) {
      const contentToEmbed = updates.content || existingMemory.content
      try {
        embeddingData = await this.embeddingService.generateEmbedding(contentToEmbed)
        embeddingString = EmbeddingService.embeddingToVector(embeddingData.embedding)
      } catch (error) {
        console.error('Failed to regenerate embedding:', error)
        // Keep existing embedding
      }
    }

    const updatedMemory = await prisma.agentMemory.update({
      where: { id },
      data: {
        content: updates.content?.trim(),
        context: updates.context?.trim(),
        type: updates.type,
        importance: updates.importance,
        tags: updates.tags,
        relatedTo: updates.relatedTo,
        embedding: embeddingString,
        embeddingModel: embeddingData?.model || existingMemory.embeddingModel,
        embeddingDim: embeddingData?.dimensions || existingMemory.embeddingDim,
      }
    })

    return updatedMemory
  }

  /**
   * Delete memory by ID
   */
  async deleteMemory(id: string): Promise<boolean> {
    try {
      await prisma.agentMemory.delete({
        where: { id }
      })
      return true
    } catch (error) {
      return false
    }
  }

  /**
   * Get all memories for an agent
   */
  async getAgentMemories(
    agentId: string,
    options: MemorySearchOptions = {}
  ): Promise<MemorySearchResult[]> {
    const {
      limit = 50,
      type,
      tags,
      minImportance,
      includeEmbedding = false
    } = options

    const whereConditions: any = { agentId }

    if (type) {
      whereConditions.type = type
    }

    if (minImportance !== undefined) {
      whereConditions.importance = { gte: minImportance }
    }

    if (tags && tags.length > 0) {
      whereConditions.tags = { hasSome: tags }
    }

    const memories = await prisma.agentMemory.findMany({
      where: whereConditions,
      orderBy: [
        { importance: 'desc' },
        { createdAt: 'desc' }
      ],
      take: limit
    })

    return memories.map(memory => ({
      ...memory,
      embedding: includeEmbedding && memory.embedding 
        ? EmbeddingService.vectorToEmbedding(memory.embedding)
        : undefined
    }))
  }

  /**
   * Get memory statistics for an agent
   */
  async getMemoryStats(agentId: string): Promise<MemoryStats> {
    const [
      totalCount,
      embeddingCount,
      avgImportance,
      typeStats,
      tagStats
    ] = await Promise.all([
      // Total memories
      prisma.agentMemory.count({ where: { agentId } }),
      
      // Memories with embeddings
      prisma.agentMemory.count({ 
        where: { agentId, embedding: { not: null } } 
      }),
      
      // Average importance
      prisma.agentMemory.aggregate({
        where: { agentId },
        _avg: { importance: true }
      }),
      
      // Memory types
      prisma.agentMemory.groupBy({
        by: ['type'],
        where: { agentId },
        _count: { type: true }
      }),
      
      // Tag statistics (this is a simplified version)
      prisma.agentMemory.findMany({
        where: { agentId },
        select: { tags: true }
      })
    ])

    // Process tag statistics
    const tagCounts: Record<string, number> = {}
    tagStats.forEach(memory => {
      memory.tags.forEach(tag => {
        tagCounts[tag] = (tagCounts[tag] || 0) + 1
      })
    })

    const topTags = Object.entries(tagCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([tag, count]) => ({ tag, count }))

    return {
      totalMemories: totalCount,
      memoriesWithEmbeddings: embeddingCount,
      averageImportance: avgImportance._avg.importance || 0,
      memoryTypes: typeStats.map(stat => ({
        type: stat.type,
        count: stat._count.type
      })),
      topTags
    }
  }
}

// Export singleton instance
export const agentMemoryService = new AgentMemoryService()

// Export types
export type {
  CreateMemoryInput,
  MemorySearchOptions,
  MemorySearchResult,
  MemoryStats
}
