import OpenAI from 'openai'
import { getEnv } from '@/lib/env'

// Embedding service configuration
export interface EmbeddingConfig {
  model: string
  dimensions: number
  maxTokens: number
  batchSize: number
}

// Embedding result interface
export interface EmbeddingResult {
  embedding: number[]
  tokens: number
  model: string
  dimensions: number
}

// Batch embedding result
export interface BatchEmbeddingResult {
  embeddings: EmbeddingResult[]
  totalTokens: number
  model: string
  dimensions: number
}

// Embedding service errors
export class EmbeddingError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly retryable: boolean = false
  ) {
    super(message)
    this.name = 'EmbeddingError'
  }
}

export class EmbeddingRateLimitError extends EmbeddingError {
  constructor(
    message: string,
    public readonly retryAfter: number
  ) {
    super(message, 'RATE_LIMIT', true)
  }
}

// Default embedding configuration
const DEFAULT_CONFIG: EmbeddingConfig = {
  model: 'text-embedding-3-large',
  dimensions: 1024, // Optimal for pgvector performance
  maxTokens: 8191, // Max tokens for text-embedding-3-large
  batchSize: 100, // Max batch size for OpenAI embeddings API
}

/**
 * Embedding Service for generating vector embeddings using OpenAI's text-embedding models
 * Optimized for agent memory storage with pgvector
 */
export class EmbeddingService {
  private client: OpenAI
  private config: EmbeddingConfig

  constructor(config: Partial<EmbeddingConfig> = {}) {
    const env = getEnv()
    
    this.client = new OpenAI({
      apiKey: env.OPENAI_API_KEY,
      timeout: 30000,
      maxRetries: 0, // We handle retries ourselves
    })

    this.config = { ...DEFAULT_CONFIG, ...config }
  }

  /**
   * Generate embedding for a single text
   */
  async generateEmbedding(text: string): Promise<EmbeddingResult> {
    if (!text || text.trim().length === 0) {
      throw new EmbeddingError('Text cannot be empty', 'INVALID_INPUT')
    }

    try {
      const response = await this.client.embeddings.create({
        model: this.config.model,
        input: text.trim(),
        dimensions: this.config.dimensions,
        encoding_format: 'float',
      })

      const data = response.data[0]
      if (!data || !data.embedding) {
        throw new EmbeddingError('No embedding returned from API', 'API_ERROR')
      }

      return {
        embedding: data.embedding,
        tokens: response.usage.total_tokens,
        model: this.config.model,
        dimensions: this.config.dimensions,
      }
    } catch (error: any) {
      throw this.handleError(error)
    }
  }

  /**
   * Generate embeddings for multiple texts in batches
   */
  async generateBatchEmbeddings(texts: string[]): Promise<BatchEmbeddingResult> {
    if (!texts || texts.length === 0) {
      throw new EmbeddingError('Texts array cannot be empty', 'INVALID_INPUT')
    }

    // Filter out empty texts
    const validTexts = texts.filter(text => text && text.trim().length > 0)
    if (validTexts.length === 0) {
      throw new EmbeddingError('No valid texts provided', 'INVALID_INPUT')
    }

    const results: EmbeddingResult[] = []
    let totalTokens = 0

    // Process in batches to respect API limits
    for (let i = 0; i < validTexts.length; i += this.config.batchSize) {
      const batch = validTexts.slice(i, i + this.config.batchSize)
      
      try {
        const response = await this.client.embeddings.create({
          model: this.config.model,
          input: batch.map(text => text.trim()),
          dimensions: this.config.dimensions,
          encoding_format: 'float',
        })

        // Process batch results
        for (let j = 0; j < response.data.length; j++) {
          const data = response.data[j]
          if (!data || !data.embedding) {
            throw new EmbeddingError(`No embedding returned for text ${i + j}`, 'API_ERROR')
          }

          results.push({
            embedding: data.embedding,
            tokens: 0, // Individual token count not available in batch
            model: this.config.model,
            dimensions: this.config.dimensions,
          })
        }

        totalTokens += response.usage.total_tokens
      } catch (error: any) {
        throw this.handleError(error)
      }
    }

    return {
      embeddings: results,
      totalTokens,
      model: this.config.model,
      dimensions: this.config.dimensions,
    }
  }

  /**
   * Calculate cosine similarity between two embeddings
   */
  static cosineSimilarity(a: number[], b: number[]): number {
    if (a.length !== b.length) {
      throw new EmbeddingError('Embeddings must have the same dimensions', 'DIMENSION_MISMATCH')
    }

    let dotProduct = 0
    let normA = 0
    let normB = 0

    for (let i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i]
      normA += a[i] * a[i]
      normB += b[i] * b[i]
    }

    const magnitude = Math.sqrt(normA) * Math.sqrt(normB)
    if (magnitude === 0) {
      return 0
    }

    return dotProduct / magnitude
  }

  /**
   * Convert embedding array to pgvector string format
   */
  static embeddingToVector(embedding: number[]): string {
    return `[${embedding.join(',')}]`
  }

  /**
   * Convert pgvector string to embedding array
   */
  static vectorToEmbedding(vector: string): number[] {
    // Remove brackets and split by comma
    const cleaned = vector.replace(/^\[|\]$/g, '')
    return cleaned.split(',').map(num => parseFloat(num.trim()))
  }

  /**
   * Validate embedding dimensions
   */
  validateEmbedding(embedding: number[]): boolean {
    return embedding.length === this.config.dimensions
  }

  /**
   * Get current configuration
   */
  getConfig(): EmbeddingConfig {
    return { ...this.config }
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<EmbeddingConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * Handle API errors and convert to EmbeddingError
   */
  private handleError(error: any): EmbeddingError {
    if (error instanceof EmbeddingError) {
      return error
    }

    // Handle OpenAI specific errors
    if (error.status) {
      switch (error.status) {
        case 401:
          return new EmbeddingError('Invalid OpenAI API key', 'AUTHENTICATION_ERROR')
        case 429:
          const retryAfter = this.parseRetryAfter(error)
          return new EmbeddingRateLimitError('Rate limit exceeded', retryAfter)
        case 400:
          return new EmbeddingError(`Invalid request: ${error.message}`, 'INVALID_REQUEST')
        case 500:
        case 502:
        case 503:
        case 504:
          return new EmbeddingError(`Server error: ${error.message}`, 'SERVER_ERROR', true)
        default:
          return new EmbeddingError(`API error: ${error.message}`, 'API_ERROR', true)
      }
    }

    // Handle network errors
    if (error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT') {
      return new EmbeddingError('Network error', 'NETWORK_ERROR', true)
    }

    return new EmbeddingError(`Unknown error: ${error.message}`, 'UNKNOWN_ERROR', true)
  }

  /**
   * Parse retry-after header from rate limit error
   */
  private parseRetryAfter(error: any): number {
    const retryAfter = error.headers?.['retry-after'] || error.response?.headers?.['retry-after']
    return retryAfter ? parseInt(retryAfter, 10) : 60 // Default to 60 seconds
  }
}

// Export singleton instance
export const embeddingService = new EmbeddingService()

// Export types
export type { EmbeddingConfig, EmbeddingResult, BatchEmbeddingResult }
