import { 
  IAIProvider, 
  AIProviderConfig, 
  AIMessage, 
  AIGenerationOptions, 
  AIResponse,
  AIProviderError,
  RateLimitError,
  AuthenticationError,
  InvalidRequestError,
  ServerError,
  NetworkError
} from '@/types/ai'

// Base class for all AI providers
export abstract class BaseAIProvider implements IAIProvider {
  protected config?: AIProviderConfig
  protected initialized = false

  constructor(
    public readonly name: string,
    public readonly models: string[]
  ) {}

  async initialize(config: AIProviderConfig): Promise<void> {
    this.config = config
    await this.validateConfig(config)
    this.initialized = true
  }

  abstract generateContent(
    messages: AIMessage[],
    options?: AIGenerationOptions
  ): Promise<AIResponse>

  abstract validateConfig(config: AIProviderConfig): Promise<boolean>

  abstract getModelInfo(model: string): {
    maxTokens: number
    supportsStreaming: boolean
    costPer1kTokens: {
      input: number
      output: number
    }
  } | null

  isAvailable(): boolean {
    return this.initialized && !!this.config?.apiKey
  }

  protected ensureInitialized(): void {
    if (!this.initialized || !this.config) {
      throw new AIProviderError(
        `Provider ${this.name} is not initialized`,
        'NOT_INITIALIZED',
        'invalid_request',
        undefined,
        undefined,
        false
      )
    }
  }

  protected handleError(error: any): never {
    if (error instanceof AIProviderError) {
      throw error
    }

    // Handle common HTTP status codes
    if (error.status || error.response?.status) {
      const status = error.status || error.response.status
      const message = error.message || error.response?.data?.message || 'Unknown error'

      switch (status) {
        case 401:
          throw new AuthenticationError(message, this.name)
        case 429:
          const retryAfter = this.parseRetryAfter(error)
          const resetTime = new Date(Date.now() + retryAfter * 1000)
          throw new RateLimitError(message, resetTime, retryAfter)
        case 400:
          throw new InvalidRequestError(message, error.response?.data)
        case 500:
        case 502:
        case 503:
        case 504:
          throw new ServerError(message, status)
        default:
          throw new AIProviderError(
            message,
            'UNKNOWN_ERROR',
            'server_error',
            status,
            error.response?.data,
            status >= 500
          )
      }
    }

    // Handle network errors
    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND' || error.code === 'ETIMEDOUT') {
      throw new NetworkError(`Network error: ${error.message}`)
    }

    // Generic error
    throw new AIProviderError(
      error.message || 'Unknown error occurred',
      'UNKNOWN_ERROR',
      'server_error',
      undefined,
      error,
      false
    )
  }

  private parseRetryAfter(error: any): number {
    // Try to get retry-after from headers
    const retryAfter = error.response?.headers?.['retry-after'] || 
                      error.response?.headers?.['Retry-After']
    
    if (retryAfter) {
      const seconds = parseInt(retryAfter, 10)
      if (!isNaN(seconds)) {
        return seconds
      }
    }

    // Default retry after 60 seconds for rate limits
    return 60
  }
}

// Retry utility with exponential backoff
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error as Error

      // Don't retry non-retryable errors
      if (error instanceof AIProviderError && !error.retryable) {
        throw error
      }

      // Don't retry on the last attempt
      if (attempt === maxRetries) {
        break
      }

      // Calculate delay with exponential backoff and jitter
      const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 1000
      
      // For rate limit errors, respect the retry-after header
      if (error instanceof RateLimitError) {
        const rateLimitDelay = error.retryAfter * 1000
        await new Promise(resolve => setTimeout(resolve, Math.max(delay, rateLimitDelay)))
      } else {
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }
  }

  throw lastError!
}

// Message formatting utilities
export function formatMessagesForProvider(
  messages: AIMessage[],
  providerName: string
): any[] {
  switch (providerName.toLowerCase()) {
    case 'openai':
      return messages.map(msg => ({
        role: msg.role,
        content: msg.content,
        ...(msg.name && { name: msg.name })
      }))
    
    case 'gemini':
      // Gemini uses a different format
      return messages
        .filter(msg => msg.role !== 'system') // Gemini handles system messages differently
        .map(msg => ({
          role: msg.role === 'assistant' ? 'model' : 'user',
          parts: [{ text: msg.content }]
        }))
    
    default:
      return messages
  }
}

// Token counting utilities (approximate)
export function estimateTokenCount(text: string): number {
  // Rough estimation: 1 token ≈ 4 characters for English text
  return Math.ceil(text.length / 4)
}

export function calculateCost(
  inputTokens: number,
  outputTokens: number,
  inputCostPer1k: number,
  outputCostPer1k: number
): number {
  const inputCost = (inputTokens / 1000) * inputCostPer1k
  const outputCost = (outputTokens / 1000) * outputCostPer1k
  return inputCost + outputCost
}

// Configuration validation
export function validateProviderConfig(config: AIProviderConfig): void {
  if (!config.apiKey) {
    throw new InvalidRequestError('API key is required')
  }

  if (config.timeout && (config.timeout < 1000 || config.timeout > 300000)) {
    throw new InvalidRequestError('Timeout must be between 1000ms and 300000ms')
  }

  if (config.maxRetries && (config.maxRetries < 0 || config.maxRetries > 10)) {
    throw new InvalidRequestError('Max retries must be between 0 and 10')
  }
}
