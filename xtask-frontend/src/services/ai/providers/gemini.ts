import { GoogleGenAI } from '@google/genai'
import {
  AIMessage,
  AIGenerationOptions,
  AIResponse,
  AIProviderConfig,
  AuthenticationError,
  RateLimitError,
  InvalidRequestError,
  ServerError,
  NetworkError
} from '@/types/ai'
import { BaseAIProvider, withRetry, estimateTokenCount, calculateCost } from '../base'

export class GeminiProvider extends BaseAIProvider {
  private client?: GoogleGenAI
  private availableModels: string[] = []

  constructor() {
    // Start with empty models array - will be populated dynamically
    super('Gemini', [])
  }

  async initialize(config: AIProviderConfig): Promise<void> {
    await super.initialize(config)

    // Initialize client with base URL if provided
    const clientConfig: any = { apiKey: config.apiKey }
    if (config.baseURL) {
      clientConfig.baseURL = config.baseURL
    }

    this.client = new GoogleGenAI(clientConfig)

    // Fetch available models dynamically
    await this.fetchAvailableModels()

    // Update the models array in the parent class
    this.models.length = 0
    this.models.push(...this.availableModels)
  }

  async generateContent(
    messages: AIMessage[],
    options: AIGenerationOptions = {}
  ): Promise<AIResponse> {
    this.ensureInitialized()

    const operation = async (): Promise<AIResponse> => {
      try {
        const modelName = options.model || this.availableModels[0] || 'gemini-2.0-flash-001'

        // Convert messages to new Gemini format
        const contents = this.formatMessagesForGemini(messages)

        // Prepare generation config
        const config = {
          temperature: options.temperature ?? 0.7,
          maxOutputTokens: options.maxTokens,
          topP: options.topP,
          stopSequences: options.stop,
          // Add safety settings
          safetySettings: [
            {
              category: 'HARM_CATEGORY_HARASSMENT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE',
            },
            {
              category: 'HARM_CATEGORY_HATE_SPEECH',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE',
            },
            {
              category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE',
            },
            {
              category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE',
            },
          ],
        }

        let result
        if (contents.length === 1 && contents[0].role === 'user') {
          // Single user message - use generateContent
          result = await this.client!.models.generateContent({
            model: modelName,
            contents: contents[0].parts[0].text,
            config
          })
        } else {
          // Multiple messages - use chat
          const chat = this.client!.chats.create({
            model: modelName,
            config,
            history: contents.slice(0, -1)
          })

          const lastMessage = contents[contents.length - 1]
          result = await chat.sendMessage({
            message: lastMessage.parts[0].text
          })
        }

        const text = result.text
        if (!text) {
          throw new InvalidRequestError('No content generated by Gemini')
        }

        // Get usage metadata from response
        const usageMetadata = result.usageMetadata || {}
        const promptTokens = usageMetadata.promptTokenCount || this.estimatePromptTokens(messages)
        const completionTokens = usageMetadata.candidatesTokenCount || estimateTokenCount(text)
        const totalTokens = usageMetadata.totalTokenCount || (promptTokens + completionTokens)

        return {
          content: text,
          model: modelName,
          usage: {
            promptTokens,
            completionTokens,
            totalTokens
          },
          finishReason: this.mapFinishReason(result.candidates?.[0]?.finishReason),
          metadata: {
            candidates: result.candidates,
            promptFeedback: result.promptFeedback,
            usageMetadata: result.usageMetadata
          }
        }
      } catch (error) {
        this.handleError(error)
      }
    }

    return withRetry(operation, this.config?.maxRetries || 3, this.config?.retryDelay || 1000)
  }

  async validateConfig(config: AIProviderConfig): Promise<boolean> {
    try {
      const clientConfig: any = { apiKey: config.apiKey }
      if (config.baseURL) {
        clientConfig.baseURL = config.baseURL
      }

      const testClient = new GoogleGenAI(clientConfig)

      // Try to fetch models first to validate connection
      try {
        await this.fetchModelsFromEndpoint(testClient)
        return true
      } catch (modelError) {
        // If model fetching fails, try a simple generation request
        const defaultModel = await this.getDefaultModel(testClient)
        await testClient.models.generateContent({
          model: defaultModel,
          contents: 'Hi'
        })
        return true
      }
    } catch (error: any) {
      if (error.message?.includes('API key') || error.status === 401) {
        throw new AuthenticationError('Invalid Gemini API key', this.name)
      }
      throw error
    }
  }

  private async fetchAvailableModels(): Promise<void> {
    try {
      if (!this.client) {
        throw new Error('Client not initialized')
      }

      // Try to fetch models from endpoint
      this.availableModels = await this.fetchModelsFromEndpoint(this.client)
    } catch (error) {
      console.warn('Failed to fetch models from endpoint, using fallback list:', error)
      // Fallback to default models if endpoint fails
      this.availableModels = this.getDefaultModels()
    }
  }

  private async fetchModelsFromEndpoint(client: GoogleGenAI): Promise<string[]> {
    try {
      // Check if there's a custom endpoint for listing models
      if (this.config?.baseURL) {
        // For custom endpoints, try to fetch models list
        const response = await fetch(`${this.config.baseURL}/models`, {
          headers: {
            'Authorization': `Bearer ${this.config.apiKey}`,
            'Content-Type': 'application/json'
          }
        })

        if (response.ok) {
          const data = await response.json()
          // Extract model names from response (format may vary by provider)
          if (data.models && Array.isArray(data.models)) {
            return data.models.map((model: any) => model.name || model.id || model)
          }
          if (data.data && Array.isArray(data.data)) {
            return data.data.map((model: any) => model.name || model.id || model)
          }
        }
      }

      // For official Google API, we don't have a public models endpoint
      // so we'll use the default list
      return this.getDefaultModels()
    } catch (error) {
      console.warn('Error fetching models from endpoint:', error)
      return this.getDefaultModels()
    }
  }

  private getDefaultModels(): string[] {
    return [
      'gemini-2.0-flash-exp',
      'gemini-2.0-flash-001',
      'gemini-1.5-pro',
      'gemini-1.5-pro-001',
      'gemini-1.5-flash',
      'gemini-1.5-flash-001',
      'gemini-1.0-pro',
      'gemini-pro'
    ]
  }

  private async getDefaultModel(client: GoogleGenAI): Promise<string> {
    const defaultModels = this.getDefaultModels()
    return defaultModels[0] // Return first available model
  }

  getModelInfo(model: string) {
    // Known model configurations (fallback for when endpoint doesn't provide details)
    const knownModelInfo: Record<string, any> = {
      'gemini-2.0-flash-exp': {
        maxTokens: 8192,
        supportsStreaming: true,
        costPer1kTokens: { input: 0.0, output: 0.0 } // Free during preview
      },
      'gemini-2.0-flash-001': {
        maxTokens: 8192,
        supportsStreaming: true,
        costPer1kTokens: { input: 0.075, output: 0.30 }
      },
      'gemini-1.5-pro': {
        maxTokens: 2097152, // 2M tokens
        supportsStreaming: true,
        costPer1kTokens: { input: 0.00125, output: 0.005 }
      },
      'gemini-1.5-pro-001': {
        maxTokens: 2097152, // 2M tokens
        supportsStreaming: true,
        costPer1kTokens: { input: 0.00125, output: 0.005 }
      },
      'gemini-1.5-flash': {
        maxTokens: 1048576, // 1M tokens
        supportsStreaming: true,
        costPer1kTokens: { input: 0.075, output: 0.30 }
      },
      'gemini-1.5-flash-001': {
        maxTokens: 1048576, // 1M tokens
        supportsStreaming: true,
        costPer1kTokens: { input: 0.075, output: 0.30 }
      },
      'gemini-1.0-pro': {
        maxTokens: 32768,
        supportsStreaming: true,
        costPer1kTokens: { input: 0.0005, output: 0.0015 }
      },
      'gemini-pro': {
        maxTokens: 32768,
        supportsStreaming: true,
        costPer1kTokens: { input: 0.0005, output: 0.0015 }
      }
    }

    // Return known info if available
    if (knownModelInfo[model]) {
      return knownModelInfo[model]
    }

    // For unknown models (from custom endpoints), provide reasonable defaults
    if (this.availableModels.includes(model)) {
      return {
        maxTokens: 32768, // Conservative default
        supportsStreaming: true,
        costPer1kTokens: { input: 0.001, output: 0.002 } // Generic pricing
      }
    }

    return null
  }

  private formatMessagesForGemini(messages: AIMessage[]): Array<{ role: string; parts: Array<{ text: string }> }> {
    // Convert to new Gemini Content format
    const result: Array<{ role: string; parts: Array<{ text: string }> }> = []
    let systemInstruction = ''

    for (const message of messages) {
      if (message.role === 'system') {
        // Collect system messages to use as system instruction
        systemInstruction += message.content + '\n'
      } else if (message.role === 'user') {
        const content = systemInstruction ? `${systemInstruction.trim()}\n\n${message.content}` : message.content
        result.push({
          role: 'user',
          parts: [{ text: content }]
        })
        systemInstruction = '' // Clear system message after first use
      } else if (message.role === 'assistant') {
        result.push({
          role: 'model', // Gemini uses 'model' instead of 'assistant'
          parts: [{ text: message.content }]
        })
      }
    }

    return result
  }

  private estimatePromptTokens(messages: AIMessage[]): number {
    return messages.reduce((total, message) => {
      return total + estimateTokenCount(message.content)
    }, 0)
  }

  private mapFinishReason(reason: string | undefined): AIResponse['finishReason'] {
    switch (reason) {
      case 'STOP':
        return 'stop'
      case 'MAX_TOKENS':
        return 'length'
      case 'SAFETY':
        return 'content_filter'
      case 'RECITATION':
        return 'content_filter'
      default:
        return 'stop'
    }
  }

  protected handleError(error: any): never {
    // Handle Gemini-specific errors
    const errorMessage = error.message || error.toString()

    if (errorMessage.includes('API key') || errorMessage.includes('authentication')) {
      throw new AuthenticationError(errorMessage, this.name)
    }

    if (errorMessage.includes('quota') || errorMessage.includes('rate limit') || error.status === 429) {
      throw new RateLimitError(errorMessage, new Date(Date.now() + 60000), 60)
    }

    if (errorMessage.includes('invalid') || error.status === 400) {
      throw new InvalidRequestError(errorMessage, error)
    }

    if (error.status >= 500 || errorMessage.includes('server error')) {
      throw new ServerError(errorMessage, error.status || 500)
    }

    if (errorMessage.includes('network') || errorMessage.includes('connection')) {
      throw new NetworkError(`Gemini connection error: ${errorMessage}`)
    }

    // Call parent error handler for other cases
    super.handleError(error)
  }
}
