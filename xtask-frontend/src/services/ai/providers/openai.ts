import OpenAI from 'openai'
import {
  AIMessage,
  AIGenerationOptions,
  AIResponse,
  AIProviderConfig,
  AuthenticationError,
  RateLimitError,
  InvalidRequestError,
  ServerError,
  NetworkError
} from '@/types/ai'
import { BaseAIProvider, withRetry, formatMessagesForProvider, calculateCost } from '../base'

export class OpenAIProvider extends BaseAIProvider {
  private client?: OpenAI
  private availableModels: string[] = []

  constructor() {
    // Start with empty models array - will be populated dynamically
    super('OpenAI', [])
  }

  async initialize(config: AIProviderConfig): Promise<void> {
    await super.initialize(config)

    this.client = new OpenAI({
      apiKey: config.apiKey,
      baseURL: config.baseURL,
      timeout: config.timeout || 30000,
      maxRetries: 0, // We handle retries ourselves
    })

    // Fetch available models dynamically
    await this.fetchAvailableModels()

    // Update the models array in the parent class
    this.models.length = 0
    this.models.push(...this.availableModels)
  }

  async generateContent(
    messages: AIMessage[],
    options: AIGenerationOptions = {}
  ): Promise<AIResponse> {
    this.ensureInitialized()

    const operation = async (): Promise<AIResponse> => {
      try {
        const formattedMessages = formatMessagesForProvider(messages, this.name)

        const completion = await this.client!.chat.completions.create({
          model: options.model || this.availableModels[0] || 'gpt-4-turbo',
          messages: formattedMessages,
          temperature: options.temperature ?? 0.7,
          max_tokens: options.maxTokens,
          top_p: options.topP,
          frequency_penalty: options.frequencyPenalty,
          presence_penalty: options.presencePenalty,
          stop: options.stop,
          stream: false, // We'll handle streaming separately if needed
        })

        const choice = completion.choices[0]
        if (!choice?.message?.content) {
          throw new InvalidRequestError('No content generated by OpenAI')
        }

        const usage = completion.usage || {
          prompt_tokens: 0,
          completion_tokens: 0,
          total_tokens: 0
        }

        return {
          content: choice.message.content,
          model: completion.model,
          usage: {
            promptTokens: usage.prompt_tokens,
            completionTokens: usage.completion_tokens,
            totalTokens: usage.total_tokens
          },
          finishReason: this.mapFinishReason(choice.finish_reason),
          metadata: {
            id: completion.id,
            created: completion.created,
            systemFingerprint: completion.system_fingerprint
          }
        }
      } catch (error) {
        this.handleError(error)
      }
    }

    return withRetry(operation, this.config?.maxRetries || 3, this.config?.retryDelay || 1000)
  }

  async validateConfig(config: AIProviderConfig): Promise<boolean> {
    try {
      const testClient = new OpenAI({
        apiKey: config.apiKey,
        baseURL: config.baseURL,
        timeout: 10000
      })

      // Try to fetch models first to validate connection
      try {
        await this.fetchModelsFromClient(testClient)
        return true
      } catch (modelError) {
        // If model fetching fails, try a simple generation request
        const defaultModel = this.getDefaultModels()[0]
        await testClient.chat.completions.create({
          model: defaultModel,
          messages: [{ role: 'user', content: 'Hi' }],
          max_tokens: 1
        })
        return true
      }
    } catch (error: any) {
      if (error.status === 401) {
        throw new AuthenticationError('Invalid OpenAI API key', this.name)
      }
      throw error
    }
  }

  private async fetchAvailableModels(): Promise<void> {
    try {
      if (!this.client) {
        throw new Error('Client not initialized')
      }

      // Try to fetch models from OpenAI API
      this.availableModels = await this.fetchModelsFromClient(this.client)
    } catch (error) {
      console.warn('Failed to fetch models from OpenAI API, using fallback list:', error)
      // Fallback to default models if API call fails
      this.availableModels = this.getDefaultModels()
    }
  }

  private async fetchModelsFromClient(client: OpenAI): Promise<string[]> {
    try {
      const response = await client.models.list()

      // Filter for chat completion models and sort by relevance
      const chatModels = response.data
        .filter(model =>
          model.id.includes('gpt') &&
          !model.id.includes('instruct') &&
          !model.id.includes('edit') &&
          !model.id.includes('embedding') &&
          !model.id.includes('whisper') &&
          !model.id.includes('tts') &&
          !model.id.includes('dall-e')
        )
        .map(model => model.id)
        .sort((a, b) => {
          // Prioritize newer models
          if (a.includes('gpt-4') && !b.includes('gpt-4')) return -1
          if (!a.includes('gpt-4') && b.includes('gpt-4')) return 1
          if (a.includes('turbo') && !b.includes('turbo')) return -1
          if (!a.includes('turbo') && b.includes('turbo')) return 1
          return a.localeCompare(b)
        })

      return chatModels.length > 0 ? chatModels : this.getDefaultModels()
    } catch (error) {
      console.warn('Error fetching models from OpenAI API:', error)
      return this.getDefaultModels()
    }
  }

  private getDefaultModels(): string[] {
    return [
      'gpt-4o',
      'gpt-4o-mini',
      'gpt-4-turbo',
      'gpt-4',
      'gpt-3.5-turbo'
    ]
  }

  getModelInfo(model: string) {
    // Known model configurations (fallback for when API doesn't provide details)
    const knownModelInfo: Record<string, any> = {
      'gpt-4o': {
        maxTokens: 128000,
        supportsStreaming: true,
        costPer1kTokens: { input: 0.0025, output: 0.01 }
      },
      'gpt-4o-mini': {
        maxTokens: 128000,
        supportsStreaming: true,
        costPer1kTokens: { input: 0.00015, output: 0.0006 }
      },
      'gpt-4-turbo': {
        maxTokens: 128000,
        supportsStreaming: true,
        costPer1kTokens: { input: 0.01, output: 0.03 }
      },
      'gpt-4': {
        maxTokens: 8192,
        supportsStreaming: true,
        costPer1kTokens: { input: 0.03, output: 0.06 }
      },
      'gpt-3.5-turbo': {
        maxTokens: 16385,
        supportsStreaming: true,
        costPer1kTokens: { input: 0.0005, output: 0.0015 }
      }
    }

    // Return known info if available
    if (knownModelInfo[model]) {
      return knownModelInfo[model]
    }

    // For unknown models (from custom endpoints), provide reasonable defaults
    if (this.availableModels.includes(model)) {
      return {
        maxTokens: 4096, // Conservative default
        supportsStreaming: true,
        costPer1kTokens: { input: 0.001, output: 0.002 } // Generic pricing
      }
    }

    return null
  }

  private mapFinishReason(reason: string | null): AIResponse['finishReason'] {
    switch (reason) {
      case 'stop':
        return 'stop'
      case 'length':
        return 'length'
      case 'content_filter':
        return 'content_filter'
      case 'function_call':
        return 'function_call'
      case 'tool_calls':
        return 'tool_calls'
      default:
        return 'stop'
    }
  }

  protected handleError(error: any): never {
    // Handle OpenAI-specific errors
    if (error instanceof OpenAI.APIError) {
      switch (error.status) {
        case 401:
          throw new AuthenticationError(error.message, this.name)
        case 429:
          const retryAfter = this.parseRetryAfter(error)
          const resetTime = new Date(Date.now() + retryAfter * 1000)
          throw new RateLimitError(error.message, resetTime, retryAfter)
        case 400:
        case 404:
          throw new InvalidRequestError(error.message, error)
        case 500:
        case 502:
        case 503:
        case 504:
          throw new ServerError(error.message, error.status)
        default:
          throw new ServerError(error.message, error.status || 500)
      }
    }

    // Handle connection errors
    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND' || error.code === 'ETIMEDOUT') {
      throw new NetworkError(`OpenAI connection error: ${error.message}`)
    }

    // Call parent error handler for other cases
    super.handleError(error)
  }

  private parseRetryAfter(error: any): number {
    // OpenAI includes retry-after in headers
    const retryAfter = error.headers?.['retry-after']
    if (retryAfter) {
      const seconds = parseInt(retryAfter, 10)
      if (!isNaN(seconds)) {
        return seconds
      }
    }
    return 60 // Default 60 seconds
  }
}
