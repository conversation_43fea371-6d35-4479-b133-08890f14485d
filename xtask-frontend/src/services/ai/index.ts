// AI Services Main Export
export * from './base'
export * from './factory'

// Provider exports
export { OpenAIProvider } from './providers/openai'
export { GeminiProvider } from './providers/gemini'

// Types
export * from '@/types/ai'

// Provider registration
import { AIProviderType, IAIProvider } from '@/types/ai'
import { registerProvider } from './factory'
import { OpenAIProvider } from './providers/openai'
import { GeminiProvider } from './providers/gemini'

// Register all available providers
registerProvider(AIProviderType.OPENAI, async (): Promise<IAIProvider> => {
  return new OpenAIProvider()
})

registerProvider(AIProviderType.GEMINI, async (): Promise<IAIProvider> => {
  return new GeminiProvider()
})

// TODO: Add other providers as they are implemented
// registerProvider(AIProviderType.MISTRAL, async (): Promise<IAIProvider> => {
//   return new MistralProvider()
// })

// registerProvider(AIProviderType.GROQ, async (): Promise<IAIProvider> => {
//   return new GroqProvider()
// })

// registerProvider(AIProviderType.HUGGINGFACE, async (): Promise<IAIProvider> => {
//   return new HuggingFaceProvider()
// })

// registerProvider(AIProviderType.OPENROUTER, async (): Promise<IAIProvider> => {
//   return new OpenRouterProvider()
// })
