import { 
  IAIProvider, 
  AIProviderType, 
  ProviderFactoryConfig,
  AIProviderConfig,
  AIProviderError,
  ProviderStatus
} from '@/types/ai'

// Provider registry
const providerRegistry = new Map<AIProviderType, () => Promise<IAIProvider>>()

// Register a provider
export function registerProvider(
  type: AIProviderType, 
  factory: () => Promise<IAIProvider>
): void {
  providerRegistry.set(type, factory)
}

// Provider factory class
export class AIProviderFactory {
  private providers = new Map<AIProviderType, IAIProvider>()
  private config: ProviderFactoryConfig
  private initialized = false

  constructor(config: ProviderFactoryConfig) {
    this.config = config
  }

  async initialize(): Promise<void> {
    if (this.initialized) return

    // Initialize all configured providers
    for (const [providerType, providerConfig] of Object.entries(this.config.providerConfigs)) {
      try {
        const provider = await this.createProvider(providerType as AIProviderType)
        await provider.initialize(providerConfig)
        this.providers.set(providerType as AIProviderType, provider)
      } catch (error) {
        console.warn(`Failed to initialize provider ${providerType}:`, error)
        // Continue with other providers
      }
    }

    this.initialized = true
  }

  async getProvider(type?: AIProviderType): Promise<IAIProvider> {
    if (!this.initialized) {
      await this.initialize()
    }

    const providerType = type || this.config.defaultProvider
    const provider = this.providers.get(providerType)

    if (!provider) {
      throw new AIProviderError(
        `Provider ${providerType} is not available`,
        'PROVIDER_NOT_AVAILABLE',
        'invalid_request',
        undefined,
        { requestedProvider: providerType },
        false
      )
    }

    if (!provider.isAvailable()) {
      // Try fallback providers
      for (const fallbackType of this.config.fallbackProviders) {
        const fallbackProvider = this.providers.get(fallbackType)
        if (fallbackProvider?.isAvailable()) {
          console.warn(`Using fallback provider ${fallbackType} instead of ${providerType}`)
          return fallbackProvider
        }
      }

      throw new AIProviderError(
        `Provider ${providerType} is not available and no fallback providers are configured`,
        'NO_PROVIDERS_AVAILABLE',
        'server_error',
        undefined,
        { requestedProvider: providerType },
        true
      )
    }

    return provider
  }

  async getAvailableProviders(): Promise<AIProviderType[]> {
    if (!this.initialized) {
      await this.initialize()
    }

    return Array.from(this.providers.entries())
      .filter(([_, provider]) => provider.isAvailable())
      .map(([type, _]) => type)
  }

  async getProviderStatus(): Promise<ProviderStatus[]> {
    if (!this.initialized) {
      await this.initialize()
    }

    const statuses: ProviderStatus[] = []

    for (const [type, provider] of this.providers.entries()) {
      statuses.push({
        name: provider.name,
        available: provider.isAvailable(),
        configured: !!this.config.providerConfigs[type]?.apiKey,
        // TODO: Add rate limit info and response time tracking
      })
    }

    return statuses
  }

  private async createProvider(type: AIProviderType): Promise<IAIProvider> {
    const factory = providerRegistry.get(type)
    
    if (!factory) {
      throw new AIProviderError(
        `Provider ${type} is not registered`,
        'PROVIDER_NOT_REGISTERED',
        'invalid_request',
        undefined,
        { providerType: type },
        false
      )
    }

    return await factory()
  }

  // Update configuration at runtime
  async updateConfig(newConfig: Partial<ProviderFactoryConfig>): Promise<void> {
    this.config = { ...this.config, ...newConfig }
    
    // Re-initialize providers if needed
    if (newConfig.providerConfigs) {
      for (const [providerType, providerConfig] of Object.entries(newConfig.providerConfigs)) {
        try {
          const provider = await this.createProvider(providerType as AIProviderType)
          await provider.initialize(providerConfig)
          this.providers.set(providerType as AIProviderType, provider)
        } catch (error) {
          console.warn(`Failed to update provider ${providerType}:`, error)
        }
      }
    }
  }
}

// Global factory instance
let globalFactory: AIProviderFactory | null = null

export function createProviderFactory(config: ProviderFactoryConfig): AIProviderFactory {
  globalFactory = new AIProviderFactory(config)
  return globalFactory
}

export function getProviderFactory(): AIProviderFactory {
  if (!globalFactory) {
    throw new AIProviderError(
      'Provider factory not initialized. Call createProviderFactory first.',
      'FACTORY_NOT_INITIALIZED',
      'invalid_request',
      undefined,
      undefined,
      false
    )
  }
  return globalFactory
}

// Helper function to create factory from environment variables
export function createFactoryFromEnv(): AIProviderFactory {
  const config: ProviderFactoryConfig = {
    defaultProvider: (process.env.DEFAULT_AI_PROVIDER as AIProviderType) || AIProviderType.OPENAI,
    fallbackProviders: [AIProviderType.GEMINI, AIProviderType.GROQ],
    providerConfigs: {}
  }

  // Add provider configs based on available environment variables
  if (process.env.OPENAI_API_KEY) {
    config.providerConfigs[AIProviderType.OPENAI] = {
      apiKey: process.env.OPENAI_API_KEY,
      timeout: 30000,
      maxRetries: 3
    }
  }

  if (process.env.GEMINI_API_KEY) {
    config.providerConfigs[AIProviderType.GEMINI] = {
      apiKey: process.env.GEMINI_API_KEY,
      timeout: 30000,
      maxRetries: 3
    }
  }

  if (process.env.MISTRAL_API_KEY) {
    config.providerConfigs[AIProviderType.MISTRAL] = {
      apiKey: process.env.MISTRAL_API_KEY,
      timeout: 30000,
      maxRetries: 3
    }
  }

  if (process.env.GROQ_API_KEY) {
    config.providerConfigs[AIProviderType.GROQ] = {
      apiKey: process.env.GROQ_API_KEY,
      timeout: 30000,
      maxRetries: 3
    }
  }

  if (process.env.HUGGINGFACE_API_KEY) {
    config.providerConfigs[AIProviderType.HUGGINGFACE] = {
      apiKey: process.env.HUGGINGFACE_API_KEY,
      timeout: 60000, // HuggingFace can be slower
      maxRetries: 2
    }
  }

  if (process.env.OPENROUTER_API_KEY) {
    config.providerConfigs[AIProviderType.OPENROUTER] = {
      apiKey: process.env.OPENROUTER_API_KEY,
      timeout: 30000,
      maxRetries: 3
    }
  }

  return createProviderFactory(config)
}
