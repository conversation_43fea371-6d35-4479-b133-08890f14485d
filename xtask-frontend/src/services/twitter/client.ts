import { <PERSON><PERSON><PERSON>, ApiR<PERSON>ponseError, ApiRequestError } from 'twitter-api-v2'
import { 
  Twitter<PERSON>red<PERSON>s, 
  TwitterUser, 
  TwitterTweet, 
  TweetPostOptions,
  TweetThreadOptions,
  MediaUploadOptions,
  TwitterApiError,
  TwitterRateLimitError,
  TwitterAuthError,
  TwitterRateLimit
} from '@/types/twitter'

export class TwitterService {
  private client: TwitterApi
  private userClient?: TwitterApi

  constructor(credentials: TwitterCredentials) {
    // Create app-only client for public data
    this.client = new TwitterApi(credentials.bearerToken || '')

    // Create user context client for posting/user actions
    if (credentials.accessToken && credentials.accessSecret) {
      this.userClient = new TwitterApi({
        appKey: credentials.appKey,
        appSecret: credentials.appSecret,
        accessToken: credentials.accessToken,
        accessSecret: credentials.accessSecret,
      })
    }
  }

  // Create user-specific client from stored tokens
  static createUserClient(
    appKey: string,
    appSecret: string,
    accessToken: string,
    accessSecret: string
  ): TwitterService {
    const credentials: TwitterCredentials = {
      appKey,
      appSecret,
      accessToken,
      accessSecret
    }
    return new TwitterService(credentials)
  }

  // Verify credentials and get user info
  async verifyCredentials(): Promise<TwitterUser> {
    try {
      if (!this.userClient) {
        throw new TwitterAuthError('User client not initialized')
      }

      const user = await this.userClient.v2.me({
        'user.fields': [
          'id', 'username', 'name', 'profile_image_url', 'public_metrics',
          'verified', 'description', 'location', 'url', 'created_at'
        ]
      })

      return user.data as TwitterUser
    } catch (error) {
      this.handleError(error)
    }
  }

  // Post a single tweet
  async postTweet(options: TweetPostOptions): Promise<TwitterTweet> {
    try {
      if (!this.userClient) {
        throw new TwitterAuthError('User client not initialized')
      }

      const tweetData: any = {
        text: options.text
      }

      if (options.reply) {
        tweetData.reply = options.reply
      }

      if (options.quote_tweet_id) {
        tweetData.quote_tweet_id = options.quote_tweet_id
      }

      if (options.media?.media_ids?.length) {
        tweetData.media = {
          media_ids: options.media.media_ids
        }
        if (options.media.tagged_user_ids?.length) {
          tweetData.media.tagged_user_ids = options.media.tagged_user_ids
        }
      }

      if (options.poll) {
        tweetData.poll = options.poll
      }

      if (options.geo) {
        tweetData.geo = options.geo
      }

      if (options.reply_settings) {
        tweetData.reply_settings = options.reply_settings
      }

      const response = await this.userClient.v2.tweet(tweetData)
      return response.data as TwitterTweet
    } catch (error) {
      this.handleError(error)
    }
  }

  // Post a thread of tweets
  async postThread(options: TweetThreadOptions): Promise<TwitterTweet[]> {
    try {
      if (!this.userClient) {
        throw new TwitterAuthError('User client not initialized')
      }

      const tweets: TwitterTweet[] = []
      let previousTweetId: string | undefined

      for (const [index, tweetData] of options.tweets.entries()) {
        const tweetOptions: TweetPostOptions = {
          text: tweetData.text,
          reply_settings: options.reply_settings
        }

        // Add media if provided
        if (tweetData.media_ids?.length) {
          tweetOptions.media = {
            media_ids: tweetData.media_ids
          }
        }

        // Add reply to previous tweet (except for first tweet)
        if (previousTweetId) {
          tweetOptions.reply = {
            in_reply_to_tweet_id: previousTweetId
          }
        }

        const tweet = await this.postTweet(tweetOptions)
        tweets.push(tweet)
        previousTweetId = tweet.id

        // Add delay between tweets to avoid rate limits
        if (index < options.tweets.length - 1) {
          await this.delay(1000) // 1 second delay
        }
      }

      return tweets
    } catch (error) {
      this.handleError(error)
    }
  }

  // Delete a tweet
  async deleteTweet(tweetId: string): Promise<boolean> {
    try {
      if (!this.userClient) {
        throw new TwitterAuthError('User client not initialized')
      }

      const response = await this.userClient.v2.deleteTweet(tweetId)
      return response.data?.deleted || false
    } catch (error) {
      this.handleError(error)
    }
  }

  // Upload media
  async uploadMedia(
    media: Buffer | string,
    options: MediaUploadOptions = {}
  ): Promise<string> {
    try {
      if (!this.userClient) {
        throw new TwitterAuthError('User client not initialized')
      }

      const mediaId = await this.userClient.v1.uploadMedia(media, {
        mimeType: this.getMimeType(media),
        target: 'tweet',
        ...options
      })

      // Add alt text if provided
      if (options.alt_text) {
        await this.userClient.v1.createMediaMetadata(mediaId, {
          alt_text: { text: options.alt_text }
        })
      }

      return mediaId
    } catch (error) {
      this.handleError(error)
    }
  }

  // Get tweet by ID
  async getTweet(tweetId: string): Promise<TwitterTweet | null> {
    try {
      const response = await this.client.v2.singleTweet(tweetId, {
        'tweet.fields': [
          'id', 'text', 'author_id', 'created_at', 'public_metrics',
          'referenced_tweets', 'attachments', 'context_annotations',
          'conversation_id', 'in_reply_to_user_id', 'lang',
          'possibly_sensitive', 'reply_settings', 'source'
        ],
        expansions: ['author_id', 'attachments.media_keys', 'referenced_tweets.id'],
        'media.fields': ['media_key', 'type', 'url', 'preview_image_url', 'duration_ms', 'height', 'width', 'public_metrics', 'alt_text']
      })

      return response.data as TwitterTweet
    } catch (error) {
      if (error instanceof ApiResponseError && error.code === 404) {
        return null
      }
      this.handleError(error)
    }
  }

  // Get user timeline
  async getUserTimeline(
    userId: string,
    options: {
      max_results?: number
      pagination_token?: string
      since_id?: string
      until_id?: string
    } = {}
  ): Promise<{ tweets: TwitterTweet[]; next_token?: string }> {
    try {
      const response = await this.client.v2.userTimeline(userId, {
        max_results: options.max_results || 10,
        pagination_token: options.pagination_token,
        since_id: options.since_id,
        until_id: options.until_id,
        'tweet.fields': [
          'id', 'text', 'author_id', 'created_at', 'public_metrics',
          'referenced_tweets', 'attachments', 'context_annotations'
        ],
        expansions: ['attachments.media_keys', 'referenced_tweets.id'],
        'media.fields': ['media_key', 'type', 'url', 'preview_image_url', 'public_metrics']
      })

      return {
        tweets: response.data?.data || [],
        next_token: response.data?.meta?.next_token
      }
    } catch (error) {
      this.handleError(error)
    }
  }

  // Get rate limit info
  async getRateLimit(endpoint: string): Promise<TwitterRateLimit | null> {
    try {
      // This would require the rate limit plugin
      // For now, return null and handle in calling code
      return null
    } catch (error) {
      return null
    }
  }

  // Helper methods
  private getMimeType(media: Buffer | string): string {
    if (typeof media === 'string') {
      const ext = media.split('.').pop()?.toLowerCase()
      switch (ext) {
        case 'jpg':
        case 'jpeg':
          return 'image/jpeg'
        case 'png':
          return 'image/png'
        case 'gif':
          return 'image/gif'
        case 'mp4':
          return 'video/mp4'
        case 'mov':
          return 'video/quicktime'
        default:
          return 'application/octet-stream'
      }
    }
    return 'application/octet-stream'
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  private handleError(error: any): never {
    if (error instanceof ApiResponseError) {
      if (error.rateLimitError && error.rateLimit) {
        throw new TwitterRateLimitError(
          error.message,
          error.rateLimit.reset,
          error.rateLimit.limit,
          error.rateLimit.remaining
        )
      }

      if (error.code === 401) {
        throw new TwitterAuthError(error.message)
      }

      throw new TwitterApiError(
        error.message,
        error.code || 500,
        error.type || 'api_error',
        error.data
      )
    }

    if (error instanceof ApiRequestError) {
      throw new TwitterApiError(
        `Request failed: ${error.message}`,
        500,
        'request_error',
        error.requestError
      )
    }

    if (error instanceof TwitterApiError) {
      throw error
    }

    throw new TwitterApiError(
      error.message || 'Unknown Twitter API error',
      500,
      'unknown_error',
      error
    )
  }
}
