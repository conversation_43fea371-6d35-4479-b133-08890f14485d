import { TwitterService } from './client'
import { TwitterCred<PERSON>s, TwitterAuthError } from '@/types/twitter'
import { prisma } from '@/lib/db'

export class TwitterServiceFactory {
  private static appCredentials: {
    appKey: string
    appSecret: string
    bearerToken: string
  } | null = null

  // Initialize app credentials from environment
  static initialize() {
    const appKey = process.env.TWITTER_CLIENT_ID
    const appSecret = process.env.TWITTER_CLIENT_SECRET
    const bearerToken = process.env.TWITTER_BEARER_TOKEN

    if (!appKey || !appSecret) {
      throw new Error('Twitter app credentials not configured')
    }

    this.appCredentials = {
      appKey,
      appSecret,
      bearerToken: bearerToken || ''
    }
  }

  // Create app-only client for public data access
  static createAppClient(): TwitterService {
    if (!this.appCredentials) {
      this.initialize()
    }

    if (!this.appCredentials) {
      throw new TwitterAuthError('Twitter app credentials not available')
    }

    const credentials: TwitterCredentials = {
      appKey: this.appCredentials.appKey,
      appSecret: this.appCredentials.appSecret,
      accessToken: '',
      accessSecret: '',
      bearerToken: this.appCredentials.bearerToken
    }

    return new TwitterService(credentials)
  }

  // Create user client from database stored tokens
  static async createUserClient(userId: string): Promise<TwitterService> {
    if (!this.appCredentials) {
      this.initialize()
    }

    if (!this.appCredentials) {
      throw new TwitterAuthError('Twitter app credentials not available')
    }

    // Get user's Twitter account from database
    const twitterAccount = await prisma.twitterAccount.findFirst({
      where: { userId }
    })

    if (!twitterAccount || !twitterAccount.accessToken || !twitterAccount.accessSecret) {
      throw new TwitterAuthError('User Twitter account not connected')
    }

    const credentials: TwitterCredentials = {
      appKey: this.appCredentials.appKey,
      appSecret: this.appCredentials.appSecret,
      accessToken: twitterAccount.accessToken,
      accessSecret: twitterAccount.accessSecret,
      bearerToken: this.appCredentials.bearerToken
    }

    return new TwitterService(credentials)
  }

  // Create user client from agent's Twitter connection
  static async createAgentClient(agentId: string): Promise<TwitterService> {
    if (!this.appCredentials) {
      this.initialize()
    }

    if (!this.appCredentials) {
      throw new TwitterAuthError('Twitter app credentials not available')
    }

    // Get agent and its user's Twitter account
    const agent = await prisma.agent.findUnique({
      where: { id: agentId },
      include: {
        user: {
          include: {
            twitterAccounts: true
          }
        }
      }
    })

    if (!agent) {
      throw new Error('Agent not found')
    }

    const twitterAccount = agent.user.twitterAccounts[0]
    if (!twitterAccount || !twitterAccount.accessToken || !twitterAccount.accessSecret) {
      throw new TwitterAuthError('Agent user Twitter account not connected')
    }

    const credentials: TwitterCredentials = {
      appKey: this.appCredentials.appKey,
      appSecret: this.appCredentials.appSecret,
      accessToken: twitterAccount.accessToken,
      accessSecret: twitterAccount.accessSecret,
      bearerToken: this.appCredentials.bearerToken
    }

    return new TwitterService(credentials)
  }

  // Create client from explicit credentials (for OAuth flow)
  static createClientFromCredentials(credentials: TwitterCredentials): TwitterService {
    return new TwitterService(credentials)
  }

  // Validate Twitter connection for a user
  static async validateUserConnection(userId: string): Promise<boolean> {
    try {
      const client = await this.createUserClient(userId)
      await client.verifyCredentials()
      return true
    } catch (error) {
      return false
    }
  }

  // Get app credentials for OAuth flow
  static getAppCredentials() {
    if (!this.appCredentials) {
      this.initialize()
    }
    return this.appCredentials
  }
}

// Rate limiting helper
export class TwitterRateLimitManager {
  private static limits = new Map<string, {
    remaining: number
    reset: number
    limit: number
  }>()

  static updateLimit(endpoint: string, limit: number, remaining: number, reset: number) {
    this.limits.set(endpoint, { limit, remaining, reset })
  }

  static canMakeRequest(endpoint: string): boolean {
    const limitInfo = this.limits.get(endpoint)
    if (!limitInfo) return true

    const now = Math.floor(Date.now() / 1000)
    if (now >= limitInfo.reset) {
      // Reset time has passed, remove old limit info
      this.limits.delete(endpoint)
      return true
    }

    return limitInfo.remaining > 0
  }

  static getWaitTime(endpoint: string): number {
    const limitInfo = this.limits.get(endpoint)
    if (!limitInfo) return 0

    const now = Math.floor(Date.now() / 1000)
    if (now >= limitInfo.reset) return 0

    return (limitInfo.reset - now) * 1000 // Convert to milliseconds
  }

  static getRemainingRequests(endpoint: string): number {
    const limitInfo = this.limits.get(endpoint)
    if (!limitInfo) return Infinity

    const now = Math.floor(Date.now() / 1000)
    if (now >= limitInfo.reset) return Infinity

    return limitInfo.remaining
  }
}

// Retry helper with exponential backoff
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error as Error

      // Don't retry on authentication errors
      if (error instanceof TwitterAuthError) {
        throw error
      }

      // Don't retry on the last attempt
      if (attempt === maxRetries) {
        break
      }

      // Calculate delay with exponential backoff
      const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 1000

      // For rate limit errors, wait for the reset time
      if (error instanceof Error && error.name === 'TwitterRateLimitError') {
        const rateLimitError = error as any
        const resetDelay = (rateLimitError.resetTime * 1000) - Date.now()
        await new Promise(resolve => setTimeout(resolve, Math.max(delay, resetDelay)))
      } else {
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }
  }

  throw lastError!
}

// Initialize factory on module load
TwitterServiceFactory.initialize()
