// Twitter API Types and Interfaces

export interface TwitterCredentials {
  appKey: string
  appSecret: string
  accessToken: string
  accessSecret: string
  bearerToken?: string
}

export interface TwitterUser {
  id: string
  username: string
  name: string
  profile_image_url?: string
  public_metrics?: {
    followers_count: number
    following_count: number
    tweet_count: number
    listed_count: number
  }
  verified?: boolean
  description?: string
  location?: string
  url?: string
  created_at?: string
}

export interface TwitterTweet {
  id: string
  text: string
  author_id?: string
  created_at?: string
  public_metrics?: {
    retweet_count: number
    like_count: number
    reply_count: number
    quote_count: number
    bookmark_count?: number
    impression_count?: number
  }
  referenced_tweets?: Array<{
    type: 'retweeted' | 'quoted' | 'replied_to'
    id: string
  }>
  attachments?: {
    media_keys?: string[]
    poll_ids?: string[]
  }
  context_annotations?: Array<{
    domain: {
      id: string
      name: string
      description?: string
    }
    entity: {
      id: string
      name: string
      description?: string
    }
  }>
  conversation_id?: string
  in_reply_to_user_id?: string
  lang?: string
  possibly_sensitive?: boolean
  reply_settings?: 'everyone' | 'mentionedUsers' | 'following'
  source?: string
}

export interface TwitterMedia {
  media_key: string
  type: 'photo' | 'video' | 'animated_gif'
  url?: string
  preview_image_url?: string
  duration_ms?: number
  height?: number
  width?: number
  public_metrics?: {
    view_count?: number
  }
  alt_text?: string
}

export interface TweetPostOptions {
  text: string
  reply?: {
    in_reply_to_tweet_id: string
    exclude_reply_user_ids?: string[]
  }
  quote_tweet_id?: string
  media?: {
    media_ids?: string[]
    tagged_user_ids?: string[]
  }
  poll?: {
    options: string[]
    duration_minutes: number
  }
  geo?: {
    place_id: string
  }
  reply_settings?: 'everyone' | 'mentionedUsers' | 'following'
  direct_message_deep_link?: string
  for_super_followers_only?: boolean
}

export interface TweetThreadOptions {
  tweets: Array<{
    text: string
    media_ids?: string[]
  }>
  reply_settings?: 'everyone' | 'mentionedUsers' | 'following'
}

export interface MediaUploadOptions {
  media_category?: 'tweet_image' | 'tweet_video' | 'tweet_gif'
  additional_owners?: string[]
  alt_text?: string
  subtitle?: {
    media_id: string
    language_code: string
    display_name: string
  }
}

export interface TwitterAnalytics {
  tweet_id: string
  impressions: number
  engagements: number
  engagement_rate: number
  retweets: number
  likes: number
  replies: number
  quotes: number
  bookmarks?: number
  profile_clicks?: number
  url_clicks?: number
  hashtag_clicks?: number
  detail_expands?: number
  permalink_clicks?: number
  app_opens?: number
  app_installs?: number
  follows?: number
  email_tweet?: number
  dial_phone?: number
  media_views?: number
  media_engagements?: number
  promoted_impressions?: number
  promoted_engagements?: number
  promoted_engagement_rate?: number
  promoted_retweets?: number
  promoted_likes?: number
  promoted_replies?: number
  promoted_follows?: number
  promoted_url_clicks?: number
  promoted_hashtag_clicks?: number
  promoted_detail_expands?: number
  promoted_permalink_clicks?: number
  promoted_app_opens?: number
  promoted_app_installs?: number
  promoted_email_tweet?: number
  promoted_dial_phone?: number
  promoted_media_views?: number
  promoted_media_engagements?: number
}

export interface TwitterRateLimit {
  limit: number
  remaining: number
  reset: number
}

export interface TwitterError {
  code: number
  message: string
  type?: string
  detail?: string
  title?: string
  resource_type?: string
  parameter?: string
  resource_id?: string
  value?: string
}

export interface TwitterApiResponse<T> {
  data?: T
  includes?: {
    tweets?: TwitterTweet[]
    users?: TwitterUser[]
    media?: TwitterMedia[]
    polls?: any[]
    places?: any[]
  }
  errors?: TwitterError[]
  meta?: {
    result_count?: number
    next_token?: string
    previous_token?: string
    newest_id?: string
    oldest_id?: string
  }
}

// Scheduled Tweet Types
export interface ScheduledTweetData {
  id: string
  agentId: string
  content: string
  mediaIds?: string[]
  scheduledFor: Date
  status: 'pending' | 'posted' | 'failed' | 'cancelled'
  tweetId?: string
  error?: string
  createdAt: Date
  updatedAt: Date
}

// Agent Twitter Integration
export interface AgentTwitterSettings {
  isConnected: boolean
  twitterUserId?: string
  twitterUsername?: string
  accessToken?: string
  accessSecret?: string
  lastSyncAt?: Date
  postingEnabled: boolean
  autoReply: boolean
  mentionMonitoring: boolean
  hashtagTracking: string[]
  postingSchedule?: {
    frequency: 'hourly' | 'daily' | 'weekly'
    times: string[] // HH:MM format
    timezone: string
  }
}

// Error Classes
export class TwitterApiError extends Error {
  constructor(
    message: string,
    public code: number,
    public type: string,
    public details?: any
  ) {
    super(message)
    this.name = 'TwitterApiError'
  }
}

export class TwitterRateLimitError extends TwitterApiError {
  constructor(
    message: string,
    public resetTime: number,
    public limit: number,
    public remaining: number
  ) {
    super(message, 429, 'rate_limit_exceeded')
  }
}

export class TwitterAuthError extends TwitterApiError {
  constructor(message: string) {
    super(message, 401, 'authentication_failed')
  }
}
