// AI Provider Types and Interfaces

export interface AIMessage {
  role: 'system' | 'user' | 'assistant'
  content: string
  name?: string
}

export interface AIGenerationOptions {
  model?: string
  temperature?: number
  maxTokens?: number
  topP?: number
  frequencyPenalty?: number
  presencePenalty?: number
  stop?: string[]
  stream?: boolean
}

export interface AIResponse {
  content: string
  model: string
  usage: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
  finishReason: 'stop' | 'length' | 'content_filter' | 'function_call' | 'tool_calls'
  metadata?: Record<string, any>
}

export interface AIProviderError {
  code: string
  message: string
  type: 'authentication' | 'rate_limit' | 'invalid_request' | 'server_error' | 'network_error'
  status?: number
  details?: any
  retryable: boolean
}

export interface AIProviderConfig {
  apiKey: string
  baseURL?: string
  timeout?: number
  maxRetries?: number
  retryDelay?: number
}

// Main AI Provider Interface
export interface IAIProvider {
  readonly name: string
  readonly models: string[]
  
  // Initialize the provider with configuration
  initialize(config: AIProviderConfig): Promise<void>
  
  // Generate content using chat completion
  generateContent(
    messages: AIMessage[],
    options?: AIGenerationOptions
  ): Promise<AIResponse>
  
  // Check if the provider is available and configured
  isAvailable(): boolean
  
  // Get provider-specific model information
  getModelInfo(model: string): {
    maxTokens: number
    supportsStreaming: boolean
    costPer1kTokens: {
      input: number
      output: number
    }
  } | null
  
  // Validate configuration
  validateConfig(config: AIProviderConfig): Promise<boolean>
}

// Provider Types
export enum AIProviderType {
  OPENAI = 'openai',
  GEMINI = 'gemini',
  MISTRAL = 'mistral',
  GROQ = 'groq',
  HUGGINGFACE = 'huggingface',
  OPENROUTER = 'openrouter'
}

// Provider Factory Configuration
export interface ProviderFactoryConfig {
  defaultProvider: AIProviderType
  fallbackProviders: AIProviderType[]
  providerConfigs: Record<AIProviderType, AIProviderConfig>
}

// Agent AI Preferences (from agent model)
export interface AgentAIPreferences {
  preferredProvider?: AIProviderType
  fallbackProviders?: AIProviderType[]
  model?: string
  temperature?: number
  maxTokens?: number
  creativity?: number // 0-1, maps to temperature
}

// Content Generation Request
export interface ContentGenerationRequest {
  agentId: string
  prompt: string
  context?: string
  type: 'tweet' | 'thread' | 'reply' | 'custom'
  preferences?: AgentAIPreferences
  metadata?: Record<string, any>
}

// Content Generation Response
export interface ContentGenerationResponse {
  content: string
  provider: AIProviderType
  model: string
  usage: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
    estimatedCost: number
  }
  metadata: {
    agentId: string
    requestId: string
    timestamp: string
    processingTime: number
  }
}

// Rate Limiting
export interface RateLimitInfo {
  requestsPerMinute: number
  tokensPerMinute: number
  requestsRemaining: number
  tokensRemaining: number
  resetTime: Date
}

// Provider Status
export interface ProviderStatus {
  name: string
  available: boolean
  configured: boolean
  rateLimitInfo?: RateLimitInfo
  lastError?: AIProviderError
  responseTime?: number
}

// Caching
export interface CacheKey {
  provider: AIProviderType
  model: string
  messages: AIMessage[]
  options: AIGenerationOptions
}

export interface CachedResponse {
  response: AIResponse
  timestamp: number
  expiresAt: number
}

// Error Classes
export class AIProviderError extends Error {
  constructor(
    message: string,
    public code: string,
    public type: AIProviderError['type'],
    public status?: number,
    public details?: any,
    public retryable: boolean = false
  ) {
    super(message)
    this.name = 'AIProviderError'
  }
}

export class RateLimitError extends AIProviderError {
  constructor(
    message: string,
    public resetTime: Date,
    public retryAfter: number
  ) {
    super(message, 'RATE_LIMIT_EXCEEDED', 'rate_limit', 429, { resetTime, retryAfter }, true)
  }
}

export class AuthenticationError extends AIProviderError {
  constructor(message: string, provider: string) {
    super(message, 'AUTHENTICATION_FAILED', 'authentication', 401, { provider }, false)
  }
}

export class InvalidRequestError extends AIProviderError {
  constructor(message: string, details?: any) {
    super(message, 'INVALID_REQUEST', 'invalid_request', 400, details, false)
  }
}

export class ServerError extends AIProviderError {
  constructor(message: string, status: number = 500) {
    super(message, 'SERVER_ERROR', 'server_error', status, undefined, true)
  }
}

export class NetworkError extends AIProviderError {
  constructor(message: string) {
    super(message, 'NETWORK_ERROR', 'network_error', undefined, undefined, true)
  }
}
