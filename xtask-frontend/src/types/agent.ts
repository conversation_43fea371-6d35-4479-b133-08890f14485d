import { z } from "zod"

// Base Agent type from Prisma schema
export interface Agent {
  id: string
  name: string
  description?: string | null
  avatar?: string | null
  isActive: boolean
  personality?: Record<string, any> | null
  writingStyle?: Record<string, any> | null
  topics?: string[] | null
  tone?: string | null
  creativity?: number | null
  preferredModel?: string | null
  temperature?: number | null
  maxTokens?: number | null
  postingFrequency?: string | null
  preferredTimes?: string[] | null
  timezone?: string | null
  totalTweets: number
  totalEngagement: number
  avgEngagement: number
  userId: string
  createdAt: Date
  updatedAt: Date
}

// Agent list item (subset for list views)
export interface AgentListItem {
  id: string
  name: string
  description?: string | null
  avatar?: string | null
  isActive: boolean
  totalTweets: number
  totalEngagement: number
  avgEngagement: number
  createdAt: Date
  updatedAt: Date
}

// Zod schemas for validation
export const createAgentSchema = z.object({
  name: z.string().min(1, "Name is required").max(100, "Name must be less than 100 characters"),
  description: z.string().optional(),
  avatar: z.string().url().optional(),
  personality: z.record(z.any()).optional(),
  writingStyle: z.record(z.any()).optional(),
  topics: z.array(z.string()).optional(),
  tone: z.string().optional(),
  creativity: z.number().min(0).max(1).optional(),
  preferredModel: z.string().optional(),
  temperature: z.number().min(0).max(1).optional(),
  maxTokens: z.number().int().positive().optional(),
  postingFrequency: z.string().optional(),
  preferredTimes: z.array(z.string()).optional(),
  timezone: z.string().optional(),
})

export const updateAgentSchema = z.object({
  name: z.string().min(1, "Name is required").max(100, "Name must be less than 100 characters").optional(),
  description: z.string().optional(),
  avatar: z.string().url().optional(),
  isActive: z.boolean().optional(),
  personality: z.record(z.any()).optional(),
  writingStyle: z.record(z.any()).optional(),
  topics: z.array(z.string()).optional(),
  tone: z.string().optional(),
  creativity: z.number().min(0).max(1).optional(),
  preferredModel: z.string().optional(),
  temperature: z.number().min(0).max(1).optional(),
  maxTokens: z.number().int().positive().optional(),
  postingFrequency: z.string().optional(),
  preferredTimes: z.array(z.string()).optional(),
  timezone: z.string().optional(),
})

// Type inference from Zod schemas
export type CreateAgentData = z.infer<typeof createAgentSchema>
export type UpdateAgentData = z.infer<typeof updateAgentSchema>

// API Response types
export interface ApiResponse<T> {
  data?: T
  message?: string
  errors?: any
}

export interface AgentApiError {
  message: string
  errors?: any
  status: number
}

// Form types for React Hook Form
export interface AgentFormData extends CreateAgentData {
  // Additional form-specific fields can be added here
}

// Agent status options
export const AGENT_STATUSES = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
} as const

export type AgentStatus = typeof AGENT_STATUSES[keyof typeof AGENT_STATUSES]

// AI Model options (can be expanded based on available models)
export const AI_MODELS = {
  GPT_4: 'gpt-4',
  GPT_3_5_TURBO: 'gpt-3.5-turbo',
  GEMINI_PRO: 'gemini-pro',
  CLAUDE_3_SONNET: 'claude-3-sonnet',
} as const

export type AIModel = typeof AI_MODELS[keyof typeof AI_MODELS]

// Tone options for agents
export const AGENT_TONES = {
  PROFESSIONAL: 'professional',
  CASUAL: 'casual',
  FRIENDLY: 'friendly',
  AUTHORITATIVE: 'authoritative',
  HUMOROUS: 'humorous',
  INSPIRATIONAL: 'inspirational',
} as const

export type AgentTone = typeof AGENT_TONES[keyof typeof AGENT_TONES]

// Posting frequency options
export const POSTING_FREQUENCIES = {
  HOURLY: 'hourly',
  DAILY: 'daily',
  WEEKLY: 'weekly',
  CUSTOM: 'custom',
} as const

export type PostingFrequency = typeof POSTING_FREQUENCIES[keyof typeof POSTING_FREQUENCIES]
