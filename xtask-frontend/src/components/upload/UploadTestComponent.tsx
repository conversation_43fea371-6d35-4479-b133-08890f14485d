"use client";

import { useState } from "react";
import { UploadButton, UploadDropzone } from "@/utils/uploadthing";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Trash2, Eye, Download } from "lucide-react";

interface UploadedFile {
  fileId: string;
  uploadedBy: string;
  url: string;
  name: string;
  size: number;
  type: string;
}

export function UploadTestComponent() {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const handleUploadComplete = (res: any[]) => {
    console.log("Upload completed:", res);
    
    // Add uploaded files to state
    const newFiles = res.map((file) => ({
      fileId: file.serverData?.fileId || "unknown",
      uploadedBy: file.serverData?.uploadedBy || "unknown",
      url: file.url,
      name: file.name,
      size: file.size,
      type: file.type,
    }));
    
    setUploadedFiles((prev) => [...prev, ...newFiles]);
  };

  const handleUploadError = (error: Error) => {
    console.error("Upload error:", error);
    alert(`Upload failed: ${error.message}`);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const getFileType = (mimeType: string) => {
    if (mimeType.startsWith("image/")) return "Image";
    if (mimeType.startsWith("video/")) return "Video";
    return "File";
  };

  const removeFile = (index: number) => {
    setUploadedFiles((prev) => prev.filter((_, i) => i !== index));
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>UploadThing Integration Test</CardTitle>
          <CardDescription>
            Test the UploadThing integration with different upload methods
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Upload Button */}
          <div>
            <h3 className="text-lg font-semibold mb-2">Upload Button</h3>
            <UploadButton
              endpoint="imageUploader"
              onClientUploadComplete={handleUploadComplete}
              onUploadError={handleUploadError}
              appearance={{
                button: "bg-primary-600 hover:bg-primary-700 text-white",
                allowedContent: "text-gray-400",
              }}
            />
          </div>

          {/* Upload Dropzone */}
          <div>
            <h3 className="text-lg font-semibold mb-2">Upload Dropzone</h3>
            <UploadDropzone
              endpoint="mediaUploader"
              onClientUploadComplete={handleUploadComplete}
              onUploadError={handleUploadError}
              appearance={{
                container: "border-2 border-dashed border-gray-300 dark:border-gray-600",
                uploadIcon: "text-gray-400",
                label: "text-gray-600 dark:text-gray-300",
                allowedContent: "text-gray-400",
              }}
            />
          </div>

          {/* Avatar Upload */}
          <div>
            <h3 className="text-lg font-semibold mb-2">Avatar Upload</h3>
            <UploadButton
              endpoint="avatarUploader"
              onClientUploadComplete={handleUploadComplete}
              onUploadError={handleUploadError}
              appearance={{
                button: "bg-green-600 hover:bg-green-700 text-white",
                allowedContent: "text-gray-400",
              }}
            />
          </div>
        </CardContent>
      </Card>

      {/* Uploaded Files Display */}
      {uploadedFiles.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Uploaded Files ({uploadedFiles.length})</CardTitle>
            <CardDescription>
              Files successfully uploaded to UploadThing and saved to database
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4">
              {uploadedFiles.map((file, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-4 border rounded-lg"
                >
                  <div className="flex items-center space-x-4">
                    {file.type.startsWith("image/") ? (
                      <img
                        src={file.url}
                        alt={file.name}
                        className="w-12 h-12 object-cover rounded"
                      />
                    ) : (
                      <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center">
                        <span className="text-xs font-medium">
                          {getFileType(file.type)}
                        </span>
                      </div>
                    )}
                    <div>
                      <p className="font-medium">{file.name}</p>
                      <p className="text-sm text-gray-500">
                        {formatFileSize(file.size)} • {getFileType(file.type)}
                      </p>
                      <p className="text-xs text-gray-400">
                        File ID: {file.fileId}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary">{getFileType(file.type)}</Badge>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => window.open(file.url, "_blank")}
                    >
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => removeFile(index)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
