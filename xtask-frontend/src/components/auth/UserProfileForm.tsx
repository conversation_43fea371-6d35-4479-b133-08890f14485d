'use client'

import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useSession } from 'next-auth/react'
import { useEffect, useState } from 'react'
import { toast } from 'sonner'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'

// Zod schema for user profile form
const userProfileSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255),
  avatar: z.string().url('Invalid avatar URL').optional().or(z.literal('')),
  timezone: z.string().optional(),
  theme: z.enum(['light', 'dark']).optional(),
  notifications: z.boolean().optional(),
  defaultModel: z.string().optional(),
  fallbackModel: z.string().optional(),
  maxTokensPerDay: z.number().int().min(1000).max(100000).optional(),
})

type UserProfileFormValues = z.infer<typeof userProfileSchema>

interface UserProfile {
  id: string
  name: string | null
  email: string
  avatar: string | null
  timezone: string
  theme: string
  notifications: boolean
  defaultModel: string
  fallbackModel: string
  maxTokensPerDay: number
  tokensUsedToday: number
  lastTokenReset: Date
  updatedAt: Date
}

export function UserProfileForm() {
  const { data: session, update } = useSession()
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)

  const form = useForm<UserProfileFormValues>({
    resolver: zodResolver(userProfileSchema),
    defaultValues: {
      name: '',
      avatar: '',
      timezone: 'UTC',
      theme: 'dark',
      notifications: true,
      defaultModel: 'gpt-4',
      fallbackModel: 'gemini-pro',
      maxTokensPerDay: 10000,
    },
  })

  // Fetch user profile data
  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        setIsLoading(true)
        const response = await fetch('/api/auth/me')

        if (!response.ok) {
          throw new Error('Failed to fetch user profile')
        }

        const userData: UserProfile = await response.json()
        setUserProfile(userData)

        // Update form with fetched data
        form.reset({
          name: userData.name || '',
          avatar: userData.avatar || '',
          timezone: userData.timezone,
          theme: userData.theme as 'light' | 'dark',
          notifications: userData.notifications,
          defaultModel: userData.defaultModel,
          fallbackModel: userData.fallbackModel,
          maxTokensPerDay: userData.maxTokensPerDay,
        })
      } catch (error) {
        console.error('Error fetching user profile:', error)
        toast.error('Failed to load user profile')
      } finally {
        setIsLoading(false)
      }
    }

    if (session?.user) {
      fetchUserProfile()
    }
  }, [session, form])

  const onSubmit = async (data: UserProfileFormValues) => {
    try {
      setIsSaving(true)

      const response = await fetch('/api/auth/me', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to update profile')
      }

      const updatedUser = await response.json()
      setUserProfile(updatedUser)

      // Update session to reflect changes
      await update({
        ...session,
        user: {
          ...session?.user,
          name: updatedUser.name,
          image: updatedUser.avatar,
        },
      })

      toast.success('Profile updated successfully!')
    } catch (error: any) {
      console.error('Profile update error:', error)
      toast.error(error.message || 'Failed to update profile')
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>User Profile</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-300 rounded w-1/4"></div>
            <div className="h-10 bg-gray-300 rounded"></div>
            <div className="h-4 bg-gray-300 rounded w-1/4"></div>
            <div className="h-10 bg-gray-300 rounded"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-3">
          <Avatar className="h-10 w-10">
            <AvatarImage src={userProfile?.avatar || ''} />
            <AvatarFallback>
              {userProfile?.name?.[0]?.toUpperCase() || 'U'}
            </AvatarFallback>
          </Avatar>
          User Profile
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Basic Information</h3>

            <div className="space-y-2">
              <Label htmlFor="name">
                Display Name
              </Label>
              <Input
                id="name"
                {...form.register('name')}
                placeholder="Enter your display name"
              />
              {form.formState.errors.name && (
                <p className="text-red-500 text-sm">{form.formState.errors.name.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="avatar">
                Avatar URL
              </Label>
              <Input
                id="avatar"
                {...form.register('avatar')}
                placeholder="https://example.com/avatar.jpg"
              />
              {form.formState.errors.avatar && (
                <p className="text-red-500 text-sm">{form.formState.errors.avatar.message}</p>
              )}
            </div>
          </div>

          {/* Preferences */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Preferences</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="timezone">
                  Timezone
                </Label>
                <Input
                  id="timezone"
                  {...form.register('timezone')}
                  placeholder="UTC"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="theme">
                  Theme
                </Label>
                <Select
                  value={form.watch('theme')}
                  onValueChange={(value) => form.setValue('theme', value as 'light' | 'dark')}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select theme" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="light">Light</SelectItem>
                    <SelectItem value="dark">Dark</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* AI Configuration */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">AI Configuration</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label htmlFor="defaultModel" className="text-sm font-medium">
                  Default AI Model
                </label>
                <Select
                  value={form.watch('defaultModel')}
                  onValueChange={(value) => form.setValue('defaultModel', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select default model" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="gpt-4">GPT-4</SelectItem>
                    <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                    <SelectItem value="gemini-pro">Gemini Pro</SelectItem>
                    <SelectItem value="claude-3-sonnet">Claude 3 Sonnet</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label htmlFor="fallbackModel" className="text-sm font-medium">
                  Fallback AI Model
                </label>
                <Select
                  value={form.watch('fallbackModel')}
                  onValueChange={(value) => form.setValue('fallbackModel', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select fallback model" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="gpt-4">GPT-4</SelectItem>
                    <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                    <SelectItem value="gemini-pro">Gemini Pro</SelectItem>
                    <SelectItem value="claude-3-sonnet">Claude 3 Sonnet</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="maxTokensPerDay" className="text-sm font-medium">
                Max Tokens Per Day
              </label>
              <Input
                id="maxTokensPerDay"
                type="number"
                min="1000"
                max="100000"
                {...form.register('maxTokensPerDay', { valueAsNumber: true })}
              />
              {form.formState.errors.maxTokensPerDay && (
                <p className="text-red-500 text-sm">{form.formState.errors.maxTokensPerDay.message}</p>
              )}
            </div>
          </div>

          {/* Usage Statistics */}
          {userProfile && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Usage Statistics</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="font-medium">Tokens Used Today</div>
                  <div className="text-2xl font-bold text-blue-600">
                    {userProfile.tokensUsedToday.toLocaleString()}
                  </div>
                </div>
                <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="font-medium">Daily Limit</div>
                  <div className="text-2xl font-bold text-green-600">
                    {userProfile.maxTokensPerDay.toLocaleString()}
                  </div>
                </div>
                <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="font-medium">Last Reset</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {new Date(userProfile.lastTokenReset).toLocaleDateString()}
                  </div>
                </div>
              </div>
            </div>
          )}

          <Button type="submit" disabled={isSaving} className="w-full">
            {isSaving ? 'Saving...' : 'Save Profile'}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
