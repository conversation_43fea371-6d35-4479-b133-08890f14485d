'use client'

import { useSession, signIn } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { toast } from 'sonner'
import { useEffect, useState } from 'react'
import { Spinner } from '@/components/ui/spinner'

interface ConnectedAccount {
  id: string
  provider: string
  providerAccountId: string
  type: string
}

interface TwitterAccount {
  id: string
  twitterId: string
  username: string
  displayName: string
  avatar: string | null
  isActive: boolean
  followersCount: number
  followingCount: number
  tweetsCount: number
  lastSyncAt: string
  createdAt: string
}

interface UserData {
  id: string
  name: string | null
  email: string
  avatar: string | null
  accounts: ConnectedAccount[]
  twitterAccounts: TwitterAccount[]
}

export function ConnectedAccountsList() {
  const { data: session, status, update } = useSession()
  const [userData, setUserData] = useState<UserData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [disconnectingAccountId, setDisconnectingAccountId] = useState<string | null>(null)

  useEffect(() => {
    const fetchUserData = async () => {
      if (status === 'authenticated') {
        try {
          setIsLoading(true)
          const response = await fetch('/api/auth/me')
          
          if (response.ok) {
            const data: UserData = await response.json()
            setUserData(data)
          } else {
            toast.error('Failed to fetch connected accounts')
          }
        } catch (error) {
          console.error('Error fetching accounts:', error)
          toast.error('An error occurred while fetching accounts')
        } finally {
          setIsLoading(false)
        }
      } else if (status === 'unauthenticated') {
        setIsLoading(false)
        setUserData(null)
      }
    }

    fetchUserData()
  }, [status])

  const handleDisconnect = async (accountId: string, provider: string) => {
    if (!confirm(`Are you sure you want to disconnect your ${provider} account?`)) {
      return
    }

    try {
      setDisconnectingAccountId(accountId)
      const response = await fetch(`/api/auth/me/accounts/${accountId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to disconnect account')
      }

      // Update local state
      if (userData) {
        setUserData({
          ...userData,
          accounts: userData.accounts.filter((acc) => acc.id !== accountId),
        })
      }

      toast.success(`${provider} account disconnected successfully!`)
      await update() // Refresh session
    } catch (error: any) {
      console.error('Disconnect error:', error)
      toast.error(error.message || 'An error occurred while disconnecting the account')
    } finally {
      setDisconnectingAccountId(null)
    }
  }

  const handleConnect = async (provider: string) => {
    try {
      await signIn(provider, { callbackUrl: '/dashboard/settings/accounts' })
    } catch (error) {
      console.error('Connect error:', error)
      toast.error(`Failed to connect ${provider} account`)
    }
  }

  const getProviderIcon = (provider: string) => {
    switch (provider.toLowerCase()) {
      case 'google':
        return '🔍'
      case 'twitter':
        return '🐦'
      default:
        return '🔗'
    }
  }

  const getProviderName = (provider: string) => {
    switch (provider.toLowerCase()) {
      case 'google':
        return 'Google'
      case 'twitter':
        return 'Twitter/X'
      default:
        return provider.charAt(0).toUpperCase() + provider.slice(1)
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Connected Accounts</CardTitle>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-8">
          <Spinner />
        </CardContent>
      </Card>
    )
  }

  if (!userData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Connected Accounts</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">Please sign in to manage your connected accounts.</p>
        </CardContent>
      </Card>
    )
  }

  const hasGoogleAccount = userData.accounts.some(acc => acc.provider === 'google')
  const hasTwitterAccount = userData.accounts.some(acc => acc.provider === 'twitter')

  return (
    <div className="space-y-6">
      {/* OAuth Accounts */}
      <Card>
        <CardHeader>
          <CardTitle>OAuth Accounts</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {userData.accounts.length === 0 ? (
            <p className="text-muted-foreground">No connected accounts.</p>
          ) : (
            <div className="space-y-3">
              {userData.accounts.map((account) => (
                <div
                  key={account.id}
                  className="flex items-center justify-between p-4 border rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <span className="text-2xl">{getProviderIcon(account.provider)}</span>
                    <div>
                      <div className="font-medium">
                        {getProviderName(account.provider)}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        ID: {account.providerAccountId}
                      </div>
                    </div>
                    <Badge variant="secondary">{account.type}</Badge>
                  </div>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleDisconnect(account.id, account.provider)}
                    disabled={disconnectingAccountId === account.id || userData.accounts.length <= 1}
                  >
                    {disconnectingAccountId === account.id ? 'Disconnecting...' : 'Disconnect'}
                  </Button>
                </div>
              ))}
            </div>
          )}

          {/* Connect New Accounts */}
          <div className="pt-4 border-t">
            <h4 className="font-medium mb-3">Connect New Accounts</h4>
            <div className="flex flex-wrap gap-2">
              {!hasGoogleAccount && (
                <Button
                  variant="outline"
                  onClick={() => handleConnect('google')}
                  className="flex items-center gap-2"
                >
                  🔍 Connect Google
                </Button>
              )}
              {!hasTwitterAccount && (
                <Button
                  variant="outline"
                  onClick={() => handleConnect('twitter')}
                  className="flex items-center gap-2"
                >
                  🐦 Connect Twitter/X
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Twitter Accounts Details */}
      {userData.twitterAccounts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Twitter/X Account Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {userData.twitterAccounts.map((twitterAccount) => (
              <div
                key={twitterAccount.id}
                className="p-4 border rounded-lg space-y-3"
              >
                <div className="flex items-center gap-3">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={twitterAccount.avatar || ''} />
                    <AvatarFallback>
                      {twitterAccount.displayName[0]?.toUpperCase() || 'T'}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="font-medium">{twitterAccount.displayName}</div>
                    <div className="text-sm text-muted-foreground">
                      @{twitterAccount.username}
                    </div>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant={twitterAccount.isActive ? 'default' : 'secondary'}>
                        {twitterAccount.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div className="text-center p-2 bg-gray-50 dark:bg-gray-800 rounded">
                    <div className="font-medium text-lg">
                      {twitterAccount.followersCount.toLocaleString()}
                    </div>
                    <div className="text-muted-foreground">Followers</div>
                  </div>
                  <div className="text-center p-2 bg-gray-50 dark:bg-gray-800 rounded">
                    <div className="font-medium text-lg">
                      {twitterAccount.followingCount.toLocaleString()}
                    </div>
                    <div className="text-muted-foreground">Following</div>
                  </div>
                  <div className="text-center p-2 bg-gray-50 dark:bg-gray-800 rounded">
                    <div className="font-medium text-lg">
                      {twitterAccount.tweetsCount.toLocaleString()}
                    </div>
                    <div className="text-muted-foreground">Tweets</div>
                  </div>
                </div>

                <div className="text-xs text-muted-foreground">
                  Last synced: {new Date(twitterAccount.lastSyncAt).toLocaleString()}
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
