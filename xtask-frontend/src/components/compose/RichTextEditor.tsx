"use client";

import React, { useCallback, useEffect } from 'react';
import { useEditor, EditorContent, BubbleMenu, FloatingMenu } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import CharacterCount from '@tiptap/extension-character-count';
import Link from '@tiptap/extension-link';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  Bold,
  Italic,
  Link as LinkIcon,
  Type,
  MessageSquare,
  Hash,
  Smile,
} from 'lucide-react';

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  maxLength?: number;
  features?: {
    bold?: boolean;
    italic?: boolean;
    mentions?: boolean;
    hashtags?: boolean;
    links?: boolean;
    emoji?: boolean;
  };
  className?: string;
  disabled?: boolean;
  onMentionSearch?: (query: string) => Promise<any[]>;
  onCharacterCountChange?: (count: number, maxLength?: number) => void;
}

const RichTextEditor: React.FC<RichTextEditorProps> = ({
  value,
  onChange,
  placeholder = "What's happening?",
  maxLength = 280,
  features = {
    bold: true,
    italic: true,
    mentions: true,
    hashtags: true,
    links: true,
    emoji: true,
  },
  className,
  disabled = false,
  onMentionSearch,
  onCharacterCountChange,
}) => {
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        // Disable some extensions we don't need for tweets
        heading: false,
        codeBlock: false,
        blockquote: false,
        horizontalRule: false,
        listItem: false,
        orderedList: false,
        bulletList: false,
        // Keep essential ones
        bold: features.bold,
        italic: features.italic,
        history: true,
        paragraph: true,
        document: true,
        text: true,
      }),
      Placeholder.configure({
        placeholder,
        emptyEditorClass: 'is-editor-empty',
      }),
      CharacterCount.configure({
        limit: maxLength,
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-primary-400 hover:text-primary-300 underline',
        },
      }),
    ],
    content: value,
    editable: !disabled,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      const text = editor.getText();
      onChange(html);
      
      // Notify parent of character count changes
      if (onCharacterCountChange) {
        onCharacterCountChange(text.length, maxLength);
      }
    },
    editorProps: {
      attributes: {
        class: cn(
          'prose prose-invert max-w-none focus:outline-none',
          'min-h-[120px] p-4 text-white',
          'prose-p:my-1 prose-p:leading-relaxed',
          className
        ),
      },
    },
  });

  // Update editor content when value prop changes
  useEffect(() => {
    if (editor && value !== editor.getHTML()) {
      editor.commands.setContent(value);
    }
  }, [editor, value]);

  // Character count and limit handling
  const characterCount = editor?.storage.characterCount.characters() || 0;
  const isNearLimit = characterCount > maxLength * 0.8;
  const isOverLimit = characterCount > maxLength;

  // Toolbar actions
  const toggleBold = useCallback(() => {
    editor?.chain().focus().toggleBold().run();
  }, [editor]);

  const toggleItalic = useCallback(() => {
    editor?.chain().focus().toggleItalic().run();
  }, [editor]);

  const addLink = useCallback(() => {
    const url = window.prompt('Enter URL:');
    if (url) {
      editor?.chain().focus().setLink({ href: url }).run();
    }
  }, [editor]);

  const insertMention = useCallback(() => {
    // Placeholder for mention functionality
    const mention = window.prompt('Enter username:');
    if (mention) {
      editor?.chain().focus().insertContent(`@${mention} `).run();
    }
  }, [editor]);

  const insertHashtag = useCallback(() => {
    // Placeholder for hashtag functionality
    const hashtag = window.prompt('Enter hashtag:');
    if (hashtag) {
      editor?.chain().focus().insertContent(`#${hashtag} `).run();
    }
  }, [editor]);

  if (!editor) {
    return (
      <div className="min-h-[120px] p-4 border border-dark-border rounded-lg bg-dark-surface animate-pulse">
        <div className="h-4 bg-gray-600 rounded w-1/3 mb-2"></div>
        <div className="h-4 bg-gray-600 rounded w-1/2"></div>
      </div>
    );
  }

  return (
    <div className="relative">
      {/* Floating Menu - appears when editor is empty */}
      <FloatingMenu
        editor={editor}
        tippyOptions={{ duration: 100 }}
        className="flex items-center gap-1 p-2 bg-dark-surface border border-dark-border rounded-lg shadow-lg"
      >
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().setParagraph().run()}
          className="h-8 w-8 p-0"
        >
          <Type className="h-4 w-4" />
        </Button>
      </FloatingMenu>

      {/* Bubble Menu - appears when text is selected */}
      <BubbleMenu
        editor={editor}
        tippyOptions={{ duration: 100 }}
        className="flex items-center gap-1 p-2 bg-dark-surface border border-dark-border rounded-lg shadow-lg"
      >
        {features.bold && (
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleBold}
            className={cn(
              "h-8 w-8 p-0",
              editor.isActive('bold') && "bg-primary-600 text-white"
            )}
          >
            <Bold className="h-4 w-4" />
          </Button>
        )}
        
        {features.italic && (
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleItalic}
            className={cn(
              "h-8 w-8 p-0",
              editor.isActive('italic') && "bg-primary-600 text-white"
            )}
          >
            <Italic className="h-4 w-4" />
          </Button>
        )}

        {features.links && (
          <Button
            variant="ghost"
            size="sm"
            onClick={addLink}
            className={cn(
              "h-8 w-8 p-0",
              editor.isActive('link') && "bg-primary-600 text-white"
            )}
          >
            <LinkIcon className="h-4 w-4" />
          </Button>
        )}
      </BubbleMenu>

      {/* Main Editor Container */}
      <div className="border border-dark-border rounded-lg bg-dark-surface focus-within:ring-2 focus-within:ring-primary-500 focus-within:border-transparent">
        <EditorContent editor={editor} />
        
        {/* Bottom Toolbar */}
        <div className="flex items-center justify-between p-3 border-t border-dark-border">
          <div className="flex items-center gap-2">
            {features.mentions && (
              <Button
                variant="ghost"
                size="sm"
                onClick={insertMention}
                className="h-8 w-8 p-0 text-gray-400 hover:text-white"
                title="Add mention"
              >
                <MessageSquare className="h-4 w-4" />
              </Button>
            )}
            
            {features.hashtags && (
              <Button
                variant="ghost"
                size="sm"
                onClick={insertHashtag}
                className="h-8 w-8 p-0 text-gray-400 hover:text-white"
                title="Add hashtag"
              >
                <Hash className="h-4 w-4" />
              </Button>
            )}

            {features.emoji && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  // Placeholder for emoji picker
                  editor?.chain().focus().insertContent('😊 ').run();
                }}
                className="h-8 w-8 p-0 text-gray-400 hover:text-white"
                title="Add emoji"
              >
                <Smile className="h-4 w-4" />
              </Button>
            )}
          </div>

          {/* Character Counter */}
          <div className="flex items-center gap-2">
            <span
              className={cn(
                "text-sm font-medium",
                isOverLimit && "text-red-400",
                isNearLimit && !isOverLimit && "text-yellow-400",
                !isNearLimit && "text-gray-400"
              )}
            >
              {characterCount}
              {maxLength && `/${maxLength}`}
            </span>
            
            {/* Visual progress indicator */}
            {maxLength && (
              <div className="w-8 h-8 relative">
                <svg className="w-8 h-8 transform -rotate-90" viewBox="0 0 32 32">
                  <circle
                    cx="16"
                    cy="16"
                    r="14"
                    stroke="currentColor"
                    strokeWidth="2"
                    fill="none"
                    className="text-gray-600"
                  />
                  <circle
                    cx="16"
                    cy="16"
                    r="14"
                    stroke="currentColor"
                    strokeWidth="2"
                    fill="none"
                    strokeDasharray={`${2 * Math.PI * 14}`}
                    strokeDashoffset={`${2 * Math.PI * 14 * (1 - characterCount / maxLength)}`}
                    className={cn(
                      "transition-all duration-200",
                      isOverLimit && "text-red-400",
                      isNearLimit && !isOverLimit && "text-yellow-400",
                      !isNearLimit && "text-primary-400"
                    )}
                  />
                </svg>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default RichTextEditor;
