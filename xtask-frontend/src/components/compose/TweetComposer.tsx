"use client";

import React, { useState, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import RichTextEditor from './RichTextEditor';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useUploadThing } from '@/utils/uploadthing';
import { cn } from '@/lib/utils';
import {
  Image as ImageIcon,
  Video as VideoIcon,
  Calendar,
  Send,
  Save,
  X,
  Loader2,
  AlertCircle,
} from 'lucide-react';

// Form validation schema
const tweetSchema = z.object({
  content: z.string().min(1, 'Tweet content is required').max(280, 'Tweet must be 280 characters or less'),
  mediaUrls: z.array(z.string()).max(4, 'Maximum 4 media files allowed'),
  agentId: z.string().optional(),
  scheduledAt: z.date().optional(),
});

type TweetFormData = z.infer<typeof tweetSchema>;

interface TweetComposerProps {
  mode?: 'compose' | 'schedule' | 'draft';
  initialContent?: string;
  agentId?: string;
  onPublish?: (data: TweetFormData) => Promise<void>;
  onSchedule?: (data: TweetFormData, scheduledAt: Date) => Promise<void>;
  onSaveDraft?: (data: TweetFormData) => Promise<void>;
  onCancel?: () => void;
  className?: string;
}

interface UploadedMedia {
  id: string;
  url: string;
  type: 'image' | 'video';
  name: string;
  size: number;
  key: string;
}

const TweetComposer: React.FC<TweetComposerProps> = ({
  mode = 'compose',
  initialContent = '',
  agentId,
  onPublish,
  onSchedule,
  onSaveDraft,
  onCancel,
  className,
}) => {
  const [content, setContent] = useState(initialContent);
  const [uploadedMedia, setUploadedMedia] = useState<UploadedMedia[]>([]);
  const [characterCount, setCharacterCount] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<TweetFormData>({
    resolver: zodResolver(tweetSchema),
    defaultValues: {
      content: initialContent,
      mediaUrls: [],
      agentId,
    },
  });

  // UploadThing integration
  const { startUpload, isUploading } = useUploadThing('mediaUploader', {
    onClientUploadComplete: (res) => {
      if (res && res.length > 0) {
        const newMedia: UploadedMedia[] = res.map((file) => ({
          id: file.serverData?.fileId || Date.now().toString(),
          url: file.url,
          type: file.type?.startsWith('image') ? 'image' : 'video',
          name: file.name,
          size: file.size,
          key: file.key,
        }));
        
        setUploadedMedia((prev) => [...prev, ...newMedia]);
        
        // Update form with new media URLs
        const allMediaUrls = [...uploadedMedia, ...newMedia].map(m => m.url);
        form.setValue('mediaUrls', allMediaUrls);
        
        setError(null);
      }
    },
    onUploadError: (error: Error) => {
      console.error('Upload error:', error);
      setError(`Upload failed: ${error.message}`);
    },
  });

  // Handle file selection
  const handleFileSelect = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    // Check media limit
    if (uploadedMedia.length + files.length > 4) {
      setError('Maximum 4 media files allowed');
      return;
    }

    const fileArray = Array.from(files);
    
    // Validate file types
    const validFiles = fileArray.filter(file => {
      const isImage = file.type.startsWith('image/');
      const isVideo = file.type.startsWith('video/');
      return isImage || isVideo;
    });

    if (validFiles.length !== fileArray.length) {
      setError('Only image and video files are supported');
      return;
    }

    try {
      await startUpload(validFiles);
    } catch (error) {
      console.error('Upload initiation failed:', error);
      setError('Failed to start upload');
    }

    // Clear the input
    event.target.value = '';
  }, [uploadedMedia.length, startUpload]);

  // Remove media
  const removeMedia = useCallback((mediaId: string) => {
    setUploadedMedia((prev) => {
      const updated = prev.filter(m => m.id !== mediaId);
      const updatedUrls = updated.map(m => m.url);
      form.setValue('mediaUrls', updatedUrls);
      return updated;
    });
  }, [form]);

  // Handle character count changes
  const handleCharacterCountChange = useCallback((count: number, maxLength?: number) => {
    setCharacterCount(count);
  }, []);

  // Form submission handlers
  const handlePublish = async (data: TweetFormData) => {
    if (!onPublish) return;
    
    setIsSubmitting(true);
    setError(null);
    
    try {
      await onPublish(data);
    } catch (error) {
      console.error('Publish error:', error);
      setError('Failed to publish tweet');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSaveDraft = async () => {
    if (!onSaveDraft) return;
    
    const data = form.getValues();
    data.content = content;
    
    setIsSubmitting(true);
    setError(null);
    
    try {
      await onSaveDraft(data);
    } catch (error) {
      console.error('Save draft error:', error);
      setError('Failed to save draft');
    } finally {
      setIsSubmitting(false);
    }
  };

  const onSubmit = (data: TweetFormData) => {
    data.content = content;
    data.mediaUrls = uploadedMedia.map(m => m.url);
    
    if (mode === 'schedule') {
      // Handle scheduling logic
      console.log('Schedule tweet:', data);
    } else {
      handlePublish(data);
    }
  };

  const isOverLimit = characterCount > 280;
  const canSubmit = content.trim().length > 0 && !isOverLimit && !isSubmitting && !isUploading;

  return (
    <Card className={cn("w-full max-w-2xl", className)}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>
            {mode === 'compose' && 'Compose Tweet'}
            {mode === 'schedule' && 'Schedule Tweet'}
            {mode === 'draft' && 'Draft Tweet'}
          </span>
          {onCancel && (
            <Button variant="ghost" size="sm" onClick={onCancel}>
              <X className="h-4 w-4" />
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          {/* Rich Text Editor */}
          <RichTextEditor
            value={content}
            onChange={setContent}
            placeholder="What's happening?"
            maxLength={280}
            onCharacterCountChange={handleCharacterCountChange}
            features={{
              bold: true,
              italic: true,
              mentions: true,
              hashtags: true,
              links: true,
              emoji: true,
            }}
          />

          {/* Error Display */}
          {error && (
            <div className="flex items-center gap-2 p-3 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">{error}</span>
            </div>
          )}

          {/* Media Upload Section */}
          <div className="space-y-3">
            {/* Upload Controls */}
            <div className="flex items-center gap-2">
              <label htmlFor="media-upload" className="cursor-pointer">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  disabled={isUploading || uploadedMedia.length >= 4}
                  className="gap-2"
                  asChild
                >
                  <span>
                    {isUploading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <ImageIcon className="h-4 w-4" />
                    )}
                    Add Media
                  </span>
                </Button>
              </label>
              
              <input
                id="media-upload"
                type="file"
                accept="image/*,video/*"
                multiple
                onChange={handleFileSelect}
                className="hidden"
                disabled={isUploading || uploadedMedia.length >= 4}
              />
              
              <Badge variant="secondary" className="text-xs">
                {uploadedMedia.length}/4
              </Badge>
            </div>

            {/* Media Preview Grid */}
            {uploadedMedia.length > 0 && (
              <div className="grid grid-cols-2 gap-3">
                {uploadedMedia.map((media) => (
                  <div key={media.id} className="relative group">
                    <div className="relative aspect-video bg-dark-surface rounded-lg overflow-hidden border border-dark-border">
                      {media.type === 'image' ? (
                        <img
                          src={media.url}
                          alt={media.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <video
                          src={media.url}
                          className="w-full h-full object-cover"
                          controls={false}
                          muted
                        />
                      )}
                      
                      {/* Media type indicator */}
                      <div className="absolute top-2 left-2">
                        <Badge variant="secondary" className="text-xs">
                          {media.type === 'image' ? (
                            <ImageIcon className="h-3 w-3 mr-1" />
                          ) : (
                            <VideoIcon className="h-3 w-3 mr-1" />
                          )}
                          {media.type}
                        </Badge>
                      </div>
                      
                      {/* Remove button */}
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        onClick={() => removeMedia(media.id)}
                        className="absolute top-2 right-2 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                    
                    {/* Media info */}
                    <p className="text-xs text-gray-400 mt-1 truncate">
                      {media.name}
                    </p>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-between pt-4 border-t border-dark-border">
            <div className="flex items-center gap-2">
              {mode === 'schedule' && (
                <Button type="button" variant="outline" size="sm" className="gap-2">
                  <Calendar className="h-4 w-4" />
                  Schedule
                </Button>
              )}
              
              {onSaveDraft && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleSaveDraft}
                  disabled={isSubmitting}
                  className="gap-2"
                >
                  <Save className="h-4 w-4" />
                  Save Draft
                </Button>
              )}
            </div>

            <Button
              type="submit"
              disabled={!canSubmit}
              className="gap-2"
            >
              {isSubmitting ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
              {mode === 'schedule' ? 'Schedule' : 'Tweet'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default TweetComposer;
