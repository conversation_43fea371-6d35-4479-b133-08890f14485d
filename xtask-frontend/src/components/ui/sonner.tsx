"use client"

import { useTheme } from "next-themes"
import { Toaster as Son<PERSON> } from "sonner"

type ToasterProps = React.ComponentProps<typeof Sonner>

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = "system" } = useTheme()

  return (
    <Sonner
      theme={theme as ToasterProps["theme"]}
      className="toaster group"
      toastOptions={{
        classNames: {
          toast:
            "group toast group-[.toaster]:bg-dark-surface group-[.toaster]:text-white group-[.toaster]:border-dark-border group-[.toaster]:shadow-lg",
          description: "group-[.toast]:text-dark-text-muted",
          actionButton:
            "group-[.toast]:bg-primary-500 group-[.toast]:text-white",
          cancelButton:
            "group-[.toast]:bg-dark-border group-[.toast]:text-dark-text-muted",
        },
      }}
      {...props}
    />
  )
}

export { Toaster }
