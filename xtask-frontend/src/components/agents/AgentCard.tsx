"use client"

import { useState } from "react"
import { MoreH<PERSON>zon<PERSON>, Edit, Trash2, Power, PowerOff } from "lucide-react"
import { AgentListItem } from "@/types/agent"
import { Card } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

interface AgentCardProps {
  agent: AgentListItem
  onEdit: (agent: AgentListItem) => void
  onDelete: (agentId: string) => void
  onToggleStatus: (agentId: string, isActive: boolean) => void
  isLoading?: boolean
}

export function AgentCard({ 
  agent, 
  onEdit, 
  onDelete, 
  onToggleStatus, 
  isLoading = false 
}: AgentCardProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)

  const handleDelete = () => {
    onDelete(agent.id)
    setShowDeleteDialog(false)
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    }).format(new Date(date))
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  return (
    <>
      <Card className="p-6 hover:shadow-lg transition-shadow duration-200">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-4 flex-1">
            <Avatar className="h-12 w-12">
              <AvatarImage src={agent.avatar || undefined} alt={agent.name} />
              <AvatarFallback className="bg-primary-500 text-white">
                {getInitials(agent.name)}
              </AvatarFallback>
            </Avatar>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-2">
                <h3 className="text-lg font-semibold text-white truncate">
                  {agent.name}
                </h3>
                <Badge variant={agent.isActive ? "default" : "secondary"}>
                  {agent.isActive ? "Active" : "Inactive"}
                </Badge>
              </div>
              
              {agent.description && (
                <p className="text-dark-text-muted text-sm mb-3 line-clamp-2">
                  {agent.description}
                </p>
              )}
              
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <p className="text-dark-text-muted">Tweets</p>
                  <p className="text-white font-medium">{agent.totalTweets}</p>
                </div>
                <div>
                  <p className="text-dark-text-muted">Engagement</p>
                  <p className="text-white font-medium">{agent.totalEngagement}</p>
                </div>
                <div>
                  <p className="text-dark-text-muted">Avg Rate</p>
                  <p className="text-white font-medium">
                    {agent.avgEngagement.toFixed(1)}%
                  </p>
                </div>
              </div>
              
              <p className="text-xs text-dark-text-muted mt-3">
                Created {formatDate(agent.createdAt)}
              </p>
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                size="sm" 
                disabled={isLoading}
                className="h-8 w-8 p-0"
              >
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">Open menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onEdit(agent)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => onToggleStatus(agent.id, !agent.isActive)}
              >
                {agent.isActive ? (
                  <>
                    <PowerOff className="mr-2 h-4 w-4" />
                    Deactivate
                  </>
                ) : (
                  <>
                    <Power className="mr-2 h-4 w-4" />
                    Activate
                  </>
                )}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => setShowDeleteDialog(true)}
                className="text-red-400 focus:text-red-400"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Agent</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{agent.name}"? This action cannot be undone.
              All scheduled tweets and memories associated with this agent will also be deleted.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end space-x-2 mt-4">
            <Button 
              variant="ghost" 
              onClick={() => setShowDeleteDialog(false)}
            >
              Cancel
            </Button>
            <Button 
              variant="danger" 
              onClick={handleDelete}
              disabled={isLoading}
            >
              Delete Agent
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
