"use client"

import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Agent, CreateAgentData, createAgentSchema, AGENT_TONES, POSTING_FREQUENCIES } from "@/types/agent"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Spinner } from "@/components/ui/spinner"
import { ModelSelector } from "@/components/ai/ModelSelector"

interface AgentFormProps {
  agent?: Agent | null
  onSubmit: (data: CreateAgentData) => void
  onCancel: () => void
  isLoading?: boolean
  mode: "create" | "edit"
}

export function AgentForm({
  agent,
  onSubmit,
  onCancel,
  isLoading = false,
  mode
}: AgentFormProps) {
  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    watch,
    setValue,
    getValues,
  } = useForm<CreateAgentData>({
    resolver: zodResolver(createAgentSchema),
    defaultValues: agent ? {
      name: agent.name,
      description: agent.description || "",
      avatar: agent.avatar || "",
      tone: agent.tone || "",
      creativity: agent.creativity || 0.7,
      preferredModel: agent.preferredModel || "",
      temperature: agent.temperature || 0.7,
      maxTokens: agent.maxTokens || 1000,
      postingFrequency: agent.postingFrequency || "",
      timezone: agent.timezone || "",
      topics: agent.topics || [],
      preferredTimes: agent.preferredTimes || [],
    } : {
      name: "",
      description: "",
      creativity: 0.7,
      temperature: 0.7,
      maxTokens: 1000,
      topics: [],
      preferredTimes: [],
    },
  })

  const watchedTopics = watch("topics") || []
  const watchedTimes = watch("preferredTimes") || []

  const addTopic = (topic: string) => {
    if (topic.trim() && !watchedTopics.includes(topic.trim())) {
      setValue("topics", [...watchedTopics, topic.trim()])
    }
  }

  const removeTopic = (topicToRemove: string) => {
    setValue("topics", watchedTopics.filter(topic => topic !== topicToRemove))
  }

  const addTime = (time: string) => {
    if (time && !watchedTimes.includes(time)) {
      setValue("preferredTimes", [...watchedTimes, time])
    }
  }

  const removeTime = (timeToRemove: string) => {
    setValue("preferredTimes", watchedTimes.filter(time => time !== timeToRemove))
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">
          {mode === "create" ? "Create New Agent" : "Edit Agent"}
        </h2>
      </div>

      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="basic">Basic Info</TabsTrigger>
          <TabsTrigger value="ai">AI Settings</TabsTrigger>
          <TabsTrigger value="schedule">Schedule</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4">
          <Card className="p-4">
            <div className="space-y-4">
              <div>
                <Input
                  label="Agent Name"
                  placeholder="Enter agent name"
                  {...register("name")}
                  error={errors.name?.message}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  Description
                </label>
                <textarea
                  className="w-full px-3 py-2 bg-dark-surface border border-dark-border rounded-md text-white placeholder-dark-text-muted focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
                  rows={3}
                  placeholder="Describe your agent's purpose and personality"
                  {...register("description")}
                />
                {errors.description && (
                  <p className="text-red-400 text-sm mt-1">{errors.description.message}</p>
                )}
              </div>

              <div>
                <Input
                  label="Avatar URL (optional)"
                  placeholder="https://example.com/avatar.jpg"
                  {...register("avatar")}
                  error={errors.avatar?.message}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  Tone
                </label>
                <select
                  className="w-full px-3 py-2 bg-dark-surface border border-dark-border rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  {...register("tone")}
                >
                  <option value="">Select a tone</option>
                  {Object.entries(AGENT_TONES).map(([key, value]) => (
                    <option key={key} value={value}>
                      {key.charAt(0) + key.slice(1).toLowerCase().replace('_', ' ')}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  Topics
                </label>
                <div className="flex flex-wrap gap-2 mb-2">
                  {watchedTopics.map((topic, index) => (
                    <Badge key={index} variant="secondary" className="cursor-pointer" onClick={() => removeTopic(topic)}>
                      {topic} ×
                    </Badge>
                  ))}
                </div>
                <div className="flex gap-2">
                  <Input
                    placeholder="Add a topic"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault()
                        addTopic(e.currentTarget.value)
                        e.currentTarget.value = ''
                      }
                    }}
                  />
                </div>
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="ai" className="space-y-4">
          <Card className="p-4">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  Preferred AI Model
                </label>
                <ModelSelector
                  value={watch("preferredModel")}
                  onValueChange={(value) => setValue("preferredModel", value)}
                  placeholder="Select an AI model..."
                  showDetails={true}
                  className="w-full"
                />
                {errors.preferredModel && (
                  <p className="text-red-400 text-sm mt-1">{errors.preferredModel.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  Creativity: {watch("creativity")}
                </label>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  className="w-full"
                  {...register("creativity", { valueAsNumber: true })}
                />
                <div className="flex justify-between text-xs text-dark-text-muted mt-1">
                  <span>Conservative</span>
                  <span>Creative</span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  Temperature: {watch("temperature")}
                </label>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  className="w-full"
                  {...register("temperature", { valueAsNumber: true })}
                />
                <div className="flex justify-between text-xs text-dark-text-muted mt-1">
                  <span>Focused</span>
                  <span>Random</span>
                </div>
              </div>

              <div>
                <Input
                  label="Max Tokens"
                  type="number"
                  placeholder="1000"
                  {...register("maxTokens", { valueAsNumber: true })}
                  error={errors.maxTokens?.message}
                />
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="schedule" className="space-y-4">
          <Card className="p-4">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  Posting Frequency
                </label>
                <select
                  className="w-full px-3 py-2 bg-dark-surface border border-dark-border rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  {...register("postingFrequency")}
                >
                  <option value="">Select frequency</option>
                  {Object.entries(POSTING_FREQUENCIES).map(([key, value]) => (
                    <option key={key} value={value}>
                      {key.charAt(0) + key.slice(1).toLowerCase()}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <Input
                  label="Timezone"
                  placeholder="America/New_York"
                  {...register("timezone")}
                  error={errors.timezone?.message}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  Preferred Times
                </label>
                <div className="flex flex-wrap gap-2 mb-2">
                  {watchedTimes.map((time, index) => (
                    <Badge key={index} variant="secondary" className="cursor-pointer" onClick={() => removeTime(time)}>
                      {time} ×
                    </Badge>
                  ))}
                </div>
                <div className="flex gap-2">
                  <input
                    type="time"
                    className="px-3 py-2 bg-dark-surface border border-dark-border rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    onChange={(e) => {
                      if (e.target.value) {
                        addTime(e.target.value)
                        e.target.value = ''
                      }
                    }}
                  />
                </div>
              </div>
            </div>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="flex justify-end space-x-2 pt-4 border-t border-dark-border">
        <Button variant="ghost" onClick={onCancel} disabled={isLoading}>
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={!isValid || isLoading}
          className="min-w-[120px]"
        >
          {isLoading ? (
            <Spinner size="sm" variant="white" />
          ) : (
            mode === "create" ? "Create Agent" : "Update Agent"
          )}
        </Button>
      </div>
    </form>
  )
}
