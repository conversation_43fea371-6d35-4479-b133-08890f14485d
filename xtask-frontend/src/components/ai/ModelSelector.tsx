"use client"

import { useState, useEffect } from "react"
import { useQuery } from "@tanstack/react-query"
import { RefreshCw, Info, DollarSign, Zap } from "lucide-react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectGroup,
  SelectLabel,
} from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Spinner } from "@/components/ui/spinner"

interface ModelInfo {
  id: string
  name: string
  provider: string
  maxTokens: number
  supportsStreaming: boolean
  costPer1kTokens: {
    input: number
    output: number
  }
  description: string
}

interface ProviderData {
  provider: string
  available: boolean
  models: ModelInfo[]
  error?: string
}

interface ModelSelectorProps {
  value?: string
  onValueChange: (value: string) => void
  provider?: string // Optional: filter by specific provider
  disabled?: boolean
  placeholder?: string
  showDetails?: boolean
  className?: string
}

export function ModelSelector({
  value,
  onValueChange,
  provider,
  disabled = false,
  placeholder = "Select a model...",
  showDetails = true,
  className
}: ModelSelectorProps) {
  const [isRefreshing, setIsRefreshing] = useState(false)

  // Fetch available models
  const {
    data: modelsData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['ai-models', provider],
    queryFn: async () => {
      const url = provider 
        ? `/api/ai/models?provider=${provider}`
        : '/api/ai/models'
      
      const response = await fetch(url)
      if (!response.ok) {
        throw new Error('Failed to fetch models')
      }
      return response.json()
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2
  })

  // Get selected model info
  const selectedModel = getSelectedModelInfo(value, modelsData)

  // Refresh models
  const handleRefresh = async () => {
    setIsRefreshing(true)
    try {
      const refreshUrl = '/api/ai/models/refresh'
      const refreshBody = provider ? { provider } : {}
      
      await fetch(refreshUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(refreshBody)
      })
      
      await refetch()
    } catch (error) {
      console.error('Failed to refresh models:', error)
    } finally {
      setIsRefreshing(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center space-x-2">
        <Spinner className="h-4 w-4" />
        <span className="text-sm text-muted-foreground">Loading models...</span>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center space-x-2">
        <span className="text-sm text-destructive">Failed to load models</span>
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefresh}
          disabled={isRefreshing}
        >
          <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
        </Button>
      </div>
    )
  }

  return (
    <div className={className}>
      <div className="flex items-center space-x-2">
        <Select value={value} onValueChange={onValueChange} disabled={disabled}>
          <SelectTrigger className="flex-1">
            <SelectValue placeholder={placeholder} />
          </SelectTrigger>
          <SelectContent>
            {renderModelOptions(modelsData, provider)}
          </SelectContent>
        </Select>
        
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefresh}
          disabled={isRefreshing}
          title="Refresh models"
        >
          <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
        </Button>
      </div>

      {/* Model details card */}
      {showDetails && selectedModel && (
        <Card className="mt-3">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium">
                {selectedModel.name}
              </CardTitle>
              <Badge variant="secondary" className="text-xs">
                {selectedModel.provider}
              </Badge>
            </div>
            <CardDescription className="text-xs">
              {selectedModel.description}
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="grid grid-cols-2 gap-3 text-xs">
              <div className="flex items-center space-x-1">
                <Info className="h-3 w-3 text-muted-foreground" />
                <span className="text-muted-foreground">Max Tokens:</span>
                <span className="font-medium">
                  {selectedModel.maxTokens.toLocaleString()}
                </span>
              </div>
              
              <div className="flex items-center space-x-1">
                <Zap className="h-3 w-3 text-muted-foreground" />
                <span className="text-muted-foreground">Streaming:</span>
                <Badge 
                  variant={selectedModel.supportsStreaming ? "default" : "secondary"}
                  className="text-xs px-1 py-0"
                >
                  {selectedModel.supportsStreaming ? "Yes" : "No"}
                </Badge>
              </div>
              
              <div className="flex items-center space-x-1">
                <DollarSign className="h-3 w-3 text-muted-foreground" />
                <span className="text-muted-foreground">Input:</span>
                <span className="font-medium">
                  ${selectedModel.costPer1kTokens.input.toFixed(4)}/1K
                </span>
              </div>
              
              <div className="flex items-center space-x-1">
                <DollarSign className="h-3 w-3 text-muted-foreground" />
                <span className="text-muted-foreground">Output:</span>
                <span className="font-medium">
                  ${selectedModel.costPer1kTokens.output.toFixed(4)}/1K
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

// Helper functions
function renderModelOptions(modelsData: any, filterProvider?: string) {
  if (!modelsData) return null

  if (filterProvider && modelsData.models) {
    // Single provider response
    return modelsData.models.map((model: ModelInfo) => (
      <SelectItem key={model.id} value={model.id}>
        <div className="flex items-center justify-between w-full">
          <span>{model.name}</span>
          <div className="flex items-center space-x-1 ml-2">
            <Badge variant="outline" className="text-xs">
              {(model.maxTokens / 1000).toFixed(0)}K
            </Badge>
            {model.costPer1kTokens.input === 0 && (
              <Badge variant="secondary" className="text-xs">
                Free
              </Badge>
            )}
          </div>
        </div>
      </SelectItem>
    ))
  }

  // Multiple providers response
  const { modelsByProvider } = modelsData
  if (!modelsByProvider) return null

  return Object.entries(modelsByProvider).map(([providerName, providerData]: [string, any]) => {
    if (!providerData.available || !providerData.models?.length) return null

    return (
      <SelectGroup key={providerName}>
        <SelectLabel className="capitalize">{providerName}</SelectLabel>
        {providerData.models.map((model: ModelInfo) => (
          <SelectItem key={model.id} value={model.id}>
            <div className="flex items-center justify-between w-full">
              <span>{model.name}</span>
              <div className="flex items-center space-x-1 ml-2">
                <Badge variant="outline" className="text-xs">
                  {(model.maxTokens / 1000).toFixed(0)}K
                </Badge>
                {model.costPer1kTokens.input === 0 && (
                  <Badge variant="secondary" className="text-xs">
                    Free
                  </Badge>
                )}
              </div>
            </div>
          </SelectItem>
        ))}
      </SelectGroup>
    )
  })
}

function getSelectedModelInfo(selectedValue: string | undefined, modelsData: any): ModelInfo | null {
  if (!selectedValue || !modelsData) return null

  if (modelsData.models) {
    // Single provider response
    return modelsData.models.find((model: ModelInfo) => model.id === selectedValue) || null
  }

  // Multiple providers response
  const { modelsByProvider } = modelsData
  if (!modelsByProvider) return null

  for (const providerData of Object.values(modelsByProvider) as any[]) {
    if (providerData.models) {
      const model = providerData.models.find((m: ModelInfo) => m.id === selectedValue)
      if (model) return model
    }
  }

  return null
}
