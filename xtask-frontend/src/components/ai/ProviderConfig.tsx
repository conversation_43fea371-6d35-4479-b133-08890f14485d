"use client"

import { useState } from "react"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { Eye, EyeOff, TestTube, CheckCircle, XCircle, Settings } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Spinner } from "@/components/ui/spinner"
import { toast } from "sonner"

interface ProviderStatus {
  name: string
  available: boolean
  configured: boolean
  rateLimitInfo?: any
  lastError?: any
  responseTime?: number
}

interface ProviderConfigProps {
  provider: string
  title: string
  description: string
  apiKeyLabel?: string
  baseUrlLabel?: string
  showBaseUrl?: boolean
  className?: string
}

export function ProviderConfig({
  provider,
  title,
  description,
  apiKeyLabel = "API Key",
  baseUrlLabel = "Base URL",
  showBaseUrl = true,
  className
}: ProviderConfigProps) {
  const [showApiKey, setShowApiKey] = useState(false)
  const [apiKey, setApiKey] = useState("")
  const [baseUrl, setBaseUrl] = useState("")
  const [isTesting, setIsTesting] = useState(false)

  const queryClient = useQueryClient()

  // Fetch provider status
  const { data: statusData, isLoading: statusLoading } = useQuery({
    queryKey: ['ai-provider-status'],
    queryFn: async () => {
      const response = await fetch('/api/ai/status')
      if (!response.ok) throw new Error('Failed to fetch status')
      return response.json()
    },
    refetchInterval: 30000, // Refresh every 30 seconds
  })

  // Get current provider status
  const providerStatus = statusData?.providerStatuses?.find(
    (p: ProviderStatus) => p.name.toLowerCase() === provider.toLowerCase()
  )

  // Test configuration mutation
  const testConfigMutation = useMutation({
    mutationFn: async (config: { apiKey: string; baseUrl?: string }) => {
      const response = await fetch('/api/ai/test-config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          provider,
          ...config
        })
      })
      
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Configuration test failed')
      }
      
      return response.json()
    },
    onSuccess: () => {
      toast.success(`${title} configuration is valid!`)
      queryClient.invalidateQueries({ queryKey: ['ai-provider-status'] })
    },
    onError: (error: Error) => {
      toast.error(`${title} test failed: ${error.message}`)
    }
  })

  // Save configuration mutation
  const saveConfigMutation = useMutation({
    mutationFn: async (config: { apiKey: string; baseUrl?: string }) => {
      const response = await fetch('/api/ai/config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          provider,
          ...config
        })
      })
      
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to save configuration')
      }
      
      return response.json()
    },
    onSuccess: () => {
      toast.success(`${title} configuration saved!`)
      queryClient.invalidateQueries({ queryKey: ['ai-provider-status'] })
      queryClient.invalidateQueries({ queryKey: ['ai-models'] })
    },
    onError: (error: Error) => {
      toast.error(`Failed to save ${title} config: ${error.message}`)
    }
  })

  const handleTest = async () => {
    if (!apiKey.trim()) {
      toast.error('API key is required')
      return
    }

    setIsTesting(true)
    try {
      await testConfigMutation.mutateAsync({
        apiKey: apiKey.trim(),
        ...(showBaseUrl && baseUrl.trim() && { baseUrl: baseUrl.trim() })
      })
    } finally {
      setIsTesting(false)
    }
  }

  const handleSave = async () => {
    if (!apiKey.trim()) {
      toast.error('API key is required')
      return
    }

    await saveConfigMutation.mutateAsync({
      apiKey: apiKey.trim(),
      ...(showBaseUrl && baseUrl.trim() && { baseUrl: baseUrl.trim() })
    })
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5" />
              <span>{title}</span>
            </CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>
          
          {statusLoading ? (
            <Spinner className="h-4 w-4" />
          ) : (
            <div className="flex items-center space-x-2">
              {providerStatus?.configured && (
                <Badge variant={providerStatus.available ? "default" : "destructive"}>
                  {providerStatus.available ? (
                    <CheckCircle className="h-3 w-3 mr-1" />
                  ) : (
                    <XCircle className="h-3 w-3 mr-1" />
                  )}
                  {providerStatus.available ? "Connected" : "Error"}
                </Badge>
              )}
              
              {!providerStatus?.configured && (
                <Badge variant="secondary">Not Configured</Badge>
              )}
            </div>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* API Key Input */}
        <div className="space-y-2">
          <label className="text-sm font-medium">{apiKeyLabel}</label>
          <div className="relative">
            <Input
              type={showApiKey ? "text" : "password"}
              placeholder={`Enter your ${title} API key`}
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              className="pr-10"
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
              onClick={() => setShowApiKey(!showApiKey)}
            >
              {showApiKey ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        {/* Base URL Input (optional) */}
        {showBaseUrl && (
          <div className="space-y-2">
            <label className="text-sm font-medium">{baseUrlLabel}</label>
            <Input
              type="url"
              placeholder={`Custom ${title} endpoint (optional)`}
              value={baseUrl}
              onChange={(e) => setBaseUrl(e.target.value)}
            />
            <p className="text-xs text-muted-foreground">
              Leave empty to use the default {title} endpoint
            </p>
          </div>
        )}

        {/* Provider Status Details */}
        {providerStatus && (
          <div className="rounded-lg bg-muted/50 p-3 space-y-2">
            <h4 className="text-sm font-medium">Status Details</h4>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div>
                <span className="text-muted-foreground">Configured:</span>
                <span className="ml-1 font-medium">
                  {providerStatus.configured ? "Yes" : "No"}
                </span>
              </div>
              <div>
                <span className="text-muted-foreground">Available:</span>
                <span className="ml-1 font-medium">
                  {providerStatus.available ? "Yes" : "No"}
                </span>
              </div>
              {providerStatus.responseTime && (
                <div>
                  <span className="text-muted-foreground">Response Time:</span>
                  <span className="ml-1 font-medium">
                    {providerStatus.responseTime}ms
                  </span>
                </div>
              )}
              {providerStatus.lastError && (
                <div className="col-span-2">
                  <span className="text-muted-foreground">Last Error:</span>
                  <span className="ml-1 font-medium text-destructive">
                    {providerStatus.lastError.message}
                  </span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex space-x-2">
          <Button
            onClick={handleTest}
            disabled={!apiKey.trim() || isTesting}
            variant="outline"
            className="flex-1"
          >
            {isTesting ? (
              <Spinner className="h-4 w-4 mr-2" />
            ) : (
              <TestTube className="h-4 w-4 mr-2" />
            )}
            Test Connection
          </Button>
          
          <Button
            onClick={handleSave}
            disabled={!apiKey.trim() || saveConfigMutation.isPending}
            className="flex-1"
          >
            {saveConfigMutation.isPending ? (
              <Spinner className="h-4 w-4 mr-2" />
            ) : (
              <CheckCircle className="h-4 w-4 mr-2" />
            )}
            Save Config
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
