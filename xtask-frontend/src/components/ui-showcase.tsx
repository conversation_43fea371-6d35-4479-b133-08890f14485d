"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Spinner } from "@/components/ui/spinner"
import { ThemeToggle } from "@/components/ui/theme-toggle"
import { toast } from "sonner"

export function UIShowcase() {
  const [progress, setProgress] = useState(33)

  return (
    <div className="container mx-auto p-8 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-white mb-2">XTask UI Components Showcase</h1>
        <p className="text-dark-text-muted">All shadcn/ui components with XTask dark theme</p>
      </div>

      {/* Buttons */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold text-white mb-4">Buttons</h2>
        <div className="flex flex-wrap gap-4">
          <Button variant="primary">Primary</Button>
          <Button variant="secondary">Secondary</Button>
          <Button variant="outline">Outline</Button>
          <Button variant="ghost">Ghost</Button>
          <Button variant="danger">Danger</Button>
          <Button loading>Loading</Button>
        </div>
      </Card>

      {/* Dialog */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold text-white mb-4">Dialog (Modal)</h2>
        <Dialog>
          <DialogTrigger asChild>
            <Button>Open Dialog</Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Are you absolutely sure?</DialogTitle>
              <DialogDescription>
                This action cannot be undone. This will permanently delete your account
                and remove your data from our servers.
              </DialogDescription>
            </DialogHeader>
          </DialogContent>
        </Dialog>
      </Card>

      {/* Avatar */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold text-white mb-4">Avatar</h2>
        <div className="flex gap-4 items-center">
          <Avatar>
            <AvatarImage src="https://github.com/shadcn.png" alt="@shadcn" />
            <AvatarFallback>CN</AvatarFallback>
          </Avatar>
          <Avatar>
            <AvatarFallback>JD</AvatarFallback>
          </Avatar>
        </div>
      </Card>

      {/* Dropdown Menu */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold text-white mb-4">Dropdown Menu</h2>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline">Open Menu</Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel>My Account</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem>Profile</DropdownMenuItem>
            <DropdownMenuItem>Billing</DropdownMenuItem>
            <DropdownMenuItem>Team</DropdownMenuItem>
            <DropdownMenuItem>Subscription</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </Card>

      {/* Tabs */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold text-white mb-4">Tabs</h2>
        <Tabs defaultValue="account" className="w-[400px]">
          <TabsList>
            <TabsTrigger value="account">Account</TabsTrigger>
            <TabsTrigger value="password">Password</TabsTrigger>
          </TabsList>
          <TabsContent value="account">
            <p className="text-dark-text-muted">Make changes to your account here.</p>
          </TabsContent>
          <TabsContent value="password">
            <p className="text-dark-text-muted">Change your password here.</p>
          </TabsContent>
        </Tabs>
      </Card>

      {/* Progress */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold text-white mb-4">Progress</h2>
        <div className="space-y-4">
          <Progress value={progress} className="w-[60%]" />
          <div className="flex gap-2">
            <Button onClick={() => setProgress(Math.max(0, progress - 10))}>-10</Button>
            <Button onClick={() => setProgress(Math.min(100, progress + 10))}>+10</Button>
          </div>
        </div>
      </Card>

      {/* Spinner */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold text-white mb-4">Loading Spinner</h2>
        <div className="flex gap-4 items-center">
          <Spinner size="sm" />
          <Spinner size="md" />
          <Spinner size="lg" />
          <Spinner variant="white" />
        </div>
      </Card>

      {/* Toast */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold text-white mb-4">Toast Notifications</h2>
        <div className="flex gap-4">
          <Button onClick={() => toast.success("Success message!")}>Success Toast</Button>
          <Button onClick={() => toast.error("Error message!")}>Error Toast</Button>
          <Button onClick={() => toast.info("Info message!")}>Info Toast</Button>
        </div>
      </Card>

      {/* Theme Toggle */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold text-white mb-4">Theme Toggle</h2>
        <ThemeToggle />
      </Card>

      {/* Input */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold text-white mb-4">Input</h2>
        <div className="space-y-4 max-w-md">
          <Input placeholder="Enter your email" />
          <Input label="Username" placeholder="Enter username" />
          <Input label="Password" type="password" placeholder="Enter password" />
        </div>
      </Card>

      {/* Badge */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold text-white mb-4">Badge</h2>
        <div className="flex gap-2">
          <Badge>Default</Badge>
          <Badge>Secondary</Badge>
          <Badge>Outline</Badge>
        </div>
      </Card>
    </div>
  )
}
