{"name": "xtask-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "tsx server.ts", "build": "next build", "start": "NODE_ENV=production tsx server.ts", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:migrate:deploy": "prisma migrate deploy", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "analyze": "cross-env ANALYZE=true next build", "clean": "rimraf .next out dist build", "validate-env": "tsx scripts/validate-env.ts"}, "dependencies": {"@headlessui/react": "2.2.4", "@heroicons/react": "2.2.0", "@hookform/resolvers": "5.1.1", "@prisma/client": "6.9.0", "@radix-ui/react-label": "2.1.7", "@tanstack/react-query": "5.80.7", "@types/bcryptjs": "3.0.0", "@types/jsonwebtoken": "9.0.9", "@uploadthing/react": "7.3.1", "axios": "1.10.0", "bcryptjs": "3.0.2", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "date-fns": "4.1.0", "express": "4.19.0", "framer-motion": "12.18.1", "jsonwebtoken": "9.0.2", "lucide-react": "0.515.0", "next": "15.3.3", "next-auth": "4.24.0", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "7.57.0", "sonner": "1.5.0", "tailwind-merge": "3.3.1", "uploadthing": "7.7.2", "zod": "3.25.64", "zustand": "5.0.5"}, "devDependencies": {"@eslint/eslintrc": "3.3.1", "@tailwindcss/forms": "0.5.10", "@tailwindcss/postcss": "4.1.10", "@tailwindcss/typography": "0.5.16", "@types/node": "20.19.0", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "eslint": "9.29.0", "eslint-config-next": "15.3.3", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "0.6.12", "prisma": "6.9.0", "tailwindcss": "4.1.10", "tsx": "4.19.2", "typescript": "5.8.4"}, "packageManager": "bun@1.2.15"}