{"name": "xtask-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "tsx server.ts", "build": "next build", "start": "NODE_ENV=production tsx server.ts", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:migrate:deploy": "prisma migrate deploy", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "analyze": "cross-env ANALYZE=true next build", "clean": "rimraf .next out dist build", "validate-env": "tsx scripts/validate-env.ts"}, "dependencies": {"@headlessui/react": "^1.7.19", "@heroicons/react": "^2.1.5", "@hookform/resolvers": "^3.6.0", "@prisma/client": "^5.18.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-tabs": "^1.1.1", "@tanstack/react-query": "^5.51.23", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.6", "@uploadthing/react": "^6.6.0", "axios": "^1.7.7", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^3.6.0", "express": "^4.19.0", "cors": "^2.8.5", "helmet": "^8.0.0", "compression": "^1.7.4", "morgan": "^1.10.0", "framer-motion": "^11.3.31", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.395.0", "next": "15.3.3", "next-auth": "^4.24.0", "next-themes": "^0.4.3", "openai": "^4.57.0", "@google/genai": "^1.5.0", "twitter-api-v2": "^1.18.2", "mime-types": "^2.1.35", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.52.2", "sonner": "^1.5.0", "tailwind-merge": "^2.4.0", "uploadthing": "^6.6.0", "uuid": "^11.0.3", "zod": "^3.23.8", "zustand": "^4.5.5"}, "devDependencies": {"@eslint/eslintrc": "^3.1.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/postcss": "^4.0.0", "@tailwindcss/typography": "^0.5.15", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/express": "^4.17.21", "@types/mime-types": "^2.1.4", "@types/node": "^22.5.5", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/uuid": "^10.0.0", "cross-env": "^7.0.3", "eslint": "^9.10.0", "eslint-config-next": "15.3.3", "jest": "^29.7.0", "playwright": "^1.47.2", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.6", "prisma": "^5.18.0", "rimraf": "^6.0.1", "tailwindcss": "^3.4.10", "tsx": "^4.19.1", "typescript": "^5.5.4"}, "packageManager": "bun@1.2.15"}