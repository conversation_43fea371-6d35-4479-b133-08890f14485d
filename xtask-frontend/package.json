{"name": "xtask-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.9.0", "@radix-ui/react-label": "^2.1.7", "@tanstack/react-query": "^5.80.7", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.9", "@uploadthing/react": "^7.3.1", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "express": "^4.19.0", "framer-motion": "^12.18.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.515.0", "next": "15.3.3", "next-auth": "^4.24.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0", "sonner": "^1.5.0", "tailwind-merge": "^3.3.1", "uploadthing": "^7.7.2", "zod": "^3.25.64", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.12", "prisma": "^6.9.0", "tailwindcss": "^4", "typescript": "^5"}, "packageManager": "bun@1.0.0"}