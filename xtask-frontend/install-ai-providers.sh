#!/bin/bash

echo "Installing AI Provider dependencies..."

# Core AI provider libraries
echo "Installing OpenAI SDK..."
bun add openai@4.24.0

echo "Installing Google GenAI SDK (latest)..."
bun add @google/genai

echo "Installing additional dependencies..."
bun add uuid
bun add @types/uuid

echo "All AI provider dependencies installed successfully!"
echo ""
echo "Next steps:"
echo "1. Ensure your .env file has the required API keys:"
echo "   - OPENAI_API_KEY=your_openai_key"
echo "   - GEMINI_API_KEY=your_gemini_key"
echo "   - MISTRAL_API_KEY=your_mistral_key (optional)"
echo "   - GROQ_API_KEY=your_groq_key (optional)"
echo "   - HUGGINGFACE_API_KEY=your_hf_key (optional)"
echo "   - OPENROUTER_API_KEY=your_openrouter_key (optional)"
echo ""
echo "2. Test the AI providers:"
echo "   - Start server: bun run dev"
echo "   - Check status: curl http://localhost:3030/api/ai/status"
echo "   - Test generation: POST to http://localhost:3030/api/ai/generate"
echo ""
echo "3. Create agents and test content generation in the UI"
