-- Enable pgvector extension for vector similarity search
CREATE EXTENSION IF NOT EXISTS vector;

-- Create vector index for agent memory embeddings
-- Using IVFFlat index for good performance with 1024-dimensional vectors
-- Lists parameter is set to sqrt(total_rows), estimated for moderate dataset size
CREATE INDEX CONCURRENTLY IF NOT EXISTS agent_memories_embedding_cosine_idx 
ON agent_memories USING ivfflat (embedding::vector(1024) vector_cosine_ops) 
WITH (lists = 100);

-- Create additional indexes for efficient filtering during vector search
CREATE INDEX CONCURRENTLY IF NOT EXISTS agent_memories_agent_type_importance_idx 
ON agent_memories (agentId, type, importance DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS agent_memories_embedding_not_null_idx 
ON agent_memories (agentId) WHERE embedding IS NOT NULL;

-- Create GIN index for tag array searches
CREATE INDEX CONCURRENTLY IF NOT EXISTS agent_memories_tags_gin_idx 
ON agent_memories USING gin (tags);

-- Add comment to document the vector dimension
COMMENT ON COLUMN agent_memories.embedding IS 'Vector embedding stored as string, cast to vector(1024) for pgvector operations. Generated using OpenAI text-embedding-3-large with 1024 dimensions.';
