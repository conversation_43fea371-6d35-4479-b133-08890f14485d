// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  // Neon connection pooling configuration
  // pgbouncer=true is handled in the connection string
}

// User model for authentication and profile management
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  avatar    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // OAuth connections
  googleId  String? @unique
  twitterId String? @unique

  // User preferences
  timezone      String  @default("UTC")
  theme         String  @default("dark")
  notifications Bo<PERSON>an @default(true)

  // AI configuration
  defaultModel    String   @default("gpt-4")
  fallbackModel   String   @default("gemini-pro")
  maxTokensPerDay Int      @default(10000)
  tokensUsedToday Int      @default(0)
  lastTokenReset  DateTime @default(now())

  // Relationships
  agents          Agent[]
  twitterAccounts TwitterAccount[]
  scheduledTweets ScheduledTweet[]
  mediaFiles      MediaFile[]

  // NextAuth.js relationships
  accounts Account[]
  sessions Session[]

  // JWT session management
  jwtSessions JWTSession[]

  @@map("users")
}

// AI Agent model for persona-driven content generation
model Agent {
  id          String   @id @default(cuid())
  name        String
  description String?
  avatar      String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Persona configuration
  personality  Json // JSON object with personality traits
  writingStyle Json // JSON object with writing preferences
  topics       String[] // Array of topics the agent focuses on
  tone         String   @default("professional")
  creativity   Float    @default(0.7) // 0-1 scale

  // AI model preferences
  preferredModel String @default("gpt-4")
  temperature    Float  @default(0.7)
  maxTokens      Int    @default(1000)

  // Scheduling preferences
  postingFrequency String @default("daily") // daily, weekly, custom
  preferredTimes   Json // Array of preferred posting times
  timezone         String @default("UTC")

  // Performance tracking
  totalTweets     Int   @default(0)
  totalEngagement Int   @default(0)
  avgEngagement   Float @default(0.0)

  // Relationships
  userId          String
  user            User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  memories        AgentMemory[]
  scheduledTweets ScheduledTweet[]

  @@map("agents")
}

// Twitter/X account connections
model TwitterAccount {
  id          String   @id @default(cuid())
  twitterId   String   @unique
  username    String   @unique
  displayName String
  avatar      String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // OAuth tokens (encrypted)
  accessToken    String
  refreshToken   String?
  tokenExpiresAt DateTime?

  // Account metrics
  followersCount Int      @default(0)
  followingCount Int      @default(0)
  tweetsCount    Int      @default(0)
  lastSyncAt     DateTime @default(now())

  // Relationships
  userId          String
  user            User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  scheduledTweets ScheduledTweet[]

  @@map("twitter_accounts")
}

// Scheduled tweet content and metadata
model ScheduledTweet {
  id          String    @id @default(cuid())
  content     String
  mediaUrls   String[] // Array of media file URLs
  scheduledAt DateTime
  publishedAt DateTime?
  status      String    @default("scheduled") // scheduled, published, failed, cancelled
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // AI generation metadata
  isAIGenerated Boolean @default(false)
  prompt        String?
  model         String?
  temperature   Float?

  // Thread support
  isThread      Boolean          @default(false)
  threadOrder   Int?
  parentTweetId String?
  parentTweet   ScheduledTweet?  @relation("ThreadReplies", fields: [parentTweetId], references: [id])
  threadReplies ScheduledTweet[] @relation("ThreadReplies")

  // Performance tracking
  impressions  Int   @default(0)
  likes        Int   @default(0)
  retweets     Int   @default(0)
  repliesCount Int   @default(0)
  engagement   Float @default(0.0)

  // Relationships
  userId           String
  user             User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  agentId          String?
  agent            Agent?         @relation(fields: [agentId], references: [id], onDelete: SetNull)
  twitterAccountId String
  twitterAccount   TwitterAccount @relation(fields: [twitterAccountId], references: [id], onDelete: Cascade)

  @@map("scheduled_tweets")
}

// Agent memory for context and learning (with vector embeddings)
model AgentMemory {
  id         String   @id @default(cuid())
  content    String // The actual memory content
  context    String? // Additional context about the memory
  type       String   @default("interaction") // interaction, learning, preference, etc.
  importance Float    @default(0.5) // 0-1 scale for memory importance
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Vector embedding for semantic search (pgvector support)
  embedding String? // Store as string, cast to vector in SQL queries

  // Metadata for embeddings
  embeddingModel String? // Model used to generate embedding
  embeddingDim   Int? // Dimension of the embedding vector

  // Memory associations
  tags      String[] // Tags for categorization
  relatedTo String? // ID of related entity (tweet, user, etc.)

  // Relationships
  agentId String
  agent   Agent  @relation(fields: [agentId], references: [id], onDelete: Cascade)

  @@index([agentId, type])
  @@index([importance])
  @@map("agent_memories")
}

// Media file storage and metadata
model MediaFile {
  id           String   @id @default(cuid())
  filename     String
  originalName String
  mimeType     String
  size         Int // File size in bytes
  url          String // UploadThing URL
  key          String   @unique // UploadThing file key for deletion/management
  thumbnailUrl String? // Thumbnail URL for images/videos
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Media metadata
  width    Int? // For images/videos
  height   Int? // For images/videos
  duration Float? // For videos/audio in seconds
  altText  String? // Accessibility description

  // Processing status
  status      String    @default("uploaded") // uploaded, processing, ready, failed
  processedAt DateTime?

  // Usage tracking
  usageCount Int       @default(0)
  lastUsedAt DateTime?

  // Relationships
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, mimeType])
  @@index([status])
  @@map("media_files")
}

// NextAuth.js required models
model Account {
  id                String  @id @default(cuid())
  userId            String  @map("user_id")
  type              String
  provider          String
  providerAccountId String  @map("provider_account_id")
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique @map("session_token")
  userId       String   @map("user_id")
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String
  expires    DateTime

  @@unique([identifier, token])
  @@map("verificationtokens")
}

// JWT Session management for custom authentication
model JWTSession {
  id         String   @id @default(cuid())
  jti        String   @unique // JWT ID for token revocation
  userId     String   @map("user_id")
  isRevoked  Boolean  @default(false)
  createdAt  DateTime @default(now())
  expiresAt  DateTime
  lastUsedAt DateTime @default(now())

  // Session metadata
  ipAddress  String?
  userAgent  String?
  deviceInfo Json? // Device fingerprint and info

  // Security tracking
  loginMethod  String? // oauth, password, etc.
  revokedAt    DateTime?
  revokedBy    String? // admin, user, system
  revokeReason String? // logout, security, expired, etc.

  // Relationships
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, isRevoked])
  @@index([jti])
  @@index([expiresAt])
  @@map("jwt_sessions")
}
